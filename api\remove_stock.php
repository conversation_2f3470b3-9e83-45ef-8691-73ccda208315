<?php
// 首先关闭错误显示，防止错误输出影响JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 必须在输出前启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once '../includes/functions.php';

// 增加会话调试信息
error_log("删除股票API: SESSION=" . json_encode($_SESSION ?? []));
error_log("删除股票API: Cookie信息=" . json_encode($_COOKIE));
error_log("删除股票API: user_id=" . ($_SESSION['user_id'] ?? '未设置'));

// 检查用户ID - 如果会话中没有，使用测试用户ID
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // 使用测试用户ID
    error_log("删除股票: 使用测试用户ID");
}

try {
    // 获取并验证POST数据
    $postData = file_get_contents('php://input');
    
    if (empty($postData)) {
        throw new Exception('没有收到任何数据');
    }
    
    error_log("接收到的原始数据: " . $postData);
    
    // 解析JSON
    $data = json_decode($postData, true);
    
    // 检查JSON解析是否成功
    if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("JSON解析失败: " . json_last_error_msg());
    }
    
    // 验证必要字段
    if (!isset($data['code']) || empty($data['code'])) {
        throw new Exception('股票代码不能为空');
    }
    
    // 净化并标准化输入
    $userId = (int)$_SESSION['user_id'];
    $stockCode = sanitizeInput($data['code']);
    
    // 连接数据库
    $db = connectDB();
    if (!$db) {
        throw new Exception('数据库连接失败');
    }
    
    // 删除股票
    $deleteSql = "DELETE FROM user_stocks WHERE user_id = ? AND stock_id = ?";
    error_log("准备执行SQL: " . $deleteSql . " 参数: user_id=$userId, stock_id=$stockCode");
    
    $stmt = $db->prepare($deleteSql);
    
    if (!$stmt) {
        throw new Exception('准备删除语句失败: ' . $db->error);
    }
    
    $stmt->bind_param('is', $userId, $stockCode);
    
    // 执行删除并检查结果
    $result = $stmt->execute();
    
    if ($result) {
        // 检查是否有行被删除
        if ($stmt->affected_rows > 0) {
            error_log("股票删除成功: user_id=$userId, stock_id=$stockCode");
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => '股票删除成功'
            ]);
        } else {
            // 没有找到匹配的股票
            error_log("未找到要删除的股票: user_id=$userId, stock_id=$stockCode");
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => '未找到该股票'
            ]);
        }
    } else {
        throw new Exception('数据库删除失败: ' . $stmt->error);
    }
    
    $stmt->close();
    
} catch (Exception $e) {
    error_log("删除股票错误: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

exit;
?> 