<?php
/**
 * 股票名称缓存直接更新脚本
 */

// 设置错误报告
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 引入函数库
require_once 'includes/functions.php';

echo "<h1>开始更新股票名称列表</h1>";
echo "<pre>";

// 从licence.json获取随机licence
$licences = getLicences();
if (empty($licences)) {
    echo "无法获取API许可证\n";
    exit(1);
}

// 随机获取一个licence
$randomLicence = $licences[array_rand($licences)];
echo "使用许可证: " . substr($randomLicence, 0, 8) . "...\n";

// 股票名称API
$STOCK_NAME_API = "http://localhost:8888/api/public/stock_info_a_code_name";
$nameApiUrl = $STOCK_NAME_API;

echo "请求股票名称API: " . $nameApiUrl . "\n";

// 初始化CURL获取名称列表
$nameCh = curl_init();
curl_setopt($nameCh, CURLOPT_URL, $nameApiUrl);
curl_setopt($nameCh, CURLOPT_RETURNTRANSFER, true);
curl_setopt($nameCh, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($nameCh, CURLOPT_TIMEOUT, 30); // 适当延长超时时间
curl_setopt($nameCh, CURLOPT_HTTPHEADER, [
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
]);

// 执行请求
$nameResponse = curl_exec($nameCh);
$nameHttpCode = curl_getinfo($nameCh, CURLINFO_HTTP_CODE);
$curlError = curl_error($nameCh);
curl_close($nameCh);

// 检查请求是否成功
if ($nameHttpCode != 200) {
    echo "API请求失败 (HTTP $nameHttpCode): " . ($curlError ? $curlError : "未知错误") . "\n";
    exit(1);
}

if (empty($nameResponse)) {
    echo "API返回空响应\n";
    exit(1);
}

// 解析JSON
$nameData = json_decode($nameResponse, true);
if ($nameData === null && json_last_error() !== JSON_ERROR_NONE) {
    echo "JSON解析失败: " . json_last_error_msg() . "\n";
    exit(1);
}

// 显示部分数据
echo "API返回数据示例:\n";
for ($i = 0; $i < min(5, count($nameData)); $i++) {
    echo json_encode($nameData[$i], JSON_UNESCAPED_UNICODE) . "\n";
}
echo "...\n";

// 确保缓存目录存在
if (!file_exists("cache")) {
    mkdir("cache", 0755, true);
    echo "创建缓存目录: cache\n";
}

// 保存原始数据到缓存
$cacheFile = "cache/stock_names.json";
file_put_contents($cacheFile, $nameResponse);

// 统计获取的股票数量
$stockCount = is_array($nameData) ? count($nameData) : 0;
echo "股票名称列表已更新，共 {$stockCount} 条记录已缓存到: {$cacheFile}\n";

// 创建索引文件，便于快速查找
$indexData = [];
if (is_array($nameData)) {
    foreach ($nameData as $stock) {
        if (isset($stock['code']) && isset($stock['name'])) {
            $indexData[$stock['code']] = $stock['name'];
        }
    }
    
    // 保存索引数据
    $indexFile = "cache/stock_names_index.json";
    file_put_contents($indexFile, json_encode($indexData, JSON_UNESCAPED_UNICODE));
    echo "股票名称索引已创建: {$indexFile}\n";
    
    // 显示索引示例
    echo "索引示例:\n";
    $count = 0;
    foreach ($indexData as $code => $name) {
        echo "{$code} => {$name}\n";
        $count++;
        if ($count >= 5) break;
    }
}

echo "</pre>";
echo "<h2>更新成功，共 {$stockCount} 条记录</h2>";
echo "<p><a href='dashboard.php'>返回个人中心</a></p>";
?>