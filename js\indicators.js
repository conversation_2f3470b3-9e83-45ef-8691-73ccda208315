/**
 * 技术指标计算
 */

const Indicators = {
    /**

     */
    calculateKDJ: function(histData, periodN = 9, periodM1 = 3, periodM2 = 3) {
        if (!histData || !Array.isArray(histData) || histData.length === 0) {
            // 注释log输出 console.error('历史数据无效');
            return { k: 50, d: 50, j: 50, signal: 'normal' };
        }
        
        // 打印第一条数据以检查格式
        // 注释log输出 console.log('KDJ计算第一条历史数据:', histData[0]);
        
        // 确保历史数据按日期升序排序
        const sortedData = [...histData].sort((a, b) => {
            return new Date(a.日期) - new Date(b.日期);
        });
        
        // 至少需要periodN天数据
        if (sortedData.length < periodN) {
            console.warn(`KDJ计算需要至少${periodN}天数据，当前只有${sortedData.length}天`);
            return { k: 50, d: 50, j: 50, signal: 'normal' };
        }
        
        // 计算RSV值
        const rsv = [];
        for (let i = periodN - 1; i < sortedData.length; i++) {
            const period = sortedData.slice(i - periodN + 1, i + 1);
            const highestPrice = Math.max(...period.map(item => item.最高));
            const lowestPrice = Math.min(...period.map(item => item.最低));
            const closePrice = sortedData[i].收盘;
            
            // 计算RSV: (收盘价 - 最低价) / (最高价 - 最低价) * 100
            let rsvValue = 0;
            if (highestPrice !== lowestPrice) {
                rsvValue = ((closePrice - lowestPrice) / (highestPrice - lowestPrice)) * 100;
            }
            rsv.push(rsvValue);
        }
        
        // 计算K、D、J值
        const k = [];
        const d = [];
        const j = [];
        
        // 初始化第一天的K和D
        k[0] = 50;
        d[0] = 50;
        
        // 计算每天的KDJ值
        for (let i = 0; i < rsv.length; i++) {
            if (i === 0) {
                k[i] = (1 / periodM1) * rsv[i] + ((periodM1 - 1) / periodM1) * 50;
                d[i] = (1 / periodM2) * k[i] + ((periodM2 - 1) / periodM2) * 50;
            } else {
                k[i] = (1 / periodM1) * rsv[i] + ((periodM1 - 1) / periodM1) * k[i - 1];
                d[i] = (1 / periodM2) * k[i] + ((periodM2 - 1) / periodM2) * d[i - 1];
            }
            j[i] = 3 * k[i] - 2 * d[i];
        }
        
        // 获取最新的KDJ值
        const lastK = parseFloat(k[k.length - 1].toFixed(2));
        const lastD = parseFloat(d[d.length - 1].toFixed(2));
        const lastJ = parseFloat(j[j.length - 1].toFixed(2));
        
        // 如果有至少两天的数据，判断金叉死叉
        let signal = 'normal';
        if (k.length >= 2 && d.length >= 2) {
            const prevK = k[k.length - 2];
            const prevD = d[d.length - 2];
            
            // 金叉：K线从下向上穿过D线
            if (prevK < prevD && lastK > lastD) {
                signal = 'golden';
            }
            // 死叉：K线从上向下穿过D线
            else if (prevK > prevD && lastK < lastD) {
                signal = 'death';
            }
        }
        
        return {
            k: lastK,
            d: lastD,
            j: lastJ,
            signal: signal
        };
    },
    
    /**
     * 获取KDJ信号描述
     * @param {Object} kdj - KDJ指标值及信号
     * @returns {Object} 信号描述和样式
     */
    getKDJSignal: function(kdj) {
        // 注释log输出 console.log('获取KDJ信号，输入数据:', kdj);
        
        if (!kdj) return { text: '', style: '' };
        
        let text = '';
        let style = '';
        
        // 根据信号类型设置文本和样式
        switch (kdj.signal) {
            case 'golden':
                text = 'KDJ金叉';
                style = 'color: #ff9900; font-weight: bold; font-size: 12px; margin-top: 5px;';
                break;
            case 'death':
                text = 'KDJ死叉';
                style = 'color: #ff3300; font-weight: bold; font-size: 12px; margin-top: 5px;';
                break;
            default:
                text = 'KDJ正常';
                style = 'color: #0066cc; font-weight: bold; font-size: 12px; margin-top: 5px;';
                break;
        }
        
        // 不再在这里附加K和D值
        console.log('生成的KDJ信号文本:', text);
        
        return { text, style };
    },
    
    /**

     */
    calculateMACD: function(histData, shortPeriod = 12, longPeriod = 26, signalPeriod = 9) {
        if (!histData || !Array.isArray(histData) || histData.length === 0) {
            console.error('历史数据无效');
            return { dif: 0, dea: 0, macd: 0, signal: 'normal' };
        }
        
        // 打印第一条数据以检查格式
      //注释log输出  console.log('MACD计算第一条历史数据:', histData[0]);
        
        // 确保历史数据按日期升序排序
        const sortedData = [...histData].sort((a, b) => {
            return new Date(a.日期) - new Date(b.日期);
        });
        
        // 至少需要longPeriod + signalPeriod天数据
        const minPeriod = longPeriod + signalPeriod;
        if (sortedData.length < minPeriod) {
            console.warn(`MACD计算需要至少${minPeriod}天数据，当前只有${sortedData.length}天`);
            return { dif: 0, dea: 0, macd: 0, signal: 'normal' };
        }
        
        // 提取收盘价数组
        const closePrices = sortedData.map(item => item.收盘);
        
        // 计算短期EMA（12日）
        const shortEMA = this.calculateEMA(closePrices, shortPeriod);
        
        // 计算长期EMA（26日）
        const longEMA = this.calculateEMA(closePrices, longPeriod);
        
        // 计算DIF：短期EMA - 长期EMA
        const dif = [];
        for (let i = 0; i < closePrices.length; i++) {
            if (i < longPeriod - 1) {
                // 长期EMA未计算出来之前，DIF为0
                dif.push(0);
            } else {
                dif.push(shortEMA[i] - longEMA[i]);
            }
        }
        
        // 计算DEA：DIF的9日EMA
        const dea = this.calculateEMA(dif, signalPeriod);
        
        // 计算MACD柱状值：(DIF - DEA) * 2
        const macd = [];
        for (let i = 0; i < dif.length; i++) {
            if (i < longPeriod + signalPeriod - 2) {
                // DEA未计算出来之前，MACD为0
                macd.push(0);
            } else {
                macd.push((dif[i] - dea[i]) * 2);
            }
        }
        
        // 获取最新的MACD值
        const lastDIF = parseFloat(dif[dif.length - 1].toFixed(4));
        const lastDEA = parseFloat(dea[dea.length - 1].toFixed(4));
        const lastMACD = parseFloat(macd[macd.length - 1].toFixed(4));
        
        // 如果有足够的数据，判断金叉死叉
        let signal = 'normal';
        if (dif.length >= longPeriod + signalPeriod && dea.length >= longPeriod + signalPeriod) {
            const prevDIF = dif[dif.length - 2];
            const prevDEA = dea[dea.length - 2];
            
            // 金叉：DIF从下向上穿过DEA
            if (prevDIF < prevDEA && lastDIF > lastDEA) {
                signal = 'golden';
            }
            // 死叉：DIF从上向下穿过DEA
            else if (prevDIF > prevDEA && lastDIF < lastDEA) {
                signal = 'death';
            }
        }
        
        return {
            dif: lastDIF,
            dea: lastDEA,
            macd: lastMACD,
            signal: signal
        };
    },
    
    /**
     * 计算指数移动平均线(EMA)
     * @param {Array} data - 数据数组
     * @param {Number} period - 周期
     * @returns {Array} EMA值数组
     */
    calculateEMA: function(data, period) {
        const ema = [];
        const k = 2 / (period + 1);
        
        // 第一个EMA等于第一个数据值
        ema.push(data[0]);
        
        // 计算后续的EMA: 今日EMA = 今日收盘价 * k + 昨日EMA * (1 - k)
        for (let i = 1; i < data.length; i++) {
            ema.push(data[i] * k + ema[i - 1] * (1 - k));
        }
        
        return ema;
    },
    
    /**

     */
    getMACDSignal: function(macd) {
        console.log('获取MACD信号，输入数据:', macd);
        
        if (!macd) return { text: '', style: '' };
        
        let text = '';
        let style = '';
        
        // 根据信号类型设置文本和样式
        switch (macd.signal) {
            case 'golden':
                text = 'MACD金叉';
                style = 'color: #ff9900; font-weight: bold; font-size: 12px; margin-top: 5px;';
                break;
            case 'death':
                text = 'MACD死叉';
                style = 'color: #ff3300; font-weight: bold; font-size: 12px; margin-top: 5px;';
                break;
            default:
                text = 'MACD正常';
                style = 'color: #0066cc; font-weight: bold; font-size: 12px; margin-top: 5px;';
                break;
        }
        
        console.log('生成的MACD信号文本:', text);
        
        return { text, style };
    }
};

// 导出到全局对象
window.Indicators = Indicators; 
