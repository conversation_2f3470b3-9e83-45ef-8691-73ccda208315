<?php
// 先加载配置文件，它会处理会话启动
require_once 'includes/config.php';
require_once 'includes/functions.php';

// 检查是否已登录
if (!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => '请先登录'
    ]);
    exit;
}

// 获取请求主体
$request_body = file_get_contents('php://input');
$data = json_decode($request_body, true);

// 验证API密钥
if (!isset($data['api_key'])) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'API密钥不能为空'
    ]);
    exit;
}

$api_key = trim($data['api_key']);
$user_id = $_SESSION['user_id'];

try {
    // 连接数据库
    $db = connectDB();
    
    // 更新API密钥
    $stmt = $db->prepare("UPDATE users SET api_key = ? WHERE id = ?");
    $stmt->bind_param("si", $api_key, $user_id);
    $stmt->execute();
    
    if ($stmt->affected_rows >= 0) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'message' => 'API密钥更新成功'
        ]);
    } else {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'API密钥更新失败'
        ]);
    }
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => '系统错误：' . $e->getMessage()
    ]);
}
?> 