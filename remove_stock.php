<?php
// 先加载配置文件，它会处理会话启动
require_once 'includes/config.php';
require_once 'includes/functions.php';

// 增加会话调试信息
error_log("删除股票: SESSION=" . json_encode($_SESSION ?? []));
error_log("删除股票: Cookie信息=" . json_encode($_COOKIE));
error_log("删除股票: session_id=" . session_id());

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['error' => '请先登录']);
    exit;
}

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['code'])) {
    http_response_code(400);
    echo json_encode(['error' => '缺少股票代码']);
    exit;
}

$code = $data['code'];
$username = $_SESSION['username'];

try {
    // 从数据库中删除股票
    $stmt = $pdo->prepare("DELETE FROM user_stocks WHERE username = ? AND stock_code = ?");
    $stmt->execute([$username, $code]);
    
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => '删除股票失败：' . $e->getMessage()]);
}
?> 