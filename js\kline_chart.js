/**
 * K线图功能 - 基于ECharts实现
 */

// 轻量级日志处理
if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
    // 保存原始日志方法引用
    const originalLog = console.log;
    // 替换为空函数但不阻碍功能
    console.log = console.info = console.warn = console.error = console.debug = function() {
        // 静默处理但不影响功能
    };
}

const KLineChart = {
    // 存储图表实例的引用
    chartInstance: null,
    
    /**
     * 初始化K线图实例
     * @param {String} containerId - 容器元素ID
     * @returns {Object} - ECharts实例
     */
    initChart: function(containerId) {
        // 确保容器元素存在
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('K线图容器元素不存在:', containerId);
            return null;
        }
        
        // 如果已存在图表实例，先销毁
        if (this.chartInstance) {
            this.chartInstance.dispose();
        }
        
        // 初始化ECharts实例
        const chart = echarts.init(container);
        this.chartInstance = chart; // 保存实例引用
        
        // 设置加载中状态
        chart.showLoading({
            text: '数据加载中...',
            color: '#4a90e2',
            textColor: '#333',
            maskColor: 'rgba(255, 255, 255, 0.8)',
            zlevel: 0
        });
        
        return chart;
    },
    
    /**
     * 加载股票K线数据
     * @param {String} stockCode - 股票代码
     * @param {String} period - K线周期，可选值：daily, weekly, monthly
     * @param {Number} days - 获取多少天的数据
     * @returns {Promise<Object>} - K线数据对象
     */
    loadKLineData: async function(stockCode, period = 'daily', days = 90) {
        try {
            // 获取原始股票代码（去掉可能的sh/sz前缀）
            const rawStockCode = stockCode.replace(/^(sh|sz)/, '');
            
            // 构建API请求URL - 使用原始代码，API会自行处理前缀
            const url = `api/stock_proxy.php?symbol=${encodeURIComponent(rawStockCode)}&type=kline&period=${period}&days=${days}`;
            
            console.log('请求K线数据URL:', url, '周期:', period);
            
            // 发起请求
            const response = await fetch(url);
            const data = await response.json();
            
            // 输出调试信息
            console.log(`K线数据响应(${period})`, data);
            
            // 检查是否成功
            if (data.error) {
                throw new Error(data.error);
            }
            
            return data;
        } catch (error) {
            console.error('加载K线数据失败:', error);
            throw error;
        }
    },
    
    /**
     * 加载股票分时图数据
     * @param {String} stockCode - 股票代码
     * @returns {Promise<Object>} - 分时图数据对象
     */
    loadTimelineData: async function(stockCode) {
        try {
            // 确保股票代码格式正确
            const formattedCode = stockCode.startsWith('sh') || stockCode.startsWith('sz') 
                ? stockCode
                : (stockCode.startsWith('6') 
                    ? 'sh' + stockCode 
                    : (stockCode === '000300' ? 'sh000300' : 'sz' + stockCode));
            
            // 构建API请求URL
            const url = `api/stock_proxy.php?symbol=${encodeURIComponent(formattedCode)}&type=timeline`;
            
            console.log('请求分时数据URL:', url);
            
            // 发起请求
            const response = await fetch(url);
            const data = await response.json();
            
            // 检查是否成功
            if (data.error) {
                throw new Error(data.error);
            }
            
            return data;
        } catch (error) {
            console.error('加载分时图数据失败:', error);
            throw error;
        }
    },
    
    /**
     * 渲染K线图
     * @param {Object} chart - ECharts实例
     * @param {Object} klineData - K线数据对象
     * @param {String} stockName - 股票名称
     */
    renderKLineChart: function(chart, klineData, stockName) {
        // 确保数据有效
        if (!chart || !klineData || !klineData.data || !Array.isArray(klineData.data)) {
            console.error('无效的K线数据或图表实例:', klineData);
            if (chart) {
                chart.hideLoading();
                chart.setOption({
                    title: {
                        text: '数据加载失败',
                        left: 'center',
                        top: 'center',
                        textStyle: {
                            color: '#999',
                            fontSize: 16
                        }
                    }
                });
            }
            return;
        }
        
        // 提取时间数据 - 从日期字段中截取日期部分
        const dates = klineData.data.map(item => {
            if (item.time) return item.time;
            // 如果使用新API，日期字段可能是'日期'
            if (item['日期']) {
                // 将ISO日期格式转换为简单的日期格式（yyyy-MM-dd）
                return item['日期'].substring(0, 10);
            }
            return '';
        });
        
        // 提取K线数据，适配新的API响应格式
        const kData = klineData.data.map(item => {
            // 如果使用旧格式
            if (item.open !== undefined) {
                return [item.open, item.close, item.lowest, item.highest];
            }
            // 如果使用新API格式
            else if (item['开盘'] !== undefined) {
                return [item['开盘'], item['收盘'], item['最低'], item['最高']];
            }
            return [0, 0, 0, 0]; // 默认值
        });
        
        // 提取成交量数据
        const volumes = klineData.data.map(item => {
            // 如果使用旧格式
            if (item.volume !== undefined) {
                return item.volume;
            }
            // 如果使用新API格式
            else if (item['成交量'] !== undefined) {
                return item['成交量'];
            }
            return 0; // 默认值
        });
        
        // 计算MA5, MA10, MA20
        const closePrices = klineData.data.map(item => {
            // 如果使用旧格式
            if (item.close !== undefined) {
                return item.close;
            }
            // 如果使用新API格式
            else if (item['收盘'] !== undefined) {
                return item['收盘'];
            }
            return 0; // 默认值
        });
        
        const ma5 = this.calculateMA(5, closePrices);
        const ma10 = this.calculateMA(10, closePrices);
        const ma20 = this.calculateMA(20, closePrices);
        
        // 设置图表选项
        const option = {
            animation: true,
            title: {
                text: `${stockName} - K线图`,
                left: 'center',
                textStyle: {
                    color: '#333'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#333'
                    },
                    lineStyle: {
                        color: '#999',
                        width: 1,
                        type: 'dashed'
                    },
                    crossStyle: {
                        color: '#999',
                        width: 1,
                        type: 'dashed'
                    },
                    // 确保十字光标跟随，这是关键设置
                    animation: false,
                    triggerTooltip: true
                },
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderColor: '#ccc',
                borderWidth: 1,
                textStyle: {
                    color: '#333'
                },
                formatter: function(params) {
                    if (!params || params.length === 0) return '';
                    
                    // 获取当前K线数据
                    const kItem = params[0];
                    if (!kItem || !kItem.axisValue || !kItem.data) return '';
                    
                    const date = kItem.axisValue;
                    const data = kItem.data;
                    
                    // 构建提示内容
                    let tooltipText = `<div style="font-weight:bold;margin-bottom:5px;">${date}</div>`;
                    tooltipText += `<div>开盘: <span style="color:#d14a61;font-weight:bold;">${data[0]}</span></div>`;
                    tooltipText += `<div>收盘: <span style="color:#d14a61;font-weight:bold;">${data[1]}</span></div>`;
                    tooltipText += `<div>最低: <span style="color:#d14a61;">${data[2]}</span></div>`;
                    tooltipText += `<div>最高: <span style="color:#d14a61;">${data[3]}</span></div>`;
                    
                    // 添加MA值
                    params.forEach(param => {
                        if (param.seriesName && param.seriesName.indexOf('MA') > -1 && param.data !== null) {
                            tooltipText += `<div>${param.seriesName}: <span style="color:${param.color};font-weight:bold;">${param.data.toFixed(2)}</span></div>`;
                        }
                    });
                    
                    // 添加成交量
                    const volumeParam = params.find(p => p.seriesName === '成交量');
                    if (volumeParam && volumeParam.data !== null) {
                        const volume = volumeParam.data;
                        tooltipText += `<div>成交量: <span style="color:#4a90e2;font-weight:bold;">${(volume/10000).toFixed(2)}万</span></div>`;
                    }
                    
                    return tooltipText;
                }
            },
            axisPointer: {
                link: {xAxisIndex: 'all'},
                label: {
                    backgroundColor: '#777'
                }
            },
            grid: [
                {
                    left: '3%',
                    right: '3%',
                    top: '8%',
                    height: '60%'
                },
                {
                    left: '3%',
                    right: '3%',
                    top: '73%',
                    height: '20%'
                }
            ],
            xAxis: [
                {
                    type: 'category',
                    data: dates,
                    scale: true,
                    boundaryGap: false,
                    axisLine: {lineStyle: {color: '#8392A5'}},
                    axisTick: {show: false},
                    axisLabel: {
                        formatter: function(value) {
                            return value.substr(5); // 只显示月和日
                        }
                    },
                    splitLine: {show: false},
                    splitNumber: 20,
                    // 确保鼠标悬停时可以显示十字光标
                    axisPointer: {
                        show: true,
                        triggerTooltip: true
                    }
                },
                {
                    type: 'category',
                    gridIndex: 1,
                    data: dates,
                    scale: true,
                    boundaryGap: false,
                    axisLine: {lineStyle: {color: '#8392A5'}},
                    axisTick: {show: false},
                    axisLabel: {show: false},
                    splitLine: {show: false},
                    splitNumber: 20,
                    // 确保鼠标悬停时可以显示十字光标
                    axisPointer: {
                        show: true,
                        triggerTooltip: false
                    }
                }
            ],
            yAxis: [
                {
                    scale: true,
                    splitNumber: 5,
                    axisLine: {lineStyle: {color: '#8392A5'}},
                    axisTick: {show: false},
                    axisLabel: {
                        inside: false,
                        formatter: '{value}\n'
                    },
                    splitLine: {show: true, lineStyle: {color: '#eee'}}
                },
                {
                    scale: true,
                    gridIndex: 1,
                    splitNumber: 3,
                    axisLine: {show: false},
                    axisTick: {show: false},
                    axisLabel: {show: false},
                    splitLine: {show: false}
                }
            ],
            dataZoom: [
                {
                    type: 'inside',
                    xAxisIndex: [0, 1],
                    start: Math.max(0, 100 - (2000 / klineData.data.length)),
                    end: 100,
                    // 添加缩放结束后的事件
                    zoomLock: false
                },
                {
                    show: true,
                    xAxisIndex: [0, 1],
                    type: 'slider',
                    bottom: '0%',
                    height: '5%',
                    start: Math.max(0, 100 - (2000 / klineData.data.length)),
                    end: 100,
                    // 添加缩放结束后的事件
                    zoomLock: false
                }
            ],
            series: [
                {
                    name: 'K线',
                    type: 'candlestick',
                    data: kData,
                    itemStyle: {
                        color: '#ef232a',
                        color0: '#14b143',
                        borderColor: '#ef232a',
                        borderColor0: '#14b143'
                    },
                    markPoint: {
                        label: {
                            normal: {
                                formatter: function(param) {
                                    return param != null ? Math.round(param.value) : '';
                                }
                            }
                        },
                        data: [
                            {
                                name: '最高点',
                                type: 'max',
                                valueDim: 'highest'
                            },
                            {
                                name: '最低点',
                                type: 'min',
                                valueDim: 'lowest'
                            }
                        ],
                        tooltip: {
                            formatter: function(param) {
                                return param.name + '<br>' + (param.data.coord || '');
                            }
                        }
                    }
                },
                {
                    name: 'MA5',
                    type: 'line',
                    data: ma5,
                    smooth: true,
                    showSymbol: false,
                    lineStyle: {
                        width: 1,
                        color: '#d48265'
                    }
                },
                {
                    name: 'MA10',
                    type: 'line',
                    data: ma10,
                    smooth: true,
                    showSymbol: false,
                    lineStyle: {
                        width: 1,
                        color: '#61a0a8'
                    }
                },
                {
                    name: 'MA20',
                    type: 'line',
                    data: ma20,
                    smooth: true,
                    showSymbol: false,
                    lineStyle: {
                        width: 1,
                        color: '#2f4554'
                    }
                },
                {
                    name: '成交量',
                    type: 'bar',
                    xAxisIndex: 1,
                    yAxisIndex: 1,
                    data: volumes,
                    itemStyle: {
                        color: function(params) {
                            const i = params.dataIndex;
                            if (i >= klineData.data.length) return '#ef232a';
                            const open = klineData.data[i].open;
                            const close = klineData.data[i].close;
                            return close > open ? '#ef232a' : '#14b143';
                        }
                    }
                }
            ]
        };
        
        // 隐藏加载状态并设置选项
        chart.hideLoading();
        chart.setOption(option, true);
        
        // 添加图表事件处理器，确保在各种操作后十字光标正常工作
        this.setupEventHandlers(chart);
        
        // 适应容器尺寸
        window.addEventListener('resize', function() {
            if (chart && !chart.isDisposed()) {
                chart.resize();
            }
        });
    },
    
    /**
     * 渲染分时图
     * @param {Object} chart - ECharts实例
     * @param {Object} timelineData - 分时图数据对象
     * @param {String} stockName - 股票名称
     */
    renderTimelineChart: function(chart, timelineData, stockName) {
        // 确保数据有效
        if (!chart || !timelineData || !timelineData.data || !Array.isArray(timelineData.data)) {
            console.error('无效的分时图数据或图表实例');
            if (chart) {
                chart.hideLoading();
                chart.setOption({
                    title: {
                        text: '暂无分时数据',
                        left: 'center',
                        top: 'center',
                        textStyle: {
                            color: '#999',
                            fontSize: 16
                        }
                    }
                });
            }
            return;
        }
        
        // 如果数据为空数组，显示暂无数据
        if (timelineData.data.length === 0) {
            chart.hideLoading();
            chart.setOption({
                title: {
                    text: '暂无分时数据',
                    left: 'center',
                    top: 'center',
                    textStyle: {
                        color: '#999',
                        fontSize: 16
                    }
                }
            });
            return;
        }
        
        // 提取时间和价格数据
        const times = timelineData.data.map(item => item.time);
        const prices = timelineData.data.map(item => item.price);
        const avgPrices = timelineData.data.map(item => item.avg_price);
        const volumes = timelineData.data.map(item => item.volume);
        
        // 获取基准价格（用于计算涨跌幅）
        const basePrice = timelineData.basePrice || prices[0] || 0;
        
        // 设置图表选项
        const option = {
            animation: false,
            title: {
                text: `${stockName} - 分时图`,
                left: 'center',
                textStyle: {
                    color: '#333'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    label: {
                        backgroundColor: '#333'
                    },
                    lineStyle: {
                        color: '#999',
                        width: 1,
                        type: 'dashed'
                    },
                    crossStyle: {
                        color: '#999',
                        width: 1,
                        type: 'dashed'
                    },
                    animation: false,
                    triggerTooltip: true
                },
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderColor: '#ccc',
                borderWidth: 1,
                textStyle: {
                    color: '#333'
                },
                formatter: function(params) {
                    if (!params || params.length === 0) return '';
                    
                    // 获取当前价格数据
                    const priceItem = params.find(p => p.seriesName === '价格') || params[0];
                    if (!priceItem || !priceItem.axisValue || !priceItem.data) return '';
                    
                    const time = priceItem.axisValue;
                    const price = priceItem.data;
                    const timeIndex = times.indexOf(time);
                    
                    // 计算涨跌幅
                    const priceChange = basePrice > 0 ? ((price - basePrice) / basePrice * 100).toFixed(2) : '0.00';
                    
                    // 构建提示内容
                    let tooltipText = `<div style="font-weight:bold;margin-bottom:5px;">${time}</div>`;
                    tooltipText += `<div>价格: <span style="color:#d14a61;font-weight:bold;">${price}</span></div>`;
                    tooltipText += `<div>涨跌: <span style="color:${priceChange >= 0 ? '#ef232a' : '#14b143'};font-weight:bold;">${priceChange}%</span></div>`;
                    
                    // 添加均价
                    const avgPriceItem = params.find(p => p.seriesName === '均价');
                    if (avgPriceItem && avgPriceItem.data !== null) {
                        tooltipText += `<div>均价: <span style="color:${avgPriceItem.color};font-weight:bold;">${avgPriceItem.data}</span></div>`;
                    }
                    
                    // 添加成交量
                    if (timeIndex >= 0 && timeIndex < volumes.length) {
                        const volume = volumes[timeIndex];
                        tooltipText += `<div>成交量: <span style="color:#4a90e2;font-weight:bold;">${(volume/100).toFixed(0)}手</span></div>`;
                    }
                    
                    return tooltipText;
                }
            },
            axisPointer: {
                link: {xAxisIndex: 'all'},
                label: {
                    backgroundColor: '#777'
                }
            },
            grid: [
                {
                    left: '3%',
                    right: '3%',
                    top: '8%',
                    height: '60%'
                },
                {
                    left: '3%',
                    right: '3%',
                    top: '73%',
                    height: '20%'
                }
            ],
            xAxis: [
                {
                    type: 'category',
                    data: times,
                    scale: true,
                    boundaryGap: false,
                    axisLine: {lineStyle: {color: '#8392A5'}},
                    axisTick: {show: false},
                    axisLabel: {
                        formatter: function(value) {
                            // 只显示时间部分
                            if (value.includes(' ')) {
                                return value.split(' ')[1];
                            }
                            return value;
                        }
                    },
                    splitLine: {show: false},
                    axisPointer: {
                        show: true,
                        triggerTooltip: true
                    }
                },
                {
                    type: 'category',
                    gridIndex: 1,
                    data: times,
                    scale: true,
                    boundaryGap: false,
                    axisLine: {lineStyle: {color: '#8392A5'}},
                    axisTick: {show: false},
                    axisLabel: {show: false},
                    splitLine: {show: false},
                    axisPointer: {
                        show: true,
                        triggerTooltip: false
                    }
                }
            ],
            yAxis: [
                {
                    scale: true,
                    splitNumber: 5,
                    axisLine: {lineStyle: {color: '#8392A5'}},
                    axisTick: {show: false},
                    axisLabel: {
                        inside: false,
                        formatter: '{value}\n'
                    },
                    splitLine: {show: true, lineStyle: {color: '#eee'}}
                },
                {
                    scale: true,
                    gridIndex: 1,
                    splitNumber: 3,
                    axisLine: {show: false},
                    axisTick: {show: false},
                    axisLabel: {show: false},
                    splitLine: {show: false}
                }
            ],
            dataZoom: [
                {
                    type: 'inside',
                    xAxisIndex: [0, 1],
                    start: 0,
                    end: 100,
                    zoomLock: false
                },
                {
                    show: true,
                    xAxisIndex: [0, 1],
                    type: 'slider',
                    bottom: '0%',
                    height: '5%',
                    start: 0,
                    end: 100,
                    zoomLock: false
                }
            ],
            series: [
                {
                    name: '价格',
                    type: 'line',
                    data: prices,
                    smooth: false,
                    symbol: 'none',
                    lineStyle: {
                        width: 2,
                        color: '#ef232a'
                    },
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            {
                                offset: 0,
                                color: 'rgba(239, 35, 42, 0.3)'
                            },
                            {
                                offset: 1,
                                color: 'rgba(239, 35, 42, 0.1)'
                            }
                        ])
                    }
                },
                {
                    name: '均价',
                    type: 'line',
                    data: avgPrices,
                    smooth: false,
                    symbol: 'none',
                    lineStyle: {
                        width: 1,
                        color: '#4a90e2',
                        type: 'dashed'
                    }
                },
                {
                    name: '成交量',
                    type: 'bar',
                    xAxisIndex: 1,
                    yAxisIndex: 1,
                    data: volumes,
                    itemStyle: {
                        color: function(params) {
                            const i = params.dataIndex;
                            // 根据价格变化着色
                            if (i === 0) return '#ef232a';
                            if (i >= prices.length) return '#ef232a';
                            
                            return prices[i] >= prices[i-1] ? '#ef232a' : '#14b143';
                        }
                    }
                }
            ]
        };
        
        // 隐藏加载状态并设置选项
        chart.hideLoading();
        chart.setOption(option, true);
        
        // 添加图表事件处理器，确保在各种操作后十字光标正常工作
        this.setupEventHandlers(chart);
        
        // 适应容器尺寸
        window.addEventListener('resize', function() {
            if (chart && !chart.isDisposed()) {
                chart.resize();
            }
        });
    },
    
    /**
     * 为图表添加事件处理器
     * @param {Object} chart - ECharts实例
     */
    setupEventHandlers: function(chart) {
        if (!chart) return;
        
        // 数据区域缩放结束后，重新激活十字光标
        chart.on('datazoom', () => {
            // 更新配置，确保十字光标仍然有效
            chart.setOption({
                tooltip: {
                    axisPointer: {
                        type: 'cross',
                        animation: false
                    }
                }
            });
        });
        
        // 确保鼠标移出图表区域时清除十字光标
        const container = chart.getDom();
        if (container) {
            container.addEventListener('mouseleave', () => {
                chart.dispatchAction({
                    type: 'hideTip'
                });
            });
            
            // 鼠标移入时自动显示十字光标
            container.addEventListener('mouseenter', (e) => {
                const rect = container.getBoundingClientRect();
                const offsetX = e.clientX - rect.left;
                const offsetY = e.clientY - rect.top;
                
                chart.dispatchAction({
                    type: 'showTip',
                    x: offsetX,
                    y: offsetY
                });
            });
        }
    },
    
    /**
     * 计算移动平均线
     * @param {Number} dayCount - 天数
     * @param {Array} data - 收盘价数组
     * @returns {Array} - 计算后的MA值数组，前(dayCount-1)天为null
     */
    calculateMA: function(dayCount, data) {
        const result = [];
        for (let i = 0; i < data.length; i++) {
            if (i < dayCount - 1) {
                result.push(null);
                continue;
            }
            let sum = 0;
            for (let j = 0; j < dayCount; j++) {
                sum += parseFloat(data[i - j]);
            }
            result.push(sum / dayCount);
        }
        return result;
    },
    
    /**
     * 显示K线图
     * @param {String} stockCode - 股票代码
     * @param {String} stockName - 股票名称
     * @param {String} containerId - 容器元素ID
     * @param {String} period - K线周期，可选值：daily, weekly, monthly
     * @param {Number} days - 获取多少天的数据
     */
    showKLineChart: async function(stockCode, stockName, containerId, period = 'daily', days = 90) {
        try {
            // 初始化图表
            const chart = this.initChart(containerId);
            if (!chart) return;
            
            // 加载数据
            const klineData = await this.loadKLineData(stockCode, period, days);
            
            // 渲染图表
            this.renderKLineChart(chart, klineData, stockName);
        } catch (error) {
            console.error('显示K线图失败:', error);
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `<div class="error-message">加载K线图失败: ${error.message}</div>`;
            }
        }
    },
    
    /**
     * 显示分时图
     * @param {String} stockCode - 股票代码
     * @param {String} stockName - 股票名称
     * @param {String} containerId - 容器元素ID
     */
    showTimelineChart: async function(stockCode, stockName, containerId) {
        try {
            // 初始化图表
            const chart = this.initChart(containerId);
            if (!chart) return;
            
            // 加载数据
            const timelineData = await this.loadTimelineData(stockCode);
            
            // 渲染图表
            this.renderTimelineChart(chart, timelineData, stockName);
        } catch (error) {
            console.error('显示分时图失败:', error);
            const container = document.getElementById(containerId);
            if (container) {
                container.innerHTML = `<div class="error-message">加载分时图失败: ${error.message}</div>`;
            }
        }
    }
}; 