/**
 * 
 * 
 */
(function() {
    // 轻量级日志处理
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
        // 保存原始日志方法引用
        const originalLog = console.log;
        // 替换为空函数但不阻碍功能
        console.log = console.info = console.warn = console.error = console.debug = function() {
            // 静默处理
        };
    }

    // 获取API密钥
    let userApiKey = '';
    
    // 在页面加载时尝试从数据属性获取API密钥
    document.addEventListener('DOMContentLoaded', function() {
        // 尝试从数据属性获取API密钥（这个数据属性需要在PHP中设置）
        const apiKeyElement = document.querySelector('.api-key-display');
        if (apiKeyElement && apiKeyElement.dataset.apiKey) {
            userApiKey = apiKeyElement.dataset.apiKey;
            console.log('从页面元素获取到API密钥');
        }
    });
    
    // 原始的fetch函数
    const originalFetch = window.fetch;
    
    // 替换原始的fetch函数
    window.fetch = async function(resource, options) {
        // 检查是否是目标API请求
        if (typeof resource === 'string' && 
            (resource.includes('api.llingfei.com/v1/chat/completions') || 
             resource.includes('/api/chat_proxy.php'))) {
            
            // 如果没有通过数据属性获取到API密钥，尝试从其他地方获取
            if (!userApiKey && options && options.headers) {
                // 尝试从请求头中获取API密钥
                const headers = options.headers;
                if (headers instanceof Headers) {
                    userApiKey = headers.get('Authorization');
                    if (userApiKey && userApiKey.startsWith('Bearer ')) {
                        userApiKey = userApiKey.substring(7);
                    }
                } else if (typeof headers === 'object') {
                    const authHeader = headers['Authorization'] || headers['authorization'];
                    if (authHeader && typeof authHeader === 'string' && authHeader.startsWith('Bearer ')) {
                        userApiKey = authHeader.substring(7);
                    }
                }
                
                // 如果还是没有API密钥，尝试从请求体中获取
                if (!userApiKey && options.body) {
                    try {
                        const body = JSON.parse(options.body);
                        userApiKey = body.api_key;
                    } catch (e) {
                        console.warn('无法从请求体解析API密钥');
                    }
                }
            }
            
            if (!userApiKey) {
                console.warn('未能获取API密钥，无法跟踪API使用情况');
                return originalFetch(resource, options);
            }
            
            try {
                // 先调用我们的跟踪API
                const trackResponse = await originalFetch('/api/track_api_usage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ api_key: userApiKey })
                });
                
                const trackData = await trackResponse.json();
                
                // 如果跟踪API返回错误（如次数用尽），则拒绝原始请求
                if (!trackResponse.ok || !trackData.success) {
                    const errorMessage = trackData.message || '调用次数不足，请联系管理员充值';
                    
                    // 更新UI上显示的剩余次数
                    updateRemainingCount(trackData.remaining || "0");
                    
                    // 模拟一个请求失败的Response对象
                    return new Response(JSON.stringify({
                        error: {
                            message: errorMessage,
                            type: 'insufficient_quota',
                            param: null,
                            code: 'insufficient_quota'
                        }
                    }), {
                        status: 403,
                        headers: { 'Content-Type': 'application/json' }
                    });
                }
                
                // 更新UI上显示的剩余次数
                updateRemainingCount(trackData.remaining);
                
                // 继续原始的API请求
                return originalFetch(resource, options);
            } catch (error) {
                console.error('跟踪API使用出错:', error);
                // 如果跟踪API调用失败，仍然继续原始请求
                return originalFetch(resource, options);
            }
        }
        
        // 对于其他请求，保持原样
        return originalFetch(resource, options);
    };
    
    // 更新剩余次数的辅助函数
    function updateRemainingCount(count) {
        const remainingElement = document.querySelector('.remaining-requests span');
        if (remainingElement) {
            remainingElement.textContent = count;
            console.log('API跟踪器已更新剩余次数显示为:', count);
        } else {
            console.warn('未找到剩余次数显示元素');
        }
        
        // 如果剩余次数较少，显示提示
        if (count <= 5) {
            if (typeof window.showNotification === 'function') {
                window.showNotification(`API调用次数不足，剩余${count}次，请及时充值`, 'warning');
            }
        }
    }
    
    console.log('API跟踪器已加载');
})(); 