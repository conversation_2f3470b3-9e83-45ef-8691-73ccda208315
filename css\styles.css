/* 全局样式 */
:root {
    --primary-color: #6a3de8;
    --primary-gradient: linear-gradient(135deg, #6a3de8, #8f5fe8);
    --secondary-color: #3ecf8e;
    --accent-color: #f5a623;
    --error-color: #e53935;
    --success-color: #43a047;
    --dark-color: #2c3e50;
    --light-color: #f5f7fa;
    --gray-color: #95a5a6;
    --border-color: #e0e0e0;
    --positive-color: #4caf50;
    --negative-color: #f44336;
    --box-shadow: 0 10px 20px rgba(106, 61, 232, 0.1);
    --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    --border-radius: 8px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
}

body {
    background-color: #f8fafc;
    color: var(--dark-color);
    line-height: 1.6;
    font-size: 14px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 通知提示样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    color: #333;
    z-index: 1000;
    max-width: 300px;
    transform: translateY(-20px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification.success {
    background-color: #e8f5e9;
    border-left: 4px solid var(--success-color);
}

.notification.error {
    background-color: #ffebee;
    border-left: 4px solid var(--error-color);
}

.notification.warning {
    background-color: #fff8e1;
    border-left: 4px solid var(--accent-color);
}

.notification.info {
    background-color: #e8f0fe;
    border-left: 4px solid var(--primary-color);
}

/* 头部样式 */
header {
    background-color: #fff;
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--box-shadow);
    margin-bottom: 20px;
    border-radius: var(--border-radius);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: transform 0.3s ease;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
}

header h1 {
    display: flex;
    align-items: center;
    font-size: 22px;
    color: var(--primary-color);
    font-weight: 600;
}

header h1 img {
    width: 32px;
    height: 32px;
    margin-right: 10px;
    border-radius: 50%;
    object-fit: cover;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
    padding: 8px 16px;
    border: 1px solid var(--primary-color);
    border-radius: 20px;
    transition: var(--transition);
    font-weight: 500;
}

.user-info a:hover {
    background: var(--primary-gradient);
    color: white;
    border-color: transparent;
    box-shadow: 0 4px 10px rgba(106, 61, 232, 0.3);
}

/* 登录和注册页面样式 */
body.auth-page {
    background: url('../bl.png') no-repeat center center fixed;
    background-size: cover;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

body.auth-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1;
}

body.auth-page .container {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 1200px;
}

.auth-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
}

.auth-features {
    flex: 1;
    color: white;
    padding: 30px;
}

.auth-features h1 {
    font-size: 2.2rem;
    margin-bottom: 30px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    font-weight: 600;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.feature-item {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 20px;
    border-radius: 12px;
    transition: transform 0.3s ease, background 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.feature-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.feature-item h3 {
    font-size: 1.2rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.feature-item p {
    font-size: 0.9rem;
    opacity: 0.8;
}

.login-container,
.register-container {
    flex: 0 0 400px;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 16px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-container:hover,
.register-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
}

.auth-logo {
    text-align: center;
    margin-bottom: 25px;
}

.auth-logo img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.form-title {
    text-align: center;
    margin-bottom: 25px;
    color: var(--primary-color);
    font-size: 24px;
    font-weight: 600;
}

.form-subtitle {
    text-align: center;
    margin-bottom: 30px;
    color: #667080;
    font-size: 15px;
}

.form-group {
    margin-bottom: 24px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--dark-color);
    font-size: 15px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 15px;
    transition: var(--transition);
    background-color: rgba(255, 255, 255, 0.8);
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(106, 61, 232, 0.15);
    background-color: #fff;
}

.submit-btn {
    width: 100%;
    padding: 14px;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: var(--transition);
    margin-top: 12px;
    box-shadow: 0 4px 12px rgba(106, 61, 232, 0.25);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(106, 61, 232, 0.4);
}

.form-footer {
    text-align: center;
    margin-top: 25px;
    color: #667080;
    font-size: 14px;
}

.form-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.form-footer a:hover {
    text-decoration: underline;
}

.alert {
    padding: 14px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-size: 14px;
    display: flex;
    align-items: center;
    opacity: 0.9;
}

.alert::before {
    content: '';
    width: 18px;
    height: 18px;
    margin-right: 10px;
    background-position: center;
    background-repeat: no-repeat;
    background-size: contain;
}

.alert-danger {
    background-color: rgba(229, 57, 53, 0.1);
    color: var(--error-color);
    border-left: 3px solid var(--error-color);
}

.alert-danger::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23e53935'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z'/%3E%3C/svg%3E");
}

.alert-success {
    background-color: rgba(67, 160, 71, 0.1);
    color: var(--success-color);
    border-left: 3px solid var(--success-color);
}

.alert-success::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2343a047'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
}

/* 仪表盘样式 */
.dashboard-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 15px;
}

.sidebar {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 20px;
    box-shadow: var(--card-shadow);
}

.main-content {
    flex: 1;
}

.sidebar section,
.main-content section {
    background-color: #fff;
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: var(--card-shadow);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.sidebar section:hover,
.main-content section:hover {
    box-shadow: var(--box-shadow);
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.section-title::before {
    content: '';
    width: 3px;
    height: 16px;
    background: var(--primary-gradient);
    margin-right: 8px;
    border-radius: 2px;
}

/* API 密钥设置 */
.api-key-section {
    display: flex;
    align-items: center;
    position: relative;
}

#apiKey {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    background-color: rgba(250, 250, 250, 0.8);
    transition: var(--transition);
}

#apiKey:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(106, 61, 232, 0.15);
    background-color: #fff;
}

.key-hint {
    position: absolute;
    right: 10px;
    font-size: 12px;
    padding: 4px 8px;
    background-color: rgba(67, 160, 71, 0.1);
    color: var(--success-color);
    border-radius: 12px;
}

.key-hint.unsaved {
    background-color: rgba(245, 166, 35, 0.1);
    color: var(--accent-color);
}

.key-hint.error {
    background-color: rgba(229, 57, 53, 0.1);
    color: var(--error-color);
}

.storage-mode-section {
    margin: 15px 0;
}

.storage-desc {
    margin-top: 10px;
    font-size: 13px;
    line-height: 1.5;
    color: #666;
    background-color: rgba(106, 61, 232, 0.05);
    padding: 10px;
    border-radius: 6px;
    border-left: 3px solid rgba(106, 61, 232, 0.5);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin-bottom: 15px;
}

.toggle-switch input { 
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-switch .slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + .slider {
    background-color: var(--secondary-color);
}

.toggle-switch input:checked + .slider:before {
    transform: translateX(26px);
}

.storage-mode-text {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    font-size: 14px;
    font-weight: 500;
    color: #555;
}

/* 公告区域 */
.announcement {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.announcement:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.announcement-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.announcement-date {
    font-size: 12px;
    color: var(--gray-color);
    margin-bottom: 8px;
}

.announcement-content {
    font-size: 14px;
    line-height: 1.5;
}

/* 搜索区域 */
.search-section {
    margin-bottom: 20px;
}

.input-section {
    display: flex;
    gap: 10px;
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 14px;
    background-color: rgba(250, 250, 250, 0.8);
    transition: var(--transition);
}

.search-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(106, 61, 232, 0.15);
    background-color: #fff;
}

.search-button {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    box-shadow: 0 4px 6px rgba(106, 61, 232, 0.2);
}

.search-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(106, 61, 232, 0.3);
}

.search-results {
    margin-top: 20px;
}

.search-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #fff;
    border-radius: 10px;
    margin-bottom: 10px;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    box-shadow: var(--card-shadow);
}

.search-result-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.search-result-item .stock-info {
    background: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.search-result-item .stock-name {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 16px;
}

.search-result-item .stock-code {
    font-size: 13px;
    color: var(--gray-color);
    background-color: #f0f0f0;
    padding: 2px 8px;
    border-radius: 4px;
    display: inline-block;
    width: fit-content;
}

.add-btn {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    transition: var(--transition);
}

.add-btn:hover {
    background-color: #32b67a;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(62, 207, 142, 0.3);
}

/* 股票列表样式 */
#stockList {
    margin-top: 15px;
    max-height: 600px;
    overflow-y: auto;
    padding-right: 5px;
}

.stock-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.stock-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--primary-gradient);
}

.stock-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: var(--card-shadow);
    padding: 8px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.stock-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.stock-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 6px;
}

.stock-header h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 2px;
    margin-bottom: 0;
}

.stock-symbol {
    background-color: #f0f0f0;
    color: #666;
    padding: 1px 6px;
    border-radius: 4px;
    font-size: 11px;
    margin-left: 6px;
    font-weight: 500;
}

.stock-price {
    font-size: 18px;
    font-weight: 700;
    display: flex;
    align-items: baseline;
    margin-bottom: 6px;
    flex: 1;
}

.stock-info {
    background-color: #f7f9fc;
    padding: 6px;
    border-radius: 6px;
    margin-top: auto;
    margin-bottom: 6px;
    border: 1px solid #f0f2f7;
}

.info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
}

.info-item {
    display: flex;
    flex-direction: column;
    width: 48%;
}

.label {
    color: var(--gray-color);
    font-size: 12px;
    margin-bottom: 2px;
}

.value {
    font-weight: 500;
    font-size: 13px;
}

.stock-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 6px;
    gap: 6px;
}

.btn {
    padding: 6px 0;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    flex: 1;
    min-width: 0;
    white-space: nowrap;
    text-align: center;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: 0 4px 6px rgba(106, 61, 232, 0.2);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(106, 61, 232, 0.3);
}

.btn-secondary {
    background-color: #f0f0f0;
    color: #333;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
    transform: translateY(-2px);
}

.btn-danger {
    background-color: rgba(229, 57, 53, 0.1);
    color: #e53935;
    border: 1px solid rgba(229, 57, 53, 0.3);
}

.btn-danger:hover {
    background-color: rgba(229, 57, 53, 0.2);
    transform: translateY(-2px);
}

.btn-icon {
    width: 36px;
    flex: 0 0 36px;
}

.price {
    margin-right: 6px;
    line-height: 1.1;
}

.change {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 12px;
    line-height: 1;
}

.positive {
    color: var(--positive-color);
    background-color: rgba(76, 175, 80, 0.08);
}

.negative {
    color: var(--negative-color);
    background-color: rgba(244, 67, 54, 0.08);
}

/* 聊天区域 */
.chat-section {
    margin-top: 30px;
}

.chat-container {
    max-height: 500px;
    overflow-y: auto;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 8px;
    margin-top: 15px;
    border: 1px solid var(--border-color);
}

.message {
    margin-bottom: 15px;
    padding: 12px 15px;
    border-radius: 8px;
    max-width: 85%;
    position: relative;
    line-height: 1.5;
}

.user-message {
    background: var(--primary-gradient);
    color: white;
    margin-left: auto;
    border-radius: 12px 12px 0 12px;
    box-shadow: 0 2px 5px rgba(106, 61, 232, 0.2);
}

.message-content {
    word-break: break-word;
    font-size: 14px;
}

/* 输入区域样式 */
.input-section {
    display: flex;
    margin-top: 15px;
    gap: 10px;
}

.input-section input,
.input-section textarea {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    resize: none;
    font-size: 14px;
    transition: var(--transition);
}

.input-section input:focus,
.input-section textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(106, 61, 232, 0.15);
}

.send-btn {
    padding: 12px 20px;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    box-shadow: 0 4px 6px rgba(106, 61, 232, 0.2);
}

.send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(106, 61, 232, 0.3);
}

/* API 许可证列表样式 */
.licence-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
}

.licence-item:last-child {
    border-bottom: none;
}

.licence-text {
    font-family: monospace;
}

.delete-btn {
    padding: 4px 8px;
    background-color: var(--error-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
}

.delete-btn:hover {
    background-color: #c62828;
}

/* 空消息和错误消息样式 */
.empty-message,
.error-message,
.loading {
    padding: 15px;
    text-align: center;
    color: var(--gray-color);
    font-style: italic;
}

.error-message {
    color: var(--error-color);
}

.loading {
    color: var(--primary-color);
}

/* 页脚样式 */
footer {
    text-align: center;
    padding: 20px;
    color: var(--gray-color);
    font-size: 12px;
}

/* 响应式样式 */
@media (max-width: 992px) {
    .dashboard-container {
        grid-template-columns: 1fr;
    }
    
    .sidebar {
        order: 2;
    }
    
    .main-content {
        order: 1;
    }
    
    .auth-wrapper {
        flex-direction: column-reverse;
        gap: 20px;
    }
    
    .auth-features {
        width: 100%;
        padding: 20px 0;
    }
    
    .login-container,
    .register-container {
        flex: none;
        width: 100%;
        max-width: 420px;
        margin: 0 auto;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .stock-grid {
        grid-template-columns: 1fr;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    header h1 {
        font-size: 20px;
    }
    
    .user-info {
        flex-direction: column;
        gap: 10px;
    }
    
    .container {
        padding: 15px;
    }
    
    .sidebar section, 
    .main-content section {
        margin-bottom: 15px;
    }
    
    .section-title {
        font-size: 16px;
    }
    
    .stock-card {
        padding: 15px;
    }
    
    .stock-price {
        flex-direction: column;
    }
    
    .change {
        margin-top: 5px;
    }
    
    .info-item .label {
        font-size: 12px;
    }
    
    .info-item .value {
        font-size: 13px;
    }
    
    .auth-features h1 {
        font-size: 1.8rem;
    }
    
    .feature-item {
        padding: 15px;
    }
    
    /* 全局按钮样式修复 */
    button, 
    .stock-btn, 
    .add-button, 
    .cloud-button, 
    .search-button {
        min-height: 40px !important;
        height: 40px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 8px 15px !important;
        font-size: 14px !important;
    }
}

@media (max-width: 480px) {
    .stock-grid {
        grid-template-columns: 1fr;
    }
    
    .login-container,
    .register-container {
        padding: 20px 15px;
    }
    
    .form-title {
        font-size: 18px;
    }
    
    .form-subtitle {
        font-size: 13px;
    }
    
    .form-control {
        padding: 8px 10px;
    }
    
    .submit-btn {
        padding: 10px;
    }
    
    .auth-features h1 {
        font-size: 1.5rem;
    }
    
    .feature-icon {
        font-size: 1.5rem;
    }
    
    .feature-item h3 {
        font-size: 1rem;
    }
}

.remove-btn:hover, .btn-delete:hover {
    background-color: rgba(229, 57, 53, 0.1);
}

/* Agent解答区域 */
.chat-section .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#chatContainer {
    max-height: 600px;
    min-height: 600px;
    overflow-y: auto;
}


.analyzing-text {
    color: #6a7a8c;
    font-size: 13px;
    text-align: center;
    padding: 8px 0;
    font-style: italic;
}

/* 微信二维码样式 */
.wechat-qrcode {
    margin-top: 15px;
    text-align: center;
    padding: 15px;
    background-color: rgba(106, 61, 232, 0.05);
    border-radius: 10px;
    border: 1px dashed rgba(106, 61, 232, 0.2);
}

.wechat-qrcode img {
    max-width: 180px;
    border-radius: 8px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.wechat-qrcode p {
    margin-top: 10px;
    font-size: 14px;
    color: #555;
    font-weight: 500;
}

/* 股票详情弹窗样式 */
.stock-detail-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(3px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.stock-detail-content {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 20px;
    width: 90%;
    max-width: 800px;
    max-height: 85vh;
    overflow: auto;
    position: relative;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stock-detail-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.stock-detail-header h2 {
    margin: 0;
    font-size: 24px;
    color: #333;
}

.update-time {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.stock-detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.price-section {
    background: rgba(248, 249, 250, 0.8);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.price-section h3 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #666;
}

.current-price {
    font-size: 32px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 10px;
}

.price-up {
    color: #ff3636;
}

.price-down {
    color: #4caf50;
}

.modal-close-btn {
    position: absolute;
    right: 15px;
    top: 15px;
    border: none;
    background: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 5px;
    line-height: 1;
    transition: color 0.3s ease;
}

.modal-close-btn:hover {
    color: #333;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .stock-detail-content {
        width: 95%;
        padding: 15px;
    }

    .stock-detail-grid {
        grid-template-columns: 1fr;
    }

    .current-price {
        font-size: 28px;
    }
}

/* K线图相关样式 */
.kline-section {
    margin-top: 20px;
    background: rgba(248, 249, 250, 0.8);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.kline-section h3 {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #666;
}

.kline-period-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.period-tab {
    padding: 6px 15px;
    border: 1px solid #ddd;
    background-color: #f8f9fa;
    color: #666;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    white-space: nowrap;
}

.period-tab:hover {
    background-color: #e9ecef;
}

.period-tab.active {
    background-color: #4a90e2;
    color: white;
    border-color: #4a90e2;
}

#klineChart {
    background-color: #fff;
    border-radius: 6px;
    min-height: 400px;
    width: 100%;
    position: relative;
    z-index: 1;
    cursor: crosshair;
}

.error-message {
    padding: 15px;
    background-color: #ffebee;
    border-left: 4px solid #f44336;
    color: #b71c1c;
    margin: 10px 0;
    border-radius: 4px;
    font-size: 14px;
}

/* 数据项网格样式 */
.data-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.data-item {
    padding: 8px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.data-item:hover {
    background-color: rgba(255, 255, 255, 0.9);
}

.data-item .label {
    color: #666;
    font-size: 12px;
    display: block;
    margin-bottom: 4px;
}

.data-item .value {
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

/* 移动端K线图适配 */
@media (max-width: 576px) {
    #klineChart {
        min-height: 300px;
    }
    
    .kline-period-tabs {
        justify-content: center;
    }
    
    .period-tab {
        padding: 4px 10px;
        font-size: 12px;
    }
}

/* 量化结果区域样式 */
#agent-section {
    margin-bottom: 30px;
}

#agent-section .section-title {
    margin-bottom: 15px;
}

#chatContainer {
    max-height: 600px;
    min-height: 200px;
    overflow-y: auto;
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.empty-message {
    text-align: center;
    padding: 15px;
    color: #666;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 6px;
    margin-bottom: 10px;
    font-size: 14px;
}

.empty-message i {
    margin-right: 5px;
    color: #4a90e2;
}

/* 量化结果内容区域样式 */
.chat-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

/* 量化中提示样式 */
.loading-dots {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    padding: 15px 20px;
    font-weight: 500;
    margin: 20px auto;
    width: fit-content;
    color: var(--primary-color);
}

.loading-dots span {
    animation: dots 1.5s infinite;
    margin-left: 2px;
    opacity: 0;
}

.loading-dots span:nth-child(1) {
    animation-delay: 0s;
}

.loading-dots span:nth-child(2) {
    animation-delay: 0.5s;
}

.loading-dots span:nth-child(3) {
    animation-delay: 1s;
}

@keyframes dots {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}

/* 量化结果卡片样式 */
.message-content {
    text-align: left;
    background: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    padding: 15px;
    margin: 10px auto;
    max-width: 800px;
    width: 100%;
    display: inline-block;
    white-space: normal;
    word-break: break-word;
}

/* 量化完成提示样式 */
.analysis-complete {
    display: flex;
    justify-content: center;
    align-items: center;
    background: #e8f5e9;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    padding: 10px 15px;
    font-weight: 500;
    margin: 10px auto;
    width: fit-content;
    color: var(--success-color);
} 