<?php
require_once '../includes/functions.php';

// 检查是否已登录
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

// 检查是否有POST数据
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '无效的请求方法']);
    exit;
}

// 获取并验证API密钥
$apiKey = isset($_POST['api_key']) ? sanitizeInput($_POST['api_key']) : '';

if (empty($apiKey)) {
    echo json_encode(['success' => false, 'message' => 'API密钥不能为空']);
    exit;
}

// 更新数据库中的API密钥
$result = updateApiKey($_SESSION['user_id'], $apiKey);

// 返回结果
echo json_encode($result);
exit;
?> 