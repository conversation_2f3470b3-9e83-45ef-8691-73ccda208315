<?php
/**
 * 股票名称更新脚本
 * 用于从API获取最新的股票名称列表并缓存到本地
 * 可以通过cron任务定期执行此脚本以保持数据最新
 */

// 设置错误报告
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 引入函数库
require_once 'includes/functions.php';

// 记录开始更新
error_log("开始更新股票名称列表...");

// 从licence.json获取随机licence
$licences = getLicences();
if (empty($licences)) {
    error_log("无法获取API许可证");
    exit(1);
}

// 随机获取一个licence
$randomLicence = $licences[array_rand($licences)];

// 股票名称API
$STOCK_NAME_API = "http://localhost:8888/api/public/stock_info_a_code_name";
$nameApiUrl = $STOCK_NAME_API;

error_log("请求股票名称API: " . $nameApiUrl);

// 初始化CURL获取名称列表
$nameCh = curl_init();
curl_setopt($nameCh, CURLOPT_URL, $nameApiUrl);
curl_setopt($nameCh, CURLOPT_RETURNTRANSFER, true);
curl_setopt($nameCh, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($nameCh, CURLOPT_TIMEOUT, 30); // 适当延长超时时间
curl_setopt($nameCh, CURLOPT_HTTPHEADER, [
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
]);

// 执行请求
$nameResponse = curl_exec($nameCh);
$nameHttpCode = curl_getinfo($nameCh, CURLINFO_HTTP_CODE);
$curlError = curl_error($nameCh);
curl_close($nameCh);

// 检查请求是否成功
if ($nameHttpCode != 200) {
    error_log("API请求失败 (HTTP $nameHttpCode): " . ($curlError ? $curlError : "未知错误"));
    exit(1);
}

if (empty($nameResponse)) {
    error_log("API返回空响应");
    exit(1);
}

// 解析JSON
$nameData = json_decode($nameResponse, true);
if ($nameData === null && json_last_error() !== JSON_ERROR_NONE) {
    error_log("JSON解析失败: " . json_last_error_msg());
    error_log("响应内容: " . substr($nameResponse, 0, 200) . "...");
    exit(1);
}

// 确保缓存目录存在
if (!file_exists("cache")) {
    mkdir("cache", 0755, true);
    error_log("创建缓存目录: cache");
}

// 标准化数据格式
$standardizedData = [];
$hasValidData = false;

// 确定API返回的数据结构
if (is_array($nameData)) {
    if (isset($nameData['data']) && is_array($nameData['data'])) {
        // API返回了{data:[...]}格式
        error_log("API返回data数组格式, 包含 " . count($nameData['data']) . " 条记录");
        $stockList = $nameData['data'];
        $hasValidData = true;
    } else if (isset($nameData['stocks']) && is_array($nameData['stocks'])) {
        // API返回了{stocks:[...]}格式
        error_log("API返回stocks数组格式, 包含 " . count($nameData['stocks']) . " 条记录");
        $stockList = $nameData['stocks'];
        $hasValidData = true;
    } else {
        // 直接是数组格式
        error_log("API返回直接数组格式, 包含 " . count($nameData) . " 条记录");
        $stockList = $nameData;
        $hasValidData = true;
    }
    
    if ($hasValidData) {
        foreach ($stockList as $stock) {
            // 处理不同字段格式，确保包含所有可能的字段名
            $standardItem = [
                'dm' => $stock['dm'] ?? $stock['code'] ?? $stock['stock_code'] ?? '',
                'mc' => $stock['mc'] ?? $stock['name'] ?? $stock['stock_name'] ?? '',
                'code' => $stock['dm'] ?? $stock['code'] ?? $stock['stock_code'] ?? '',
                'name' => $stock['mc'] ?? $stock['name'] ?? $stock['stock_name'] ?? ''
            ];
            
            // 只添加有效的记录
            if (!empty($standardItem['dm']) && !empty($standardItem['mc'])) {
                $standardizedData[] = $standardItem;
            }
        }
    }
}

// 保存标准化后的数据
if (!empty($standardizedData)) {
    $cacheFile = "cache/stock_names.json";
    $jsonData = json_encode($standardizedData, JSON_UNESCAPED_UNICODE);
    file_put_contents($cacheFile, $jsonData);
    
    $stockCount = count($standardizedData);
    error_log("股票名称列表已更新，共 {$stockCount} 条记录已缓存到: {$cacheFile}");
    
    // 创建索引文件，便于快速查找
    $indexData = [];
    foreach ($standardizedData as $stock) {
        // 使用股票代码作为键，股票名称作为值
        $stockCode = $stock['dm'] ?? $stock['code'] ?? '';
        $stockName = $stock['mc'] ?? $stock['name'] ?? '';
        
        if (!empty($stockCode) && !empty($stockName)) {
            $indexData[$stockCode] = $stockName;
        }
    }
    
    // 保存索引数据
    $indexFile = "cache/stock_names_index.json";
    if (!empty($indexData)) {
        file_put_contents($indexFile, json_encode($indexData, JSON_UNESCAPED_UNICODE));
        error_log("股票名称索引已创建: {$indexFile}，包含 " . count($indexData) . " 条记录");
    } else {
        error_log("警告：未能创建有效的股票名称索引");
    }
    
    // 输出成功消息
    echo "股票名称列表已更新，共 {$stockCount} 条记录\n";
} else {
    error_log("未能提取有效的股票数据，原始响应长度: " . strlen($nameResponse));
    echo "更新失败: 未能提取有效的股票数据\n";
    exit(1);
}

exit(0);