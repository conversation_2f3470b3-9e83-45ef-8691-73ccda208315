<?php
require_once '../includes/functions.php';

// 检查是否已登录
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

// 检查是否有POST数据
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '无效的请求方法']);
    exit;
}

// 获取股票代码和API密钥
$stockCode = isset($_POST['code']) ? sanitizeInput($_POST['code']) : '';
$apiKey = isset($_POST['api_key']) ? sanitizeInput($_POST['api_key']) : '';

if (empty($stockCode)) {
    echo json_encode(['success' => false, 'message' => '股票代码不能为空']);
    exit;
}

// 如果用户没有提供API密钥，尝试从数据库中获取
if (empty($apiKey)) {
    $user = getCurrentUser();
    if (!empty($user['api_key'])) {
        $apiKey = $user['api_key'];
    } else {
        // 尝试从许可证列表中随机获取一个
        $licences = getLicences($_SESSION['user_id']);
        if (!empty($licences)) {
            // 随机选择一个许可证
            $randomLicence = $licences[array_rand($licences)];
            $apiKey = $randomLicence['licence_key'];
        } else {
            // 尝试从licence.json文件中获取
            $licenceFile = '../licence.json';
            if (file_exists($licenceFile)) {
                $licenceData = json_decode(file_get_contents($licenceFile), true);
                if (!empty($licenceData)) {
                    $apiKey = $licenceData[array_rand($licenceData)];
                }
            }
        }
    }
}

if (empty($apiKey)) {
    echo json_encode(['success' => false, 'message' => '请设置API密钥或添加许可证']);
    exit;
}

// 调用AI API进行分析
$result = analyzeStockWithAI($stockCode, $apiKey);

// 返回结果
echo json_encode($result);
exit;

// 使用AI API分析股票
function analyzeStockWithAI($stockCode, $apiKey) {
    $apiUrl = API_URL;
    $apiModel = API_MODEL;
    
    // 准备请求数据
    $data = [
        'model' => $apiModel,
        'messages' => [
            [
                'role' => 'user',
                'content' => $stockCode
            ]
        ]
    ];
    
    // 发送API请求
    $ch = curl_init($apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['success' => false, 'message' => '请求失败: ' . $error];
    }
    
    if ($httpCode !== 200) {
        return ['success' => false, 'message' => 'API请求失败, 状态码: ' . $httpCode];
    }
    
    $responseData = json_decode($response, true);
    
    if (!$responseData || !isset($responseData['choices'][0]['message']['content'])) {
        return ['success' => false, 'message' => '无法解析API响应'];
    }
    
    $analysisContent = $responseData['choices'][0]['message']['content'];
    
    return ['success' => true, 'data' => $analysisContent];
}
?> 