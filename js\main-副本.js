/**
 * 股票分析平台前端脚本
 */

// 简化版反调试，避免影响功能
(function() {
    // 仅在生产环境下进行轻量级处理
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
        // 保存原始console方法
        const originalConsole = {
            log: console.log,
            info: console.info,
            warn: console.warn,
            error: console.error,
            debug: console.debug
        };

        // 轻量级覆盖，不影响功能
        console.log = console.info = console.warn = console.error = console.debug = function() {
            // 空函数，但不阻止后续代码执行
        };
    }
})();

// 覆盖console方法，防止日志泄露
(function() {
    // 保存原始console方法的引用
    const originalConsole = {
        log: console.log,
        info: console.info,
        warn: console.warn,
        error: console.error,
        debug: console.debug
    };

    // 生产环境下禁用console
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
        console.log = console.info = console.warn = console.error = console.debug = function() {
            // 静默失败或替换为空操作
        };
    }
})();

// 默认使用前端存储模式
if (localStorage.getItem('use_frontend_storage') === null) {
    localStorage.setItem('use_frontend_storage', 'true');
    console.log('首次访问，默认启用前端模式');
}

/**
 * 初始化股票名称缓存
 */
function initStockNamesCache() {
    // 检查缓存是否存在
    fetch('cache/stock_names.json', { method: 'HEAD' })
        .then(response => {
            if (!response.ok) {
                console.log('股票名称缓存不存在，尝试从API获取');
                fetchStockNamesList();
            } else {
                console.log('股票名称缓存已存在');
            }
        })
        .catch(error => {
            console.error('检查股票名称缓存失败:', error);
            fetchStockNamesList();
        });
        
    function fetchStockNamesList() {
        fetch('api/update_stock_names.php')
            .then(response => response.text())
            .then(text => {
                try {
                    const data = JSON.parse(text);
                    console.log('获取股票名称列表结果:', data);
                    if (data.success) {
                        showNotification('股票名称缓存已更新', 'success');
                    } else {
                        console.error('获取股票名称列表失败:', data.message);
                    }
                } catch (e) {
                    console.error('解析股票名称列表响应失败:', e);
                }
            })
            .catch(error => {
                console.error('获取股票名称列表请求失败:', error);
            });
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 启动市场指数自动刷新
    startMarketIndicesRefresh();
    
    // 启动股票变动数据自动刷新
    startStockChangesRefresh();
    
    // 绑定股票变动标签切换事件
    bindStockChangesTabs();
    
    // 获取API密钥并存储到sessionStorage中供其他脚本使用
    getApiKeyFromDOM();
    
    // 加载用户许可证
    loadLicences();
    
    // 绑定表单提交事件
    bindFormSubmitEvents();
    
    // 初始化股票名称缓存
    initStockNamesCache();
});

/**
 * 从DOM元素中获取API密钥
 */
function getApiKeyFromDOM() {
    // 从带有data-api-key属性的元素获取API密钥
    const apiKeyElement = document.querySelector('.api-key-display');
    if (apiKeyElement && apiKeyElement.dataset.apiKey) {
        const apiKey = apiKeyElement.dataset.apiKey;
        // 将API密钥存储到sessionStorage中供其他脚本使用
        sessionStorage.setItem('api_key', apiKey);
        console.log('从DOM获取API密钥并保存到会话存储');
    }
}

/**
 * 加载用户的API许可证
 */
function loadLicences() {
    const licenceList = document.getElementById('licenceList');
    if (!licenceList) return;
    
    fetch('api/get_licences.php')
    .then(response => response.json())
    .then(data => {
        licenceList.innerHTML = '';
        
        if (data.success && data.licences.length > 0) {
            data.licences.forEach(licence => {
                const licenceItem = document.createElement('div');
                licenceItem.className = 'licence-item';
                licenceItem.innerHTML = `
                    <div class="licence-text">${licence.licence_key}</div>
                    <button class="delete-btn" data-id="${licence.id}">删除</button>
                `;
                licenceList.appendChild(licenceItem);
            });
            
            // 绑定删除按钮事件
            document.querySelectorAll('.delete-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const licenceId = this.getAttribute('data-id');
                    deleteLicence(licenceId);
                });
            });
        } else {
            licenceList.innerHTML = '<div class="empty-message">暂无许可证</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        licenceList.innerHTML = '<div class="error-message">加载失败</div>';
    });
}

/**
 * 添加API许可证
 */
function addLicence(licence) {
    fetch('add_licence.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ licence_key: licence })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('添加成功', 'success');
            loadLicences();
        } else {
            showNotification(data.message || '添加失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('添加失败', 'error');
    });
}

/**
 * 删除API许可证
 */
function deleteLicence(licenceId) {
    if (!confirm('确定要删除此许可证吗？')) {
        return;
    }
    
    fetch('delete_licence.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ licence_id: licenceId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('删除成功', 'success');
            loadLicences();
        } else {
            showNotification(data.message || '删除失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('删除失败', 'error');
    });
}

/**
 * 加载用户关注的股票列表
 */
function loadUserStocks() {
    // 检查前端存储模式
    const useFrontendMode = localStorage.getItem('frontend_storage_mode') === 'true';

    // 根据存储模式选择不同的加载方式
    if (useFrontendMode && window.FrontendStocks) {
        console.log('使用前端存储模式加载股票');
        const frontendStocks = window.FrontendStocks.getAllStocks();
        
        // 清空容器
        const stocksContainer = document.getElementById('stocksContainer');
        if (stocksContainer) {
            stocksContainer.innerHTML = '';
            
            // 检查是否有股票
            if (frontendStocks.length === 0) {
                stocksContainer.innerHTML = '<div class="no-stocks">您尚未添加任何股票。<br>可通过上方搜索框查找并添加股票。</div>';
                return;
            }
            
            // 构建一个对象来保存股票代码到股票对象的映射
            const stockMap = {};
            frontendStocks.forEach(stock => {
                stockMap[stock.code] = stock;
            });
            
            // 排序股票列表 (可以根据需求自定义排序)
            const sortedStocks = [...frontendStocks].sort((a, b) => {
                // 默认按添加时间排序，最新添加的在前面
                const dateA = new Date(a.added_at || 0);
                const dateB = new Date(b.added_at || 0);
                return dateB - dateA;
            });
            
            // 遍历并创建股票卡片
            sortedStocks.forEach(stock => {
                // 将前端存储的股票数据转换为标准格式
                const standardizedStock = {
                    code: stock.code,
                    name: stock.name,
                    price: stock.price || '0.00',
                    change: stock.price_change || '0.00',
                    open: stock.open || '0.00',
                    close: stock.close || '0.00',
                    high: stock.high || '0.00',
                    low: stock.low || '0.00',
                    // 添加MA数据
                    ma5: stock.ma5 || '-',
                    ma10: stock.ma10 || '-',
                    ma20: stock.ma20 || '-',
                    volumeRatio: stock.volumeRatio || '0.00',
                    kdj: stock.kdj || null,  // 添加KDJ数据
                    macd: stock.macd || null  // 添加MACD数据
                };
                
                // 创建股票卡片
                if (window.FrontendStocks.createStockCard) {
                    const card = window.FrontendStocks.createStockCard(standardizedStock);
                    stocksContainer.appendChild(card);
                } else {
                    console.error('createStockCard方法未定义');
                }
            });
            
            // 显示股票数量
            const stockCount = document.getElementById('stockCount');
            if (stockCount) {
                stockCount.textContent = `${frontendStocks.length}`;
            }
        } else {
            console.error('找不到股票容器元素');
        }
    } else {
        const stockList = document.getElementById('stockList');
        if (!stockList) return;
        
        console.log('开始加载用户股票列表...');
        stockList.innerHTML = '<div class="loading">加载中...</div>';
        
        // 检查是否启用了前端模式
        if (window.FrontendStocks && typeof window.FrontendStocks.isEnabled === 'function' && window.FrontendStocks.isEnabled()) {
            console.log('使用前端模式加载股票列表');
            
            try {
                // 获取股票数据
                const stocks = window.FrontendStocks.getAllStocks();
                console.log('前端模式股票数据:', stocks);
                
                // 清空列表
                stockList.innerHTML = '';
                
                // 如果没有股票，显示提示
                if (!stocks || stocks.length === 0) {
                    stockList.innerHTML = '<div class="empty-message">暂无添加的股票，请先搜索并添加股票</div>';
                    return;
                }
                
                // 创建股票网格
                const stockGrid = document.createElement('div');
                stockGrid.className = 'stock-grid';
                
                // 添加股票到列表
                stocks.forEach(stock => {
                    console.log('处理前端模式股票:', stock);
                    // 标准化数据结构
                    const standardizedStock = {
                        code: stock.code || stock.stock_id || '',
                        name: stock.name || '未知股票',
                        price: stock.price || stock.current_price || '0.00',
                        change: stock.change || stock.price_change || '0.00',
                        // 添加其他可能需要的字段默认值
                        open: stock.open || '0.00',
                        close: stock.close || '0.00',
                        high: stock.high || '0.00',
                        low: stock.low || '0.00',
                        // 添加MA数据
                        ma5: stock.ma5 || '-',
                        ma10: stock.ma10 || '-',
                        ma20: stock.ma20 || '-',
                        volumeRatio: stock.volumeRatio || '0.00',
                        kdj: stock.kdj || null,  // 添加KDJ数据
                        macd: stock.macd || null  // 添加MACD数据
                    };
                    
                    // 创建股票卡片 - 确保方法存在
                    let stockCard;
                    if (typeof window.FrontendStocks.createStockCard === 'function') {
                        stockCard = window.FrontendStocks.createStockCard(standardizedStock);
                    } else {
                        // 如果createStockCard不存在，使用通用创建卡片功能
                        stockCard = createStockCard(standardizedStock);
                    }
                    
                    stockGrid.appendChild(stockCard);
                });
                
                stockList.appendChild(stockGrid);
                return;
            } catch (error) {
                console.error('前端模式加载股票失败:', error);
                stockList.innerHTML = `<div class="error-message">加载失败: ${error.message}</div>`;
                return;
            }
        }
        
        // 后端模式加载股票
        fetch('api/get_user_stocks.php')
        .then(response => {
            console.log('股票API响应状态: ', response.status);
            console.log('股票API响应头: ', {
                'content-type': response.headers.get('content-type')
            });
            
            // 如果服务器返回失败但前端模式可用，自动切换
            if (response.status !== 200 && window.FrontendStocks && typeof window.FrontendStocks.isEnabled === 'function') {
                console.log('服务器API失败，自动切换到前端模式');
                window.FrontendStocks.enable();
                showNotification('服务器连接失败，已切换到前端存储模式', 'info');
                // 递归调用自身，这次会使用前端模式
                loadUserStocks();
                return null;
            }
            
            return response.text(); // 先获取文本内容以便调试
        })
        .then(text => {
            // 如果前面已处理则跳过
            if (text === null) return null;
            
            console.log('股票API原始响应: ', text);
            
            // 检查响应是否为空
            if (!text || text.trim() === '') {
                throw new Error('服务器返回了空响应');
            }
            
            // 使用提取JSON方法处理可能混合了HTML的响应
            return extractJSON(text);
        })
        .then(data => {
            // 如果前面已处理则跳过
            if (data === null) return;
            
            console.log('解析后的股票数据: ', data);
            stockList.innerHTML = '';
            
            if (!data) {
                throw new Error('解析后的数据为空');
            }
            
            if (data.success && data.data && data.data.length > 0) {
                const stockGrid = document.createElement('div');
                stockGrid.className = 'stock-grid';
                
                data.data.forEach(stock => {
                    console.log('处理股票: ', stock);
                    // 标准化字段
                    const standardizedStock = {
                        code: stock.code || stock.stock_id || '',
                        name: stock.name || '未知股票',
                        price: stock.price || stock.current_price || '0.00',
                        change: stock.change || stock.price_change || '0.00',
                        // 添加其他可能需要的字段默认值
                        open: stock.open || '0.00',
                        close: stock.close || '0.00',
                        high: stock.high || '0.00',
                        low: stock.low || '0.00',
                        // 添加MA数据
                        ma5: stock.ma5 || '-',
                        ma10: stock.ma10 || '-',
                        ma20: stock.ma20 || '-',
                        volumeRatio: stock.volumeRatio || '0.00',
                        kdj: stock.kdj || null,  // 添加KDJ数据
                        macd: stock.macd || null  // 添加MACD数据
                    };
                    
                    const stockCard = createStockCard(standardizedStock);
                    stockGrid.appendChild(stockCard);
                });
                
                stockList.appendChild(stockGrid);
            } else {
                stockList.innerHTML = '<div class="empty-message">暂无添加的股票，请先搜索并添加股票</div>';
            }
        })
        .catch(error => {
            console.error('加载股票列表错误:', error);
            
            // 如果后端API调用失败但前端模式可用，自动切换到前端模式
            if (window.FrontendStocks && typeof window.FrontendStocks.isEnabled === 'function') {
                console.log('后端API错误，尝试切换到前端模式');
                window.FrontendStocks.enable();
                showNotification('服务器错误，已切换到前端存储模式', 'info');
                // 递归调用自身，这次会使用前端模式
                loadUserStocks();
                return;
            }
            
            stockList.innerHTML = `<div class="error-message">加载失败: ${error.message}</div>`;
        });
    }
}

/**
 * 创建股票卡片元素
 */
function createStockCard(stock) {
    const stockCard = document.createElement('div');
    stockCard.className = 'stock-card';
    
    // 为了与数据库结构兼容，适应不同的字段名
    const stockName = stock.stock_name || stock.name || '';
    const stockCode = stock.stock_symbol || stock.code || stock.symbol || '';
    const stockId = stock.id || '0';
    
    // 设置data-code属性，用于删除时查找元素
    stockCard.setAttribute('data-code', stockCode);
    
    // 价格相关数据可能不存在，提供默认值
    const stockPrice = stock.price || stock.current_price || '0.00';
    const priceChange = stock.change || stock.price_change || 0;
    const openPrice = stock.open || stock.open_price || '0.00';
    const prevClose = stock.close || stock.prev_close || '0.00';
    const highPrice = stock.high || stock.high_price || '0.00';
    const lowPrice = stock.low || stock.low_price || '0.00';
    // 均线数据
    const ma5 = stock.ma5 || '-';
    const ma10 = stock.ma10 || '-';
    const ma20 = stock.ma20 || '-';
    
    // 判断价格变化是正是负
    const isPositive = parseFloat(priceChange) >= 0;
    const changeClass = isPositive ? 'positive' : 'negative';
    const changeSign = isPositive ? '+' : '';
    
    stockCard.innerHTML = `
        <div class="stock-header">
            <h4>${stockName} <span class="stock-symbol">${stockCode}</span></h4>
        </div>
        
        <div class="stock-price">
            <div class="price">${stockPrice}</div>
            <div class="change ${changeClass}">
                ${changeSign}${priceChange}%
            </div>
        </div>
        
        <div class="stock-info">
            <div class="info-row">
                <div class="info-item">
                    <span class="label">最高</span>
                    <span class="value">${highPrice}</span>
                </div>
                <div class="info-item">
                    <span class="label">最低</span>
                    <span class="value">${lowPrice}</span>
                </div>
            </div>
            <div class="info-row">
                <div class="info-item">
                    <span class="label">MA5</span>
                    <span class="value">${stock.ma5 !== undefined ? stock.ma5 : '-'}</span>
                </div>
                <div class="info-item">
                    <span class="label">MA10</span>
                    <span class="value">${stock.ma10 !== undefined ? stock.ma10 : '-'}</span>
                </div>
                <div class="info-item">
                    <span class="label">MA20</span>
                    <span class="value">${stock.ma20 !== undefined ? stock.ma20 : '-'}</span>
                </div>
            </div>
        </div>

        <div class="stock-actions">
            <button class="btn btn-primary analyze-btn" data-symbol="${stockCode}">分析</button>
            <button class="btn btn-secondary query-btn" data-symbol="${stockCode}">咨询</button>
            <button class="btn btn-secondary update-btn" data-code="${stockCode}">刷新</button>
            <button class="btn btn-danger btn-icon remove-btn" data-code="${stockCode}">×</button>
        </div>
    `;
    
    // 绑定分析按钮事件
    const analyzeBtn = stockCard.querySelector('.analyze-btn');
    if (analyzeBtn) {
        analyzeBtn.addEventListener('click', function() {
            const symbol = this.getAttribute('data-symbol');
            if (symbol) {
                const stockName = stockNameMap[symbol] || "未知股票";
                analyzeStock(symbol, stockName);
            }
        });
    }
    
    // 绑定咨询按钮事件
    const queryBtn = stockCard.querySelector('.query-btn');
    if (queryBtn) {
        queryBtn.addEventListener('click', function() {
            const symbol = this.getAttribute('data-symbol');
            if (symbol) {
                const chatContainer = document.getElementById('chatContainer');
                if (!chatContainer) {
                    console.error('找不到聊天容器');
                    return;
                }
                
                // 清空之前的聊天内容
                chatContainer.innerHTML = '';
                
                // 先聚焦到聊天区域
                chatContainer.scrollIntoView({ behavior: 'smooth' });
                
                // 添加用户消息
                addChatMessage(`请告诉我关于${stockName}(${symbol})的信息`, 'user');
                
                // 添加AI回复 - 分析中状态
                addChatMessage(`正在分析${stockName}(${symbol})的数据...`, 'ai');
                
                // 检查API密钥
                const apiKey = localStorage.getItem('api_key');
                if (!apiKey) {
                    // 如果没有API密钥，尝试从DOM获取
                    const apiKeyInput = document.getElementById('apiKey');
                    if (apiKeyInput && apiKeyInput.value.trim()) {
                        localStorage.setItem('api_key', apiKeyInput.value.trim());
                    } else {
                        // 更新"分析中"消息，而不是移除
                        const analyzingMsg = chatContainer.querySelector('.analyzing');
                        if (analyzingMsg) {
                            const content = analyzingMsg.querySelector('.message-content');
                            if (content) {
                                content.innerHTML = "无法获取API密钥，请先在API密钥设置中保存有效的密钥";
                            }
                            analyzingMsg.classList.remove('analyzing');
                        }
                        return;
                    }
                }
                
                // 获取股票实时数据再发送请求
                if (window.FrontendStocks && typeof window.FrontendStocks.fetchStockRealTimeData === 'function') {
                    window.FrontendStocks.fetchStockRealTimeData(symbol)
                        .then(stockData => {
                            // 格式化股票数据为中文
                            let stockInfo = "";
                            if (stockData && Object.keys(stockData).length > 0) {
                                stockInfo = formatStockDataToChinese(stockData);
                                console.log('获取到股票实时数据:', stockInfo);
                            }
                            
                            // 发送带有实时数据的请求
                            sendStockInfoRequest(symbol, stockName, stockInfo);
                        })
                        .catch(error => {
                            console.error('获取股票数据失败:', error);
                            // 忽略错误，直接发送不带实时数据的请求
                            sendStockInfoRequest(symbol, stockName, "");
                        });
                } else {
                    // 无法获取实时数据，直接发送基本请求
                    sendStockInfoRequest(symbol, stockName, "");
                }
            }
        });
    }
    
    // 绑定更新按钮事件
    const updateBtn = stockCard.querySelector('.update-btn');
    if (updateBtn) {
        updateBtn.addEventListener('click', function() {
            const code = this.getAttribute('data-code');
            if (code) {
                // 显示更新中状态
                this.disabled = true;
                this.textContent = '更新中';
                
                // 使用FrontendStocks获取最新数据
                if (window.FrontendStocks && typeof window.FrontendStocks.fetchStockRealTimeData === 'function') {
                    window.FrontendStocks.fetchStockRealTimeData(code)
                        .then(stockData => {
                            console.log('获取到最新数据:', stockData);
                            
                            // fetchStockRealTimeData已内部调用updateStock方法更新UI
                            // 无需在此手动更新卡片
                            
                            // 恢复按钮状态
                            this.disabled = false;
                            this.textContent = '刷新';
                            
                            // 显示成功通知
                            showNotification('数据已更新', 'success');
                        })
                        .catch(error => {
                            console.error('获取股票数据失败:', error);
                            // 恢复按钮状态
                            this.disabled = false;
                            this.textContent = '刷新';
                            showNotification('更新失败: ' + error.message, 'error');
                        });
                } else {
                    // 如果没有FrontendStocks，使用简单的模拟更新
                    setTimeout(() => {
                        this.disabled = false;
                        this.textContent = '刷新';
                        showNotification('数据已更新', 'success');
                    }, 1000);
                }
            }
        });
    }
    
    // 绑定删除按钮事件
    const removeBtn = stockCard.querySelector('.remove-btn');
    if (removeBtn) {
        removeBtn.addEventListener('click', function() {
            const code = this.getAttribute('data-code');
            if (code) {
                removeStock(code);
            }
        });
    }
    
    return stockCard;
}

/**
 * 搜索股票
 */
function searchStock(query) {
    const searchResults = document.getElementById('searchResults');
    
    searchResults.innerHTML = '<div class="loading">搜索中...</div>';
    console.log('开始搜索股票: ' + query);
    
    // 先检查是否为纯数字代码
    const isNumeric = /^\d+$/.test(query);
    
    // 如果不是纯数字，尝试从缓存数据中查找匹配的股票名称
    if (!isNumeric) {
        // 尝试从本地缓存加载股票名称数据
        fetch('cache/stock_names.json')
            .then(response => {
                if (!response.ok) {
                    throw new Error('无法加载股票数据缓存');
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('解析缓存文件失败:', e);
                        throw e;
                    }
                });
            })
            .then(stockNameData => {
                console.log('成功加载股票名称缓存', Array.isArray(stockNameData) ? stockNameData.length : 0 + '条记录');
                
                // 根据名称搜索匹配的股票
                const matchedStocks = [];
                
                // 遍历股票数据查找名称相似的股票
                if (Array.isArray(stockNameData)) {
                    for (const stock of stockNameData) {
                        // 兼容两种字段格式：mc或name
                        const stockName = stock.mc || stock.name || '';
                        if (stockName && stockName.includes(query)) {
                            matchedStocks.push({
                                code: stock.dm || stock.code || '',
                                name: stockName,
                                price: '0.00',  // 默认价格，后面可以通过API更新
                                change: '0.00'  // 默认变化，后面可以通过API更新
                            });
                            
                            // 限制最多显示10个结果
                            if (matchedStocks.length >= 10) {
                                break;
                            }
                        }
                    }
                }
                
                // 如果找到匹配的股票名称
                if (matchedStocks.length > 0) {
                    console.log('通过名称匹配到股票:', matchedStocks);
                    displaySearchResults(matchedStocks);
                    
                    // 获取实时价格数据
                    for (const stock of matchedStocks) {
                        fetchStockData(stock.code);
                    }
                } else {
                    // 没有匹配的名称，尝试使用API搜索
                    fetchStockAPI(query);
                }
            })
            .catch(error => {
                console.error('加载股票名称缓存失败:', error);
                // 回退到API搜索
                fetchStockAPI(query);
            });
    } else {
        // 如果是纯数字代码，直接使用API搜索
        fetchStockAPI(query);
    }
    
    // 通过API搜索股票
    function fetchStockAPI(query) {
        fetch('api/search_stock.php?keyword=' + encodeURIComponent(query), {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            console.log('搜索API响应状态: ', response.status);
            return response.text(); // 先获取文本内容以便调试
        })
        .then(text => {
            console.log('搜索API原始响应: ', text);
            
            // 检查响应是否为空
            if (!text || text.trim() === '') {
                throw new Error('服务器返回了空响应');
            }
            
            try {
                // 尝试直接解析JSON
                return JSON.parse(text);
            } catch (e) {
                console.error('JSON解析错误，尝试提取JSON部分: ', e);
                // 使用提取JSON方法处理可能混合了HTML的响应
                return extractJSON(text);
            }
        })
        .then(data => {
            console.log('解析后的搜索结果: ', data);
            
            if (!data) {
                throw new Error('解析后的数据为空');
            }
            
            // 尝试从不同格式中提取结果
            let stockList = [];
            if (data.success) {
                if (data.data && Array.isArray(data.data) && data.data.length > 0) {
                    stockList = data.data;
                } else if (data.stocks && Array.isArray(data.stocks) && data.stocks.length > 0) {
                    stockList = data.stocks;
                }
            }
            
            if (stockList.length > 0) {
                // 转换为统一格式
                const formattedStocks = stockList.map(stock => ({
                    code: stock.code || stock.dm || stock.stock_code || '',
                    name: stock.name || stock.mc || stock.stock_name || '',
                    price: stock.price || '0.00',
                    change: stock.change || stock.price_change || '0.00'
                }));
                
                displaySearchResults(formattedStocks);
            } else {
                searchResults.innerHTML = `<div class="empty-message">${data.message || '未找到匹配的股票'}</div>`;
            }
        })
        .catch(error => {
            console.error('搜索股票错误:', error);
            searchResults.innerHTML = `<div class="error-message">搜索失败: ${error.message}</div>`;
        });
    }
    
    // 获取单只股票实时数据
    function fetchStockData(stockCode) {
        // 如果已有真实数据，就不需要再次获取
        const existingResults = document.querySelectorAll(`.search-result-item[data-code="${stockCode}"]`);
        if (existingResults.length > 0 && existingResults[0].getAttribute('data-has-data') === 'true') {
            return;
        }
            
        fetch('api/search_stock.php?keyword=' + encodeURIComponent(stockCode), {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`API响应错误: ${response.status}`);
            }
            return response.text();
        })
        .then(text => {
            if (!text) {
                throw new Error('响应为空');
            }
            
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('JSON解析错误: ', e);
                throw e;
            }
        })
        .then(data => {
            let stockInfo = null;
            if (data.success) {
                if (data.data && data.data.length > 0) {
                    stockInfo = data.data[0];
                } else if (data.stocks && data.stocks.length > 0) {
                    stockInfo = data.stocks[0];
                }
            }
            
            if (stockInfo) {
                // 找到对应的DOM元素并更新
                const stockItem = document.querySelector(`.search-result-item[data-code="${stockCode}"]`);
                if (stockItem) {
                    const priceEl = stockItem.querySelector('.stock-price');
                    if (priceEl) {
                        const price = stockInfo.price || '0.00';
                        const change = stockInfo.change || stockInfo.price_change || '0.00';
                        const isPositive = parseFloat(change || '0') >= 0;
                        priceEl.className = `stock-price ${isPositive ? 'positive' : 'negative'}`;
                        priceEl.innerHTML = `${price} <span>${isPositive ? '+' : ''}${change}%</span>`;
                    }
                    stockItem.setAttribute('data-has-data', 'true');
                }
            }
        })
        .catch(error => {
            console.error('获取股票数据失败:', error);
        });
    }
    
    // 显示搜索结果
    function displaySearchResults(stocksData) {
        searchResults.innerHTML = '';
        
        if (stocksData.length === 0) {
            searchResults.innerHTML = `<div class="empty-message">未找到匹配的股票</div>`;
            return;
        }
        
        // 添加结果来源提示
        const sourceHint = document.createElement('div');
        sourceHint.className = 'search-hint';
        sourceHint.style.marginBottom = '10px';
        sourceHint.style.fontSize = '14px';
        sourceHint.style.color = '#666';
        
        // 通过检查第一个结果的价格判断来源
        const isFromCache = stocksData[0].price === '0.00';
        if (isFromCache) {
            sourceHint.textContent = `找到 ${stocksData.length} 条结果，来自本地数据缓存，正在加载实时数据...`;
        } else {
            sourceHint.textContent = `找到 ${stocksData.length} 条结果，来自API请求`;
        }
        searchResults.appendChild(sourceHint);
        
        const resultList = document.createElement('div');
        resultList.className = 'result-list';
        
        stocksData.forEach(stock => {
            console.log('处理搜索结果: ', stock);
            const resultItem = document.createElement('div');
            resultItem.className = 'search-result-item';
            resultItem.setAttribute('data-code', stock.code);
            resultItem.setAttribute('data-has-data', stock.price !== '0.00');
            
            // 确保股票有name和code属性
            const stockName = stock.name || '未知名称';
            const stockCode = stock.code || query;
            const stockPrice = stock.price || '0.00';
            const priceChange = stock.change || '0.00';
            const isPositive = parseFloat(priceChange) >= 0;
            
            resultItem.innerHTML = `
                <div class="stock-info">
                    <div class="stock-name">${stockName}</div>
                    <div class="stock-code">${stockCode}</div>
                    <div class="stock-price ${isPositive ? 'positive' : 'negative'}">
                        ${stockPrice} <span>${isPositive ? '+' : ''}${priceChange}%</span>
                    </div>
                </div>
                <button class="btn btn-primary add-btn" data-code="${stockCode}" data-name="${stockName}">添加</button>
            `;
            
            resultList.appendChild(resultItem);
        });
        
        searchResults.appendChild(resultList);
        
        // 绑定添加按钮事件 - 使用箭头函数避免 this 上下文丢失
        document.querySelectorAll('.add-btn').forEach(button => {
            button.addEventListener('click', (event) => {
                event.preventDefault();
                event.stopPropagation();
                
                const code = button.getAttribute('data-code');
                const name = button.getAttribute('data-name');
                console.log('添加股票: ', code, name);
                
                // 直接传递 button 元素
                addStock(code, name, button);
            });
        });
    }
}

/**
 * 添加股票
 */
function addStock(code, name, btnElement) {
    console.log(`添加股票: code=${code}, name=${name}`);
    
    // 显示加载状态
    if (btnElement) {
        btnElement.disabled = true;
        btnElement.textContent = '添加中...';
    }
    
    // 检查是否启用了前端模式
    if (window.FrontendStocks && typeof window.FrontendStocks.isEnabled === 'function' && window.FrontendStocks.isEnabled()) {
        console.log('使用前端模式添加股票');
        try {
            // 使用前端存储模式添加股票
            const result = window.FrontendStocks.addStock(code, name);
            
            // 更新按钮状态
            if (btnElement) {
                btnElement.disabled = true;
                btnElement.textContent = '已添加';
                btnElement.classList.add('added');
            }
            
            // 显示通知
            showNotification(result.message || '股票添加成功', 'success');
            return;
        } catch (error) {
            console.error('前端模式添加股票失败:', error);
            
            // 恢复按钮状态
            if (btnElement) {
                btnElement.disabled = false;
                btnElement.textContent = '添加';
            }
            
            showNotification('添加失败: ' + error.message, 'error');
            return;
        }
    }
    
    // 强制启用前端模式
    if (window.FrontendStocks && typeof window.FrontendStocks.enable === 'function') {
        window.FrontendStocks.enable();
    }
    
    // 优先使用前端模式添加股票，避免服务器错误影响用户体验
    if (window.FrontendStocks && typeof window.FrontendStocks.addStock === 'function') {
        console.log('使用前端模式添加股票');
        try {
            // 使用前端存储模式添加股票
            const result = window.FrontendStocks.addStock(code, name);
            
            // 更新按钮状态
            if (btnElement) {
                btnElement.disabled = true;
                btnElement.textContent = '已添加';
                btnElement.classList.add('added');
            }
            
            // 显示通知
            showNotification(result.message || '股票添加成功', 'success');
            
            // 无论是否返回股票数据，都主动刷新列表
            loadUserStocks();
            return;
        } catch (error) {
            console.error('前端模式添加股票失败:', error);
            
            // 恢复按钮状态
            if (btnElement) {
                btnElement.disabled = false;
                btnElement.textContent = '添加';
            }
            
            showNotification('添加失败: ' + error.message, 'error');
            return;
        }
    } else {
        console.warn('前端存储模块不可用，尝试使用后端API');
    }
    
    // 使用正确的数据格式发送请求
    const stockData = {
        stockCode: code,
        stockName: name || '未知名称'
    };
    
    console.log('准备发送的数据:', stockData);
    
    fetch('api/add_stock.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(stockData)
    })
    .then(response => {
        console.log('添加股票响应状态: ', response.status);
        console.log('添加股票响应头: ', {
            'content-type': response.headers.get('content-type'),
            'location': response.headers.get('location')
        });
        
        // 如果服务器返回500错误但前端模式可用，使用前端模式添加
        if (response.status === 500 && window.FrontendStocks && typeof window.FrontendStocks.isEnabled === 'function') {
            console.log('服务器出错，切换到前端模式');
            window.FrontendStocks.enable();
            const result = window.FrontendStocks.addStock(code, name);
            
            if (btnElement) {
                btnElement.disabled = true;
                btnElement.textContent = '已添加';
                btnElement.classList.add('added');
            }
            
            showNotification('服务器出错，已自动切换到前端模式并添加成功', 'success');
            return null; // 不继续处理响应
        }
        
        return response.text();
    })
    .then(text => {
        // 如果前面已经处理了返回null，跳过后续处理
        if (text === null) return null;
        
        console.log('添加股票API原始响应: ', text);
        
        // 如果响应为空
        if (!text || text.trim() === '') {
            throw new Error('服务器返回了空响应');
        }
        
        try {
            return JSON.parse(text);
        } catch (e) {
            console.error('添加股票JSON解析错误: ', e, text);
            // 如果无法解析JSON，抛出错误
            throw new Error('服务器返回了无效的JSON数据: ' + text.substring(0, 100));
        }
    })
    .then(data => {
        // 如果前面已经处理了返回null，跳过后续处理
        if (data === null) return;
        
        console.log('添加股票解析后的响应: ', data);
        
        // 重置按钮状态
        if (btnElement) {
            btnElement.disabled = false;
            if (data.success && (data.already_exists || true)) {
                btnElement.textContent = '已添加';
                btnElement.classList.add('added');
                btnElement.disabled = true;
            } else {
                btnElement.textContent = '添加';
            }
        }
        
        if (data.success) {
            // 显示成功消息
            showNotification(data.message || '股票添加成功', 'success');
            
            // 无论是否返回股票数据，都主动刷新列表
            loadUserStocks();
        } else {
            showNotification(data.message || '添加失败', 'error');
            console.error('添加股票失败: ', data.message);
        }
    })
    .catch(error => {
        console.error('添加股票错误:', error);
        
        // 重置按钮状态
        if (btnElement) {
            btnElement.disabled = false;
            btnElement.textContent = '添加';
        }
        
        // 如果服务器请求失败，尝试使用前端模式
        if (window.FrontendStocks && typeof window.FrontendStocks.isEnabled === 'function') {
            console.log('服务器请求失败，尝试使用前端模式');
            window.FrontendStocks.enable();
            try {
                const result = window.FrontendStocks.addStock(code, name);
                showNotification('已切换到前端模式并添加成功', 'success');
                
                if (btnElement) {
                    btnElement.disabled = true;
                    btnElement.textContent = '已添加';
                    btnElement.classList.add('added');
                }
                
                return;
            } catch (e) {
                console.error('前端模式添加也失败:', e);
            }
        }
        
        showNotification('添加失败: ' + error.message, 'error');
    });
}

/**
 * 删除股票
 */
function removeStock(stockCode) {
    if (!confirm(`确定要删除这支股票吗？`)) {
        return;
    }
    
    console.log('准备删除股票: ' + stockCode);
    
    fetch('api/remove_stock.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify({ code: stockCode })
    })
    .then(response => response.text())
    .then(text => {
        try {
            return JSON.parse(text);
        } catch (e) {
            console.error('删除股票JSON解析错误: ', e, text);
            // 尝试从混合响应中提取JSON
            return extractJSON(text);
        }
    })
    .then(data => {
        console.log('删除股票响应: ', data);
        if (data.success) {
            showNotification('股票删除成功', 'success');
            
            // 从DOM中移除股票卡片
            const stockCard = document.querySelector(`.stock-card[data-code="${stockCode}"]`);
            if (stockCard) {
                stockCard.remove();
            } else {
                // 如果找不到股票卡片，重新加载列表
                loadUserStocks();
            }
        } else {
            showNotification(data.message || '删除失败', 'error');
        }
    })
    .catch(error => {
        console.error('删除股票错误:', error);
        showNotification('删除失败: ' + error.message, 'error');
    });
}

/**
 * 分析股票
 */
function analyzeStock(symbol, stockName) {
    // 创建或清空聊天区域
    const chatContainer = document.getElementById('chatContainer');
    if (!chatContainer) {
        console.error('找不到聊天容器');
        return;
    }
    
    // 清空之前的聊天内容
    chatContainer.innerHTML = '';
    
    // 先聚焦到聊天区域
    chatContainer.scrollIntoView({ behavior: 'smooth' });
    
    // 添加用户消息
    addChatMessage(`请帮我分析${stockName}(${symbol})的走势`, 'user');
    
    // 添加AI回复 - 分析中状态
    addChatMessage(`正在分析${stockName}(${symbol})的走势...`, 'ai');
    
    // 检查API密钥
    const apiKey = localStorage.getItem('api_key');
    if (!apiKey) {
        // 如果没有API密钥，尝试从DOM获取
        const apiKeyInput = document.getElementById('apiKey');
        if (apiKeyInput && apiKeyInput.value.trim()) {
            localStorage.setItem('api_key', apiKeyInput.value.trim());
        } else {
            // 更新"分析中"消息，而不是移除
            const analyzingMsg = chatContainer.querySelector('.analyzing');
            if (analyzingMsg) {
                const content = analyzingMsg.querySelector('.message-content');
                if (content) {
                    content.innerHTML = "无法获取API密钥，请先在API密钥设置中保存有效的密钥";
                }
                analyzingMsg.classList.remove('analyzing');
            }
            return;
        }
    }
    
    // 获取股票实时数据再发送请求
    if (window.FrontendStocks && typeof window.FrontendStocks.fetchStockRealTimeData === 'function') {
        window.FrontendStocks.fetchStockRealTimeData(symbol)
            .then(stockData => {
                // 格式化股票数据为中文
                let stockInfo = "";
                if (stockData && Object.keys(stockData).length > 0) {
                    stockInfo = formatStockDataToChinese(stockData);
                    console.log('获取到股票实时数据:', stockInfo);
                }
                
                // 发送带有实时数据的分析请求
                sendAnalysisRequest(symbol, stockName, stockInfo);
            })
            .catch(error => {
                console.error('获取股票数据失败:', error);
                // 忽略错误，直接发送不带实时数据的请求
                sendAnalysisRequest(symbol, stockName, "");
            });
    } else {
        // 无法获取实时数据，直接发送基本请求
        sendAnalysisRequest(symbol, stockName, "");
    }
}

/**
 * 发送股票分析请求
 */
function sendAnalysisRequest(symbol, name, stockInfo) {
    const chatContainer = document.getElementById('chatContainer');
    
    // 构建包含实时数据的分析请求消息
    const baseMessage = `请帮我分析${name}(${symbol})的走势`;
    const fullMessage = stockInfo ? `${baseMessage}\n\n实时数据：${stockInfo}` : baseMessage;
    
    console.log('发送分析请求:', fullMessage);
    
    // 发送请求
    setTimeout(() => {
        fetch('api/send_message.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                message: fullMessage,
                api_key: localStorage.getItem('api_key') || ''
            })
        })
        .then(response => response.json())
        .then(data => {
            // 更新"分析中"消息，而不是移除
            const analyzingMsg = chatContainer.querySelector('.analyzing');
            if (analyzingMsg) {
                const content = analyzingMsg.querySelector('.message-content');
                if (content) {
                    // 使用marked.js渲染Markdown
                    if (window.marked) {
                        content.innerHTML = marked.parse(data.data || data.response);
                    } else {
                        content.innerHTML = (data.data || data.response).replace(/\n/g, '<br>');
                    }
                    content.classList.add('markdown-content');
                }
                analyzingMsg.classList.remove('analyzing');
            } else {
                // 如果没有找到分析中消息，添加新的AI回复
                addChatMessage(data.data || data.response, 'ai');
            }
            
            // 显示分析完成通知
            if (typeof window.showAnalysisNotification === 'function' && symbol && name) {
                window.showAnalysisNotification(name, symbol);
            }
            
            // 滚动到底部
            chatContainer.scrollTop = chatContainer.scrollHeight;
        })
        .catch(error => {
            console.error('Error:', error);
            // 更新"分析中"消息
            const analyzingMsg = chatContainer.querySelector('.analyzing');
            if (analyzingMsg) {
                const content = analyzingMsg.querySelector('.message-content');
                if (content) {
                    content.innerHTML = '请求失败，请稍后再试';
                }
                analyzingMsg.classList.remove('analyzing');
            } else {
                addChatMessage('请求失败，请稍后再试', 'ai');
            }
            chatContainer.scrollTop = chatContainer.scrollHeight;
        });
    }, 100); // 短暂延迟确保滚动完成
}

/**
 * 添加聊天消息
 */
function addChatMessage(message, type, isWaiting = false) {
    const chatContainer = document.getElementById('chatContainer');
    
    // 如果是AI消息且消息包含"量化完成"，则先移除所有正在量化中的消息
    if (type === 'ai' && message.includes('量化完成')) {
        const analyzingMessages = chatContainer.querySelectorAll('.analyzing');
        analyzingMessages.forEach(msg => msg.remove());
    }
    
    const messageEl = document.createElement('div');
    messageEl.className = `message ${type}-message`;
    
    // 如果是等待状态，添加analyzing类
    if (isWaiting) {
        messageEl.classList.add('analyzing');
    }
    
    const contentEl = document.createElement('div');
    contentEl.className = 'message-content';
    
    // 如果是AI消息且需要显示等待状态，则添加等待的提示
    if (type === 'ai' && isWaiting) {
        contentEl.innerHTML = message;
    } else if (type === 'ai' && message === '量化中') {
        messageEl.classList.add('analyzing');
        contentEl.innerHTML = '<div class="loading-dots">量化中<span>.</span><span>.</span><span>.</span></div>';
    } else {
        // 对AI消息进行处理
        if (type === 'ai') {
            try {
                // 检查消息是否已经包含HTML标签（如量化完成提示）
                if (message.includes('<div class="analysis-complete">') || message.indexOf('<') !== -1 && message.indexOf('>') !== -1) {
                    // 消息已包含HTML，直接使用
                    contentEl.innerHTML = message;
                } else if (window.marked) {
                    // 使用marked.js渲染Markdown
                    contentEl.innerHTML = marked.parse(message);
                    contentEl.classList.add('markdown-content');
                } else {
                    // 如果marked库不可用，保留换行
                    contentEl.innerHTML = message.replace(/\n/g, '<br>');
                }
            } catch (e) {
                console.error('Markdown渲染失败:', e);
                contentEl.textContent = message;
            }
        } else {
            // 用户消息直接使用文本
            contentEl.textContent = message;
        }
    }
    
    messageEl.appendChild(contentEl);
    chatContainer.appendChild(messageEl);
    
    // 滚动到底部
    chatContainer.scrollTop = chatContainer.scrollHeight;
}

/**
 * 绑定表单提交事件
 */
function bindFormSubmitEvents() {
    // 搜索按钮点击事件
    const searchButton = document.getElementById('searchButton');
    if (searchButton) {
        searchButton.addEventListener('click', function() {
            const query = document.getElementById('searchInput').value.trim();
            if (query) {
                searchStock(query);
            }
        });
    }
    
    // 推荐股票按钮点击事件
    const recommendButton = document.getElementById('recommendButton');
    if (recommendButton) {
        recommendButton.addEventListener('click', function() {
            // 清空旧消息，显示一条分析中的消息
            const chatContainer = document.getElementById('chatContainer');
            if (chatContainer) {
                // 清空之前的聊天内容
                chatContainer.innerHTML = '';
                
                // 聚焦到聊天区域
                chatContainer.scrollIntoView({ behavior: 'smooth' });
                
                // 显示等待信息
                addChatMessage("正在生成推荐信息,请稍等....", 'ai', true);
                
                // 获取API密钥
                let userApiKey = '';
                // 尝试从数据属性获取API密钥
                const apiKeyElement = document.querySelector('.api-key-display');
                if (apiKeyElement && apiKeyElement.dataset.apiKey) {
                    userApiKey = apiKeyElement.dataset.apiKey;
                } else {
                    // 从localStorage获取
                    userApiKey = localStorage.getItem('api_key');
                }
                
                if (!userApiKey) {
                    // 更新等待消息
                    const waitingMsg = chatContainer.querySelector('.analyzing');
                    if (waitingMsg) {
                        const content = waitingMsg.querySelector('.message-content');
                        if (content) {
                            content.innerHTML = "无法获取API密钥，请先设置有效的API密钥";
                        }
                        waitingMsg.classList.remove('analyzing');
                    }
                    return;
                }
                
                // 先调用track_api_usage.php扣除调用次数
                fetch('/api/track_api_usage.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ api_key: userApiKey })
                })
                .then(response => response.json())
                .then(trackData => {
                    // 如果跟踪API返回错误（如次数用尽），显示错误信息
                    if (!trackData.success) {
                        const waitingMsg = chatContainer.querySelector('.analyzing');
                        if (waitingMsg) {
                            const content = waitingMsg.querySelector('.message-content');
                            if (content) {
                                content.innerHTML = trackData.message || "调用次数不足，请联系管理员充值";
                            }
                            waitingMsg.classList.remove('analyzing');
                        }
                        
                        // 更新UI上显示的剩余次数
                        const remainingElement = document.querySelector('.remaining-requests span');
                        if (remainingElement) {
                            remainingElement.textContent = trackData.remaining || "0";
                        }
                        return;
                    }
                    
                    // 更新UI上显示的剩余次数
                    const remainingElement = document.querySelector('.remaining-requests span');
                    if (remainingElement) {
                        remainingElement.textContent = trackData.remaining;
                    }
                    
                    // 使用send_message.php发送推荐请求
                    fetch('api/send_message.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ 
                            message: "请为我推荐3支值得关注的股票",
                            api_key: userApiKey
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新"分析中"消息，而不是移除
                            const analyzingMsg = chatContainer.querySelector('.analyzing');
                            if (analyzingMsg) {
                                const content = analyzingMsg.querySelector('.message-content');
                                if (content) {
                                    // 使用marked.js渲染Markdown
                                    if (window.marked) {
                                        content.innerHTML = marked.parse(data.data || data.response);
                                    } else {
                                        content.innerHTML = (data.data || data.response).replace(/\n/g, '<br>');
                                    }
                                    content.classList.add('markdown-content');
                                }
                                analyzingMsg.classList.remove('analyzing');
                            } else {
                                // 如果没有找到分析中消息，添加新的AI回复
                                addChatMessage(data.data || data.response, 'ai');
                            }
                        } else {
                            // 更新"分析中"消息
                            const analyzingMsg = chatContainer.querySelector('.analyzing');
                            if (analyzingMsg) {
                                const content = analyzingMsg.querySelector('.message-content');
                                if (content) {
                                    content.innerHTML = data.message || '推荐失败';
                                }
                                analyzingMsg.classList.remove('analyzing');
                            } else {
                                addChatMessage(data.message || '推荐失败', 'ai');
                            }
                        }
                        
                        // 滚动到底部
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // 更新"分析中"消息
                        const analyzingMsg = chatContainer.querySelector('.analyzing');
                        if (analyzingMsg) {
                            const content = analyzingMsg.querySelector('.message-content');
                            if (content) {
                                content.innerHTML = '请求失败，请稍后再试';
                            }
                            analyzingMsg.classList.remove('analyzing');
                        } else {
                            addChatMessage('请求失败，请稍后再试', 'ai');
                        }
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                    });
                })
                .catch(error => {
                    console.error('调用次数扣除失败:', error);
                    // 更新"分析中"消息
                    const analyzingMsg = chatContainer.querySelector('.analyzing');
                    if (analyzingMsg) {
                        const content = analyzingMsg.querySelector('.message-content');
                        if (content) {
                            content.innerHTML = '调用次数扣除失败，请稍后再试';
                        }
                        analyzingMsg.classList.remove('analyzing');
                    } else {
                        addChatMessage('调用次数扣除失败，请稍后再试', 'ai');
                    }
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                });
            }
        });
    }
    
    // 搜索输入框回车事件
    const searchInput = document.getElementById('searchInput');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const query = this.value.trim();
                if (query) {
                    searchStock(query);
                }
            }
        });
    }
    
    // 存储模式开关
    const storageMode = document.getElementById('storageMode');
    if (storageMode) {
        storageMode.addEventListener('change', function() {
            if (this.checked) {
                // 启用前端模式
                if (window.FrontendStocks && typeof window.FrontendStocks.enable === 'function') {
                    window.FrontendStocks.enable();
                } else {
                    // 直接设置localStorage
                    localStorage.setItem('use_frontend_storage', 'true');
                    showNotification('前端存储模式已启用', 'success');
                }
            } else {
                // 禁用前端模式
                if (window.FrontendStocks && typeof window.FrontendStocks.disable === 'function') {
                    window.FrontendStocks.disable();
                } else {
                    // 直接设置localStorage
                    localStorage.setItem('use_frontend_storage', 'false');
                    showNotification('前端存储模式已禁用', 'info');
                }
            }
            
            // 无论何种情况，都刷新股票列表
            setTimeout(loadUserStocks, 300);
        });
    }
    
    // 聊天表单
    const chatForm = document.getElementById('chatForm');
    if (chatForm) {
        chatForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const userInput = document.getElementById('userInput');
            const message = userInput.value.trim();
            
            if (message) {
                // 添加用户消息
                const chatContainer = document.getElementById('chatContainer');
                addChatMessage(message, 'user');
                
                // 添加分析中的提示消息
                addChatMessage("正在分析您的问题，请稍等...", 'ai');
                
                // 清空输入框
                userInput.value = '';
                
                // 检查API密钥
                const apiKey = localStorage.getItem('api_key');
                if (!apiKey) {
                    // 如果没有API密钥，尝试从DOM获取
                    const apiKeyInput = document.getElementById('apiKey');
                    if (apiKeyInput && apiKeyInput.value.trim()) {
                        localStorage.setItem('api_key', apiKeyInput.value.trim());
                    } else {
                        // 更新"分析中"消息，而不是移除
                        const analyzingMsg = chatContainer.querySelector('.analyzing');
                        if (analyzingMsg) {
                            const content = analyzingMsg.querySelector('.message-content');
                            if (content) {
                                content.innerHTML = "无法获取API密钥，请先在API密钥设置中保存有效的密钥";
                            }
                            analyzingMsg.classList.remove('analyzing');
                        }
                        return;
                    }
                }
                
                // 发送请求
                fetch('api/send_message.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        message: message,
                        api_key: localStorage.getItem('api_key') || ''
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新"分析中"消息，而不是移除
                        const analyzingMsg = chatContainer.querySelector('.analyzing');
                        if (analyzingMsg) {
                            const content = analyzingMsg.querySelector('.message-content');
                            if (content) {
                                // 使用marked.js渲染Markdown
                                if (window.marked) {
                                    content.innerHTML = marked.parse(data.data || data.response);
                                } else {
                                    content.innerHTML = (data.data || data.response).replace(/\n/g, '<br>');
                                }
                                content.classList.add('markdown-content');
                            }
                            analyzingMsg.classList.remove('analyzing');
                        } else {
                            // 如果没有找到分析中消息，添加新的AI回复
                            addChatMessage(data.data || data.response, 'ai');
                        }
                    } else {
                        // 更新"分析中"消息
                        const analyzingMsg = chatContainer.querySelector('.analyzing');
                        if (analyzingMsg) {
                            const content = analyzingMsg.querySelector('.message-content');
                            if (content) {
                                content.innerHTML = data.message || '请求失败';
                            }
                            analyzingMsg.classList.remove('analyzing');
                        } else {
                            addChatMessage(data.message || '请求失败', 'ai');
                        }
                    }
                    
                    // 滚动到底部
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 更新"分析中"消息
                    const analyzingMsg = chatContainer.querySelector('.analyzing');
                    if (analyzingMsg) {
                        const content = analyzingMsg.querySelector('.message-content');
                        if (content) {
                            content.innerHTML = '请求失败，请稍后再试';
                        }
                        analyzingMsg.classList.remove('analyzing');
                    } else {
                        addChatMessage('请求失败，请稍后再试', 'ai');
                    }
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                });
            }
        });
    }
    
    // API许可证表单
    const licenceForm = document.getElementById('licenceForm');
    if (licenceForm) {
        licenceForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const licenceInput = document.getElementById('licenceInput');
            const licence = licenceInput.value.trim();
            
            if (licence) {
                addLicence(licence);
                licenceInput.value = '';
            }
        });
    }

    // 咨询按钮点击事件
    const consultButton = document.getElementById('consultButton');
    if (consultButton) {
        consultButton.addEventListener('click', function() {
            const messageBox = document.getElementById('messageBox');
            const message = messageBox.value.trim();
            if (message) {
                // 清空之前的消息
                const chatContainer = document.getElementById('chatContainer');
                if (chatContainer) {
                    chatContainer.innerHTML = '';
                }

                // 添加用户消息
                addChatMessage(message, 'user');
                
                // 显示分析中消息
                addChatMessage("正在分析您的问题，请稍等...", 'ai');
                
                // 清空输入框并聚焦
                messageBox.value = '';
                messageBox.focus();
                
                // 检查API密钥
                const apiKey = localStorage.getItem('api_key');
                if (!apiKey) {
                    // 如果没有API密钥，尝试从DOM获取
                    const apiKeyInput = document.getElementById('apiKey');
                    if (apiKeyInput && apiKeyInput.value.trim()) {
                        localStorage.setItem('api_key', apiKeyInput.value.trim());
                    } else {
                        // 更新"分析中"消息，而不是移除
                        const analyzingMsg = chatContainer.querySelector('.analyzing');
                        if (analyzingMsg) {
                            const content = analyzingMsg.querySelector('.message-content');
                            if (content) {
                                content.innerHTML = "无法获取API密钥，请先在API密钥设置中保存有效的密钥";
                            }
                            analyzingMsg.classList.remove('analyzing');
                        }
                        return;
                    }
                }
                
                // 延迟确保滚动完成
                setTimeout(() => {
                    fetch('api/send_message.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ 
                            message: message,
                            api_key: localStorage.getItem('api_key') || ''
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新"分析中"消息，而不是移除
                            const analyzingMsg = chatContainer.querySelector('.analyzing');
                            if (analyzingMsg) {
                                const content = analyzingMsg.querySelector('.message-content');
                                if (content) {
                                    // 使用marked.js渲染Markdown
                                    if (window.marked) {
                                        content.innerHTML = marked.parse(data.data || data.response);
                                    } else {
                                        content.innerHTML = (data.data || data.response).replace(/\n/g, '<br>');
                                    }
                                    content.classList.add('markdown-content');
                                }
                                analyzingMsg.classList.remove('analyzing');
                            } else {
                                // 如果没有找到分析中消息，添加新的AI回复
                                addChatMessage(data.data || data.response, 'ai');
                            }
                        } else {
                            // 更新"分析中"消息
                            const analyzingMsg = chatContainer.querySelector('.analyzing');
                            if (analyzingMsg) {
                                const content = analyzingMsg.querySelector('.message-content');
                                if (content) {
                                    content.innerHTML = data.message || '咨询失败';
                                }
                                analyzingMsg.classList.remove('analyzing');
                            } else {
                                addChatMessage(data.message || '咨询失败', 'ai');
                            }
                        }
                        
                        // 滚动到底部
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        // 更新"分析中"消息
                        const analyzingMsg = chatContainer.querySelector('.analyzing');
                        if (analyzingMsg) {
                            const content = analyzingMsg.querySelector('.message-content');
                            if (content) {
                                content.innerHTML = '请求失败，请稍后再试';
                            }
                            analyzingMsg.classList.remove('analyzing');
                        } else {
                            addChatMessage('请求失败，请稍后再试', 'ai');
                        }
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                    });
                }, 100);
            }
        });
    }
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // 自动消失
    setTimeout(() => {
        notification.classList.add('fade-out');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 3000);
}

/**
 * 提取JSON数据
 * 从可能混合其他内容的响应中提取JSON部分
 */
function extractJSON(text) {
    if (!text || typeof text !== 'string') {
        console.error('无效输入: 不是字符串');
        return null;
    }
    
    try {
        // 先尝试直接解析整个文本
        return JSON.parse(text);
    } catch (e) {
        console.log('尝试从混合响应中提取JSON');
        
        // 查找可能的JSON开始和结束位置
        const firstBrace = text.indexOf('{');
        const lastBrace = text.lastIndexOf('}');
        
        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
            try {
                // 提取并解析JSON部分
                const jsonPart = text.substring(firstBrace, lastBrace + 1);
                return JSON.parse(jsonPart);
            } catch (e2) {
                console.error('JSON提取失败:', e2);
            }
        }
        
        // 如果以上方法都失败，尝试查找JSON数组
        const firstBracket = text.indexOf('[');
        const lastBracket = text.lastIndexOf(']');
        
        if (firstBracket !== -1 && lastBracket !== -1 && lastBracket > firstBracket) {
            try {
                // 提取并解析JSON数组部分
                const jsonPart = text.substring(firstBracket, lastBracket + 1);
                return JSON.parse(jsonPart);
            } catch (e3) {
                console.error('JSON数组提取失败:', e3);
            }
        }
    }
    
    // 所有方法都失败，返回null
    console.error('无法从响应中提取有效JSON');
    return null;
}

// 将股票数据格式化为中文描述
function formatStockDataToChinese(stockData) {
    const fields = [
        { key: 'p', name: '当前价格' },
        { key: 'price', name: '当前价格' },
        { key: 'zs', name: '涨跌幅' },
        { key: 'change', name: '涨跌幅' },
        { key: 'price_change', name: '涨跌幅' },
        { key: 'o', name: '今开' },
        { key: 'open', name: '今开' },
        { key: 'yc', name: '昨收' },
        { key: 'close', name: '昨收' },
        { key: 'h', name: '最高' },
        { key: 'high', name: '最高' },
        { key: 'l', name: '最低' },
        { key: 'low', name: '最低' },
        { key: 'v', name: '成交量' },
        { key: 'volume', name: '成交量' },
        { key: 'cje', name: '成交额' },
        { key: 'hs', name: '换手率' },
        { key: 'pe', name: '市盈率' },
        // 添加MA均线数据
        { key: 'ma5', name: 'MA5' },
        { key: 'ma10', name: 'MA10' },
        { key: 'ma20', name: 'MA20' },
        // 添加量比数据
        { key: 'volumeRatio', name: '量比' }
    ];
    
    const result = [];
    const addedKeys = new Set();
    
    fields.forEach(field => {
        if (stockData[field.key] !== undefined && !addedKeys.has(field.name)) {
            addedKeys.add(field.name);
            const value = stockData[field.key];
            result.push(`${field.name}：${value}${field.key === 'zs' || field.key === 'change' || field.key === 'price_change' || field.key === 'hs' ? '%' : ''}`);
        }
    });
    
    // 添加KDJ数据
    if (stockData.kdj) {
        result.push(`KDJ指标：K值=${stockData.kdj.k.toFixed(2)}，D值=${stockData.kdj.d.toFixed(2)}，J值=${stockData.kdj.j.toFixed(2)}`);
        
        // 添加KDJ信号
        let kdjSignal = '';
        switch (stockData.kdj.signal) {
            case 'golden':
                kdjSignal = 'KDJ金叉(买入信号)';
                break;
            case 'death':
                kdjSignal = 'KDJ死叉(卖出信号)';
                break;
            default:
                kdjSignal = 'KDJ正常';
                break;
        }
        result.push(`KDJ信号：${kdjSignal}`);
    }
    
    // 添加MACD数据
    if (stockData.macd) {
        result.push(`MACD指标：DIF=${stockData.macd.dif.toFixed(4)}，DEA=${stockData.macd.dea.toFixed(4)}，MACD=${stockData.macd.macd.toFixed(4)}`);
        
        // 添加MACD信号
        let macdSignal = '';
        switch (stockData.macd.signal) {
            case 'golden':
                macdSignal = 'MACD金叉(买入信号)';
                break;
            case 'death':
                macdSignal = 'MACD死叉(卖出信号)';
                break;
            default:
                macdSignal = 'MACD正常';
                break;
        }
        result.push(`MACD信号：${macdSignal}`);
    }
    
    return result.join('，');
}

/**
 * 发送股票信息咨询请求
 */
function sendStockInfoRequest(symbol, name, stockInfo) {
    const chatContainer = document.getElementById('chatContainer');
    
    // 构建包含实时数据的信息请求
    const baseMessage = `请告诉我关于${name}(${symbol})的信息`;
    const fullMessage = stockInfo ? `${baseMessage}\n\n实时数据：${stockInfo}` : baseMessage;
    
    console.log('发送咨询请求:', fullMessage);
    
    // 发送请求
    setTimeout(() => {
        fetch('api/send_message.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                message: fullMessage,
                api_key: localStorage.getItem('api_key') || ''
            })
        })
        .then(response => response.json())
        .then(data => {
            // 更新"分析中"消息，而不是移除
            const analyzingMsg = chatContainer.querySelector('.analyzing');
            if (analyzingMsg) {
                const content = analyzingMsg.querySelector('.message-content');
                if (content) {
                    // 使用marked.js渲染Markdown
                    if (window.marked) {
                        content.innerHTML = marked.parse(data.data || data.response);
                    } else {
                        content.innerHTML = (data.data || data.response).replace(/\n/g, '<br>');
                    }
                    content.classList.add('markdown-content');
                }
                analyzingMsg.classList.remove('analyzing');
            } else {
                // 如果没有找到分析中消息，添加新的AI回复
                addChatMessage(data.data || data.response, 'ai');
            }
            
            // 显示咨询完成通知
            if (typeof window.showAnalysisNotification === 'function' && symbol && name) {
                window.showAnalysisNotification(name, symbol);
            }
            
            // 滚动到底部
            chatContainer.scrollTop = chatContainer.scrollHeight;
        })
        .catch(error => {
            console.error('Error:', error);
            // 更新"分析中"消息
            const analyzingMsg = chatContainer.querySelector('.analyzing');
            if (analyzingMsg) {
                const content = analyzingMsg.querySelector('.message-content');
                if (content) {
                    content.innerHTML = '请求失败，请稍后再试';
                }
                analyzingMsg.classList.remove('analyzing');
            } else {
                addChatMessage('请求失败，请稍后再试', 'ai');
            }
            chatContainer.scrollTop = chatContainer.scrollHeight;
        });
    }, 100); // 短暂延迟确保滚动完成
}

// 获取市场指数数据
function fetchMarketIndices() {
    fetch('api/stock_proxy.php?type=index&index_type=沪深重要指数')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            return response.text(); // 先获取文本，避免JSON解析错误
        })
        .then(text => {
            // 尝试解析JSON
            let data;
            try {
                data = JSON.parse(text);
            } catch (e) {
                console.error('JSON解析错误:', e, '原始数据:', text.substring(0, 200));
                throw new Error('API返回的数据格式无效');
            }

            // 检查是否返回了错误对象
            if (data && data.error) {
                throw new Error(data.error);
            }

            const marketIndicesDiv = document.getElementById('marketIndices');
            if (!marketIndicesDiv) return;

            // 检查数据是否为数组且有内容
            if (!Array.isArray(data) || data.length === 0) {
                throw new Error('API返回的数据格式不正确或为空');
            }

            // 过滤出我们需要显示的三个主要指数
            const mainIndices = data.filter(index => 
                ['上证指数', '深证成指', '创业板指'].includes(index.名称)
            );

            // 检查是否找到了需要的指数
            if (mainIndices.length === 0) {
                throw new Error('未找到需要的指数数据');
            }

            // 清空加载提示
            marketIndicesDiv.innerHTML = '';

            // 创建指数卡片
            mainIndices.forEach(index => {
                const isUp = index.涨跌幅 >= 0;
                const card = document.createElement('div');
                card.className = 'index-card';
                card.innerHTML = `
                    <div class="index-name">${index.名称}</div>
                    <div class="index-price">${index.最新价.toFixed(2)}</div>
                    <div class="index-change">
                        <span class="index-change-item ${isUp ? 'price-up' : 'price-down'}">
                            ${index.涨跌幅 >= 0 ? '+' : ''}${index.涨跌幅.toFixed(2)}%
                        </span>
                        <span class="index-change-item ${isUp ? 'price-up' : 'price-down'}">
                            ${index.涨跌额 >= 0 ? '+' : ''}${index.涨跌额.toFixed(2)}
                        </span>
                    </div>
                    <div class="index-details">
                        <div class="index-detail-item">开：${index.今开.toFixed(2)}</div>
                        <div class="index-detail-item">高：${index.最高.toFixed(2)}</div>
                        <div class="index-detail-item">低：${index.最低.toFixed(2)}</div>
                        <div class="index-detail-item">昨：${index.昨收.toFixed(2)}</div>
                    </div>
                `;
                marketIndicesDiv.appendChild(card);
            });
        })
        .catch(error => {
            console.error('获取市场指数数据失败:', error);
            const marketIndicesDiv = document.getElementById('marketIndices');
            if (marketIndicesDiv) {
                // 显示更具体的错误信息
                marketIndicesDiv.innerHTML = `<div class="loading-indices">获取指数数据失败: ${error.message}</div>`;
                
                // 错误超过3次后，暂停自动刷新
                window.marketIndexErrorCount = (window.marketIndexErrorCount || 0) + 1;
                if (window.marketIndexErrorCount >= 3) {
                    if (window.marketIndexInterval) {
                        clearInterval(window.marketIndexInterval);
                        console.log('由于连续错误，已暂停指数自动刷新');
                        marketIndicesDiv.innerHTML += '<div class="loading-indices">由于连续错误，已暂停自动刷新。请刷新页面重试。</div>';
                    }
                }
            }
        });
}

// 定时刷新市场指数数据
function startMarketIndicesRefresh() {
    // 清除可能存在的定时器
    if (window.marketIndexInterval) {
        clearInterval(window.marketIndexInterval);
    }
    
    // 先立即获取一次数据
    fetchMarketIndices();
    
    // 设置定时器，每隔一段时间刷新一次
    window.marketIndexInterval = setInterval(fetchMarketIndices, 37000);
}

// 获取股票变动数据
function fetchStockChanges(type = '大笔买入') {
    const url = `api/stock_proxy.php?type=changes&changes_type=${encodeURIComponent(type)}`;
    console.log(`请求股票变动数据: ${url}`);
    
    // 显示加载中状态
    const marqueeContainer = document.getElementById('stockChangesMarquee');
    if (marqueeContainer) {
        marqueeContainer.innerHTML = `<div class="loading-changes with-spinner">正在获取${type}数据...</div>`;
    }
    
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }
            return response.text(); // 先获取文本，避免JSON解析错误
        })
        .then(text => {
            // 检查是否为空
            if (!text || text.trim() === '') {
                throw new Error('服务器返回空响应');
            }
            
            // 记录原始响应用于调试
            console.log(`${type}原始响应(前100字符): ${text.substring(0, 100)}`);
            
            // 尝试解析JSON
            let data;
            try {
                data = JSON.parse(text);
            } catch (e) {
                console.error('JSON解析错误:', e, '原始数据:', text.substring(0, 200));
                throw new Error('API返回的数据格式无效: ' + e.message);
            }

            // 检查是否返回了错误对象
            if (data && data.error) {
                throw new Error(data.error);
            }

            // 检查数据是否为数组且有内容
            if (!Array.isArray(data)) {
                throw new Error(`API返回的数据格式不正确: 预期数组，实际为${typeof data}`);
            }
            
            if (data.length === 0) {
                marqueeContainer.innerHTML = `<div class="loading-changes">暂无${type}数据</div>`;
                return;
            }

            // 创建滚动内容
            const contentDiv = document.createElement('div');
            contentDiv.className = 'stock-changes-content';
            
            // 不再暂停动画，直接让其运行
            // contentDiv.style.animationPlayState = 'paused';
            
            // 只取前20条数据，避免过长
            const limitedData = data.slice(0, 50);
            
            // 构建HTML
            limitedData.forEach(item => {
                const changeItem = document.createElement('span');
                changeItem.className = 'stock-change-item';
                
                // 解析相关信息（格式：数量,价格,涨跌幅）
                let infoText = '';
                if (item.相关信息) {
                    const infoParts = item.相关信息.split(',');
                    if (infoParts.length >= 3) {
                        const amount = parseInt(infoParts[0]).toLocaleString();
                        const price = parseFloat(infoParts[1]).toFixed(2);
                        const change = parseFloat(infoParts[2]) * 100;
                        const changeText = change >= 0 ? `+${change.toFixed(2)}%` : `${change.toFixed(2)}%`;
                        infoText = `${amount}股 ${price}元 ${changeText}`;
                    } else {
                        infoText = item.相关信息;
                    }
                }
                
                changeItem.innerHTML = `
                    <span class="time">${item.时间}</span>
                    <span class="code">${item.代码}</span>
                    <span class="name">${item.名称}</span>
                    <span class="info">${infoText}</span>
                `;
                
                contentDiv.appendChild(changeItem);
            });
            
            // 清空并添加新内容
            marqueeContainer.innerHTML = '';
            marqueeContainer.appendChild(contentDiv);
            
            // 添加用户提示，但不延迟动画
            const tipDiv = document.createElement('div');
            tipDiv.className = 'loading-changes';
            tipDiv.style.marginTop = '5px';
            tipDiv.style.fontSize = '12px';
            tipDiv.style.color = '#999';
            tipDiv.textContent = '提示：鼠标悬停可暂停滚动';
            marqueeContainer.appendChild(tipDiv);
            
            // 短暂显示提示后自动移除
            setTimeout(() => {
                if (tipDiv.parentNode) {
                    tipDiv.parentNode.removeChild(tipDiv);
                }
            }, 1000);
            
            // 存储当前类型
            window.currentChangesType = type;
            
            // 更新激活的选项卡
            updateActiveTab(type);
        })
        .catch(error => {
            console.error(`获取${type}数据失败:`, error);
            const marqueeContainer = document.getElementById('stockChangesMarquee');
            if (marqueeContainer) {
                marqueeContainer.innerHTML = `<div class="loading-changes">获取${type}数据失败: ${error.message}</div>`;
                
                // 错误超过3次后，暂停自动刷新
                window.stockChangesErrorCount = (window.stockChangesErrorCount || 0) + 1;
                if (window.stockChangesErrorCount >= 3) {
                    if (window.stockChangesInterval) {
                        clearInterval(window.stockChangesInterval);
                        console.log('由于连续错误，已暂停股票变动数据自动刷新');
                        marqueeContainer.innerHTML += '<div class="loading-changes">由于连续错误，已暂停自动刷新。请刷新页面重试。</div>';
                    }
                }
            }
        });
}

// 更新活动的标签
function updateActiveTab(type) {
    const tabs = document.querySelectorAll('.changes-tab');
    tabs.forEach(tab => {
        if (tab.getAttribute('data-type') === type) {
            tab.classList.add('active');
        } else {
            tab.classList.remove('active');
        }
    });
}

// 定时刷新股票变动数据
function startStockChangesRefresh() {
    // 清除可能存在的定时器
    if (window.stockChangesInterval) {
        clearInterval(window.stockChangesInterval);
    }
    
    // 先立即获取一次数据
    fetchStockChanges();
    
    // 设置定时器，每隔一段时间刷新一次
    window.stockChangesInterval = setInterval(function() {
        fetchStockChanges(window.currentChangeType || '大笔买入');
    }, 35000); // 每35秒更新一次
}

// 绑定股票变动标签切换事件
function bindStockChangesTabs() {
    const tabs = document.querySelectorAll('.changes-tab');
    if (tabs.length === 0) {
        console.warn('未找到股票变动标签元素');
        return;
    }
    
    console.log(`找到${tabs.length}个标签元素`);
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function(event) {
            event.preventDefault();
            const type = this.getAttribute('data-type');
            if (type) {
                console.log(`切换到${type}数据`);
                
                // 获取新类型的数据
                fetchStockChanges(type);
            }
        });
    });
}

// 搜索股票
document.getElementById('searchButton').addEventListener('click', function() {
    searchStock();
});

// 回车键搜索
document.getElementById('searchInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        e.preventDefault();
        searchStock();
    }
});

// 搜索股票函数
function searchStock() {
    const query = document.getElementById('searchInput').value.trim();
    if (!query) return;
    
    const searchResults = document.getElementById('searchResults');
    searchResults.innerHTML = '<div class="loading">正在搜索...</div>';
    
    fetch(`api/search_stock.php?query=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' && data.data.length > 0) {
                let resultsHtml = '';
                data.data.forEach(stock => {
                    resultsHtml += `
                    <div class="search-result-item">
                        <div class="stock-info">
                            <div class="stock-info-name">${stock.name}</div>
                            <div class="stock-info-code">${stock.code}</div>
                        </div>
                        <button class="add-button" data-code="${stock.code}" data-name="${stock.name}">
                            <i class="fas fa-plus"></i> 添加
                        </button>
                    </div>`;
                });
                searchResults.innerHTML = resultsHtml;
                
                // 添加事件监听
                searchResults.querySelectorAll('.add-button').forEach(button => {
                    button.addEventListener('click', function() {
                        const code = this.getAttribute('data-code');
                        const name = this.getAttribute('data-name');
                        
                        // 使用前端存储模块添加股票
                        if (FrontendStocks.addStock(code, name)) {
                            // 添加成功，显示通知
                            showNotification(`已添加 ${name}(${code})`, 'success');
                            // 刷新股票列表
                            loadStockList();
                        } else {
                            // 添加失败，可能是已存在
                            showNotification(`${name}(${code}) 已在列表中`, 'warning');
                        }
                    });
                });
            } else {
                searchResults.innerHTML = '<div class="no-results">未找到符合条件的股票</div>';
            }
        })
        .catch(error => {
            console.error('搜索失败', error);
            searchResults.innerHTML = '<div class="error">搜索失败，请重试</div>';
        });
}
