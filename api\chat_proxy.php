<?php
// 启用错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// 记录请求日志
function logError($message, $data = null) {
    $logEntry = date('Y-m-d H:i:s') . " - " . $message;
    if ($data) {
        $logEntry .= " - Data: " . json_encode($data);
    }
    error_log($logEntry . "\n", 3, "chat_proxy_error.log");
}

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // 获取POST数据
    $rawData = file_get_contents('php://input');
    logError("Received request data", $rawData);
    
    $data = json_decode($rawData, true);
    
    if (!$data || !isset($data['api_key']) || !isset($data['content'])) {
        logError("Missing required parameters", $data);
        http_response_code(400);
        echo json_encode(['error' => '缺少必要参数']);
        exit;
    }
    
    // 构建请求数据
    $requestData = [
        'model' => 'agu',
        'messages' => [
            ['role' => 'user', 'content' => $data['content']]
        ]
    ];
    
    // 设置CURL选项
    $ch = curl_init('https://api.llingfei.com/v1/chat/completions');
    curl_setopt_array($ch, [
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $data['api_key']
        ],
        CURLOPT_POSTFIELDS => json_encode($requestData),
        CURLOPT_SSL_VERIFYPEER => true, //SSL启用
        CURLOPT_TIMEOUT => 100,         //超时 
        CURLOPT_CONNECTTIMEOUT => 20,  //链接建立超时
        CURLOPT_TCP_KEEPALIVE => 1,   //长链接
        CURLOPT_TCP_KEEPIDLE => 60,
        CURLOPT_TCP_KEEPINTVL => 60,
        CURLOPT_BUFFERSIZE => 128000,
        CURLOPT_VERBOSE => false, // 日志记录
        CURLOPT_IPRESOLVE => CURL_IPRESOLVE_V4,
        CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2, // 避免TLS协商耗时
        CURLOPT_ENCODING => '',
        CURLOPT_FAILONERROR => false 
    ]);
    
    // 创建一个临时文件来存储CURL的详细信息
    $verbose = fopen('php://temp', 'w+');
    curl_setopt($ch, CURLOPT_STDERR, $verbose);
    
    // 发送请求到AI API
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    // 如果请求失败，记录详细信息
    if ($response === false || $httpCode !== 200) {
        rewind($verbose);
        $verboseLog = stream_get_contents($verbose);
        
        logError("API request failed", [
            'curl_error' => curl_error($ch),
            'http_code' => $httpCode,
            'verbose_log' => $verboseLog
        ]);
        
        throw new Exception('API请求失败: ' . curl_error($ch) . ', HTTP状态码: ' . $httpCode);
    }
    
    // 检查响应是否是有效的JSON
    $responseData = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        logError("Invalid JSON response", $response);
        throw new Exception('API返回了无效的JSON数据');
    }
    
    // 返回API响应
    echo $response;
    
} catch (Exception $e) {
    logError("Exception occurred", [
        'message' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    http_response_code(500);
    echo json_encode([
        'error' => '请求失败',
        'message' => $e->getMessage()
    ]);
} finally {
    if (isset($ch)) {
        curl_close($ch);
    }
    if (isset($verbose)) {
        fclose($verbose);
    }
} 