<?php
session_start();
require_once 'db_connect.php';

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['error' => '请先登录']);
    exit;
}

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['message'])) {
    http_response_code(400);
    echo json_encode(['error' => '缺少消息内容']);
    exit;
}

$message = $data['message'];
$username = $_SESSION['username'];

try {
    // 记录消息
    $stmt = $pdo->prepare("INSERT INTO chat_messages (username, message, created_at) VALUES (?, ?, NOW())");
    $stmt->execute([$username, $message]);
    
    echo json_encode(['success' => true]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => '发送消息失败：' . $e->getMessage()]);
}
?> 