<?php
// 包含配置文件和函数库
require_once '../includes/config.php';
require_once '../includes/functions.php';

// 确保会话已启动
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 设置响应头为JSON
header('Content-Type: application/json');

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => '用户未登录']);
    exit;
}

// 连接数据库
$db = getDbConnection();

// 处理请求
$action = isset($_GET['action']) ? $_GET['action'] : '';

switch ($action) {
    // 保存高评分股票
    case 'save':
        saveHighScoreStock($db);
        break;
    
    // 获取高评分股票列表
    case 'get':
        getHighScoreStocks($db);
        break;
        
    // 错误的动作
    default:
        echo json_encode(['success' => false, 'message' => '无效的操作']);
        break;
}

/**
 * 保存高评分股票到数据库
 */
function saveHighScoreStock($db) {
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        echo json_encode(['success' => false, 'message' => '无效的请求方法']);
        return;
    }
    
    // 获取POST数据
    $data = json_decode(file_get_contents('php://input'), true);
    
    // 检查必要参数
    if (!isset($data['code'], $data['name'], $data['score']) || empty($data['code']) || empty($data['name']) || !is_numeric($data['score'])) {
        echo json_encode(['success' => false, 'message' => '缺少必要参数或参数格式错误']);
        return;
    }
    
    // 获取数据
    $stockCode = $data['code'];
    $stockName = $data['name'];
    $score = intval($data['score']);
    $userId = $_SESSION['user_id'];
    
    // 只保存80分以上的高评分股票
    if ($score < 80) {
        echo json_encode(['success' => false, 'message' => '只有80分以上的股票才会被记录到高评分列表']);
        return;
    }
    
    try {
        // 检查是否已存在该股票的记录
        $stmt = $db->prepare("SELECT id FROM high_score_stocks WHERE stock_code = ?");
        $stmt->execute([$stockCode]);
        $existingRecord = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existingRecord) {
            // 更新现有记录
            $stmt = $db->prepare("UPDATE high_score_stocks SET 
                stock_name = ?, 
                score = ?, 
                user_id = ?, 
                updated_at = NOW() 
                WHERE stock_code = ?");
            $stmt->execute([$stockName, $score, $userId, $stockCode]);
        } else {
            // 插入新记录
            $stmt = $db->prepare("INSERT INTO high_score_stocks 
                (stock_code, stock_name, score, user_id, created_at, updated_at) 
                VALUES (?, ?, ?, ?, NOW(), NOW())");
            $stmt->execute([$stockCode, $stockName, $score, $userId]);
        }
        
        echo json_encode(['success' => true, 'message' => '高评分股票保存成功']);
    } catch (PDOException $e) {
        error_log("保存高评分股票失败: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => '数据库操作失败']);
    }
}

/**
 * 从数据库获取高评分股票列表
 */
function getHighScoreStocks($db) {
    try {
        // 获取最近添加的最多5只高评分股票（评分>=80，按分数降序）
        $stmt = $db->query("SELECT 
            s.stock_code, 
            s.stock_name, 
            s.score, 
            s.updated_at,
            u.username as submitted_by
            FROM high_score_stocks s
            LEFT JOIN users u ON s.user_id = u.id
            WHERE s.score >= 80
            ORDER BY s.score DESC, s.updated_at DESC
            LIMIT 5");
        
        $stocks = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'stocks' => $stocks]);
    } catch (PDOException $e) {
        error_log("获取高评分股票失败: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => '数据库操作失败']);
    }
} 