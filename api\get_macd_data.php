<?php
// MACD数据获取API

// 股票代码
$stockCode = $_GET['symbol'] ?? '';

// 检查股票代码是否为空
if (empty($stockCode)) {
    header('Content-Type: application/json');
    echo json_encode(['error' => '股票代码不能为空']);
    exit;
}

// 目标 API 地址 - 获取历史交易数据
$end_date = date('Ymd');
$start_date = date('Ymd', strtotime('-60 days')); // 获取60天数据用于MACD计算（需要更长周期）
$apiUrl = "http://localhost:8888/api/public/stock_zh_a_hist?symbol=" . urlencode($stockCode) . 
          "&period=daily&start_date=" . $start_date . "&end_date=" . $end_date . "&adjust=qfq";

// 初始化 cURL
$ch = curl_init();

// 设置 cURL 选项
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, false);

// 发送请求并获取响应
$response = curl_exec($ch);

// 检查是否有错误发生
if (curl_errno($ch)) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'cURL 错误: ' . curl_error($ch)]);
    exit;
}

// 关闭 cURL 资源
curl_close($ch);

// 设置响应头为 JSON
header('Content-Type: application/json');

// 处理历史数据
if (!empty($response)) {
    $data = json_decode($response, true);
    if (is_array($data)) {
        // 按日期降序排序
        usort($data, function($a, $b) {
            return strtotime($b['日期']) - strtotime($a['日期']);
        });
        
        // 返回完整的历史数据用于MACD计算
        echo json_encode([
            'success' => true,
            'data' => array_slice($data, 0, 50) // 最近50天数据，足够计算MACD
        ]);
    } else {
        echo json_encode(['error' => '解析历史数据失败']);
    }
} else {
    echo json_encode(['error' => '获取历史数据失败']);
}
?> 