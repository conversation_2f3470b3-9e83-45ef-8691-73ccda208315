<?php
// 先加载配置文件，它会处理会话启动
require_once 'includes/config.php';
require_once 'includes/functions.php';

// 确保会话已启动
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    header('Location: index.php');
    exit;
}

$username = $_SESSION['username'];

// 获取当前用户信息
$user = getCurrentUser();

// 获取公告列表
$announcements = getAnnouncements();

// 获取用户的股票列表
$stocks = getUserStocks($_SESSION['user_id']);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - <?php echo WEBSITE_NAME; ?></title>
    <link rel="icon" href="<?php echo WEBSITE_LOGO; ?>" type="image/png">
    <!-- 内联反调试代码 -->
    <script>
    (function() {
        // 提前标记已加载，防止重复
        window.__antiDebugLoaded = true;
        
        // 初始设置 - 非本地环境启用保护
        var isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
        if(!isProduction) return;
        
        // 记录原始console方法
        var _console = {
            log: console.log,
            warn: console.warn,
            error: console.error,
            info: console.info,
            debug: console.debug
        };
        
        // 立即禁用控制台 - 早期保护
        console.log = console.warn = console.error = console.info = console.debug = function(){};
        
        // 禁用F12和其他开发工具快捷键
        document.addEventListener('keydown', function(e) {
            if(e.key === 'F12' || 
              (e.ctrlKey && e.shiftKey && e.key === 'I') || 
              (e.ctrlKey && e.shiftKey && e.key === 'J') ||
              (e.ctrlKey && e.key === 'U')) {
                e.preventDefault();
                return false;
            }
        }, true);
        
        // 检测是否已打开开发工具
        function isDevToolsOpened() {
            var threshold = 160;
            var widthThreshold = 160;
            var heightThreshold = 160;
            
            // 检测大小差异
            var widthDiff = window.outerWidth - window.innerWidth > widthThreshold;
            var heightDiff = window.outerHeight - window.innerHeight > heightThreshold;
            
            return widthDiff || heightDiff;
        }
        
        // 周期性检查 - 使用非阻塞方式
        var checkInterval;
        
        function startChecking() {
            if(!checkInterval) {
                checkInterval = setInterval(function() {
                    if(isDevToolsOpened()) {
                        // 检测到开发工具，但不重定向，只禁用功能
                        disableDevTools();
                    }
                }, 1000);
            }
        }
        
        // 禁用开发工具而不是重定向
        function disableDevTools() {
            // 覆盖控制台对象，而不是重定向
            Object.defineProperty(window, 'console', {
                get: function() {
                    return {
                        log: function(){},
                        warn: function(){},
                        error: function(){},
                        info: function(){},
                        debug: function(){}
                    };
                },
                set: function(){}
            });
            
            // 可以添加其他措施，但不影响核心功能
        }
        
        // 页面加载后启动检查
        if(document.readyState === 'complete') {
            startChecking();
        } else {
            window.addEventListener('load', startChecking);
        }
    })();
    </script>
    <!-- 反调试脚本 -->
    <script src="js/anti-debug.js"></script>
    <!-- 基础样式 -->
    <link rel="stylesheet" href="css/style.css?v=<?php echo filemtime('css/style.css'); ?>">
    <link rel="stylesheet" href="css/styles.css?v=<?php echo filemtime('css/styles.css'); ?>">
    <!-- 新增现代UI样式 -->
    <link rel="stylesheet" href="css/modern.css?v=<?php echo filemtime('css/modern.css'); ?>">
    <link rel="stylesheet" href="css/components.css?v=<?php echo filemtime('css/components.css'); ?>">
    <!-- 移动端样式 -->
    <link rel="stylesheet" href="css/mobile.css?v=<?php echo filemtime('css/mobile.css'); ?>">
    <link rel="stylesheet" href="css/mobile-modern.css?v=<?php echo filemtime('css/mobile-modern.css'); ?>">
    <!-- Font Awesome图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script>
        function showQRCode(type) {
            const modal = document.getElementById(type + 'Modal');
            if (modal) {
                modal.style.display = 'flex';
            }
        }

        function hideQRCode(type) {
            const modal = document.getElementById(type + 'Modal');
            if (modal) {
                modal.style.display = 'none';
            }
        }

        // 点击模态框背景关闭
        document.addEventListener('DOMContentLoaded', function() {
            const modals = document.querySelectorAll('.qrcode-modal');
            modals.forEach(modal => {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        this.style.display = 'none';
                    }
                });
            });
        });
    </script>
    <style>
        body {
            background: #f8f8fc;
            position: relative;
            color: #333;
        }
        
        .container {
            position: relative;
            z-index: 1;
            padding-top: 60px; /* 添加顶部内边距，防止内容被标题栏遮挡 */
        }
        
        .sidebar section, 
        .main-content section {
            background-color: #ffffff;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border-radius: 12px;
            border-top: 3px solid #e8e8e8;
        }
        
        .sidebar section:hover, 
        .main-content section:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        
        header {
            background-color: #ffffff;
            backdrop-filter: blur(10px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            border-radius: 0 0 12px 12px;
            border-bottom: 3px solid #e8e8e8;
            /* 移除固定定位 */
            position: fixed;
            width: 100%;
            top: 0;
            left: 0;
            z-index: 1000;
            transition: transform 0.3s ease;
        }
        
        footer {
            color: #555;
            margin-top: 2rem;
        }
        
        .storage-desc {
            background-color: rgba(112, 69, 175, 0.1);
            border-left-color: rgba(112, 69, 175, 0.7);
        }
        
        /* 市场指数样式 */
        .market-index-section {
            margin-bottom: 20px;
        }
        
        .market-indices {
            display: flex;
            flex-wrap: nowrap;
            justify-content: space-between;
            gap: 15px;
            padding: 15px;
        }
        
        .index-card {
            flex: 1;
            min-width: 0; /* 允许卡片缩小 */
            background: #fff;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .index-card:hover {
            transform: translateY(-3px);
        }
        
        .index-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .index-price {
            font-size: 22px;
            font-weight: bold;
            margin: 3px 0;
        }
        
        .index-change {
            display: flex;
            gap: 8px;
            margin-top: 3px;
        }
        
        .index-change-item {
            padding: 3px 6px;
            border-radius: 4px;
            font-size: 13px;
        }
        
        .price-up {
            color: #f55;
            background: rgba(255,85,85,0.1);
        }
        
        .price-down {
            color: #44b979;
            background: rgba(68,185,121,0.1);
        }
        
        .index-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr); /* 两列布局 */
            gap: 6px;
            margin-top: 8px;
            font-size: 12px;
            color: #666;
        }
        
        .index-detail-item {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .loading-indices {
            width: 100%;
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        /* API密钥显示样式 */
        .api-key-display {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px 15px;
            margin-bottom: 10px;
        }
        
        .key-label {
            font-weight: 500;
            color: #495057;
            margin-right: 8px;
        }
        
        .key-value {
            font-family: monospace;
            background: #e9ecef;
            padding: 3px 8px;
            border-radius: 3px;
            color: #0d6efd;
            letter-spacing: 1px;
        }
        
        .key-info {
            display: block;
            margin-top: 8px;
            font-size: 12px;
            color: #6c757d;
        }
        
        .remaining-requests {
            margin-top: 10px;
            margin-left: 30px;
            background-color: #e8f4ff;
            padding: 12px 15px;
            border-radius: 8px;
            color: #0d6efd;
            font-weight: 500;
            text-align: center;
            font-size: 16px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border: 1px solid #d0e5ff;
        }
        
        .remaining-requests span {
            font-weight: 700;
            font-size: 18px;
            color: #0b5ed7;
        }
        
        .qrcode-buttons {
            display: flex;
            justify-content: space-around;
            padding: 20px;
            gap: 20px;
        }

        .qrcode-button {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            width: 120px;
            background-color: #fff;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .qrcode-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .qrcode-button i {
            font-size: 32px;
            margin-bottom: 8px;
        }

        .wechat-button i {
            color: #07C160;
        }

        .recharge-button i {
            color: #FF6B6B;
        }

        .qrcode-button p {
            margin: 0;
            font-size: 14px;
            color: #666;
        }

        .qrcode-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .qrcode-content {
            background-color: #fff;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            position: relative;
        }

        .qrcode-content img {
            max-width: 200px;
            height: auto;
        }

        .qrcode-content .close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            font-size: 20px;
            color: #666;
        }

        .recharge-notice {
            color: #ff4444;
            margin-top: 10px;
            font-size: 14px;
        }
        
        .section-title {
            color: #555555;
            border-bottom: 2px solid rgba(200, 200, 200, 0.5);
            padding-bottom: 8px;
        }
        
        .search-button {
            background-color: #4a90e2;
            border-color: #4a90e2;
            color: #fff;
        }
        
        .search-button:hover {
            background-color: #3a75c4;
            border-color: #3a75c4;
        }
        
        /* 用户信息链接样式 */
        .user-info a {
            display: inline-block;
            margin-left: 10px;
            padding: 5px 12px;
            background-color: #4a90e2;
            color: #fff;
            text-decoration: none;
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .user-info a:hover {
            background-color: #3a75c4;
        }
        
        /* 添加按钮样式 */
        .add-button {
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 15px;
            cursor: pointer;
            transition: background-color 0.2s;
            width: auto;
            max-width: 80px;
            text-align: center;
        }
        
        .add-button:hover {
            background-color: #3a75c4;
        }
        
        /* 存储模式开关样式修复 */
        .storage-mode-section {
            padding: 15px;
            background-color: #ffffff;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #e8e8e8;
        }
        
        .toggle-switch {
            display: flex;
            align-items: center;
            position: relative;
            cursor: pointer;
            margin-bottom: 8px;
        }
        
        .toggle-switch input {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: relative;
            display: inline-block;
            width: 46px;
            height: 24px;
            background-color: #c8c8c8;
            border-radius: 24px;
            transition: all 0.3s;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            border-radius: 50%;
            transition: all 0.3s;
        }
        
        input:checked + .toggle-slider {
            background-color: #4caf50;
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(22px);
        }
        
        .toggle-label {
            color: #333;
            font-size: 15px;
            font-weight: 500;
            display: inline-block;
            white-space: nowrap;
            min-width: 120px;
        }
        
        .storage-info {
            color: #666;
            font-size: 13px;
            margin-top: 5px;
            margin-left: 0;
        }
        
        /* Agent分析等待区域样式 */
        .empty-message {
            text-align: center;
            padding: 10px;
            color: #666;
            font-size: 14px;
            background-color: rgba(200, 200, 200, 0.1);
            border-radius: 6px;
            margin: 10px 0;
            line-height: 1.4;
        }
        
        /* 云同步按钮样式 */
        .cloud-sync-buttons {
            display: flex;
            margin-top: 10px;
            gap: 10px;
            flex-direction: column;
            align-items: stretch;
        }
        
        .cloud-button {
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 10px;
            font-size: 13px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
            width: 100%;
            white-space: nowrap;
        }
        
        .cloud-button:hover {
            background-color: #3a75c4;
        }
        
        .cloud-button svg {
            margin-right: 5px;
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }
        
        .cloud-button.download {
            background-color: #4a90e2;
        }
        
        .cloud-button.download:hover {
            background-color: #3a75c4;
        }

        /* 在移动设备上调整为垂直排列 */
        @media (max-width: 992px) {
            .market-indices {
                flex-wrap: wrap;
            }
            
            .index-card {
                min-width: 100%;
                margin-bottom: 10px;
            }
        }
        
        /* 股票变动滚动条样式 */
        .stock-changes-section {
            margin-bottom: 20px;
        }
        
        .stock-changes-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .stock-changes-tabs {
            display: flex;
            background-color: #f5f5f5;
            border-bottom: 1px solid #eee;
        }
        
        .changes-tab {
            padding: 10px 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            color: #666;
            border-bottom: 2px solid transparent;
        }
        
        .changes-tab:hover {
            background-color: rgba(74, 144, 226, 0.1);
            color: #4a90e2;
        }
        
        .changes-tab.active {
            color: #4a90e2;
            border-bottom: 2px solid #4a90e2;
            font-weight: 500;
        }
        
        .stock-changes-marquee {
            overflow: hidden;
            position: relative;
            height: 40px;
            padding: 0 10px;
        }
        
        .stock-changes-content {
            white-space: nowrap;
            position: absolute;
            animation: marquee 180s linear infinite;
            padding: 10px 0;
            width: max-content;
        }
        
        .stock-changes-content:hover {
            animation-play-state: paused;
        }
        
        @keyframes marquee {
            0% { transform: translateX(0%); }
            100% { transform: translateX(-150%); }
        }
        
        .stock-change-item {
            display: inline-block;
            margin-right: 30px;
            color: #333;
            font-size: 14px;
        }
        
        .stock-change-item .time {
            color: #888;
            font-size: 12px;
            margin-right: 5px;
        }
        
        .stock-change-item .code {
            color: #4a90e2;
            margin-right: 5px;
        }
        
        .stock-change-item .name {
            font-weight: 500;
            margin-right: 5px;
        }
        
        .stock-change-item .info {
            color: #ff6b6b;
            font-size: 12px;
        }
        
        .loading-changes {
            text-align: center;
            padding: 10px;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 20px;
        }
        
        .loading-changes.with-spinner::before {
            content: "";
            width: 16px;
            height: 16px;
            margin-right: 10px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4a90e2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 在移动设备上调整样式 */
        @media (max-width: 768px) {
            .stock-changes-tabs {
                flex-wrap: wrap;
            }
            
            .changes-tab {
                flex: 1;
                text-align: center;
                padding: 8px 5px;
                font-size: 13px;
            }
            
            .stock-changes-marquee {
                height: 60px;
            }
            
            .stock-change-item {
                margin-right: 20px;
                font-size: 13px;
            }
        }
        
        /* 量化分析完成通知样式 */
        .analysis-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px;
            background-color: #4a90e2;
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            z-index: 2000;
            display: flex;
            flex-direction: column;
            min-width: 260px;
            max-width: 320px;
            transform: translateX(120%);
            opacity: 0;
            transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease;
        }
        
        .analysis-notification.show {
            transform: translateX(0);
            opacity: 1;
        }
        
        .analysis-notification-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .analysis-notification-header i {
            font-size: 18px;
            margin-right: 10px;
            color: #ffffff;
        }
        
        .analysis-notification-title {
            font-weight: 500;
            font-size: 16px;
            flex: 1;
        }
        
        .analysis-notification-content {
            font-size: 14px;
            display: flex;
            flex-direction: column;
        }
        
        .analysis-notification-code {
            color: #e8f0fe;
            font-weight: 500;
            margin-bottom: 3px;
        }
        
        .analysis-notification-message {
            color: #fff;
        }
        
        .message-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }
        
        .message-content {
            background-color: #fff;
            padding: 25px;
            border-radius: 12px;
            text-align: left;
            position: relative;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .message-content .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            cursor: pointer;
            font-size: 24px;
            color: #888;
            transition: color 0.3s;
        }
        
        .message-content .close-btn:hover {
            color: #333;
        }
        
        .message-title {
            margin-top: 0;
            margin-bottom: 15px;
            color: #4a90e2;
            font-size: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .message-body {
            font-size: 16px;
            line-height: 1.5;
            color: #333;
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-line;
        }
        
        .message-time {
            font-size: 12px;
            color: #888;
            text-align: right;
            margin-bottom: 15px;
        }
        
        .message-actions {
            display: flex;
            justify-content: flex-end;
        }
        
        .message-btn {
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 15px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .message-btn:hover {
            background-color: #3a75c4;
        }
        
        .message-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #4a90e2;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            z-index: 1500;
            cursor: pointer;
            display: none;
            animation: slideIn 0.5s forwards;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* 清空按钮样式 */
        .clear-chat-btn {
            background-color: #ff4444;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
            display: flex;
            align-items: center;
            gap: 5px;
            margin-left: auto; /* 让按钮靠右 */
            margin-right: 20px; /* 增加右边距 */
        }

        .clear-chat-btn:hover {
            background-color: #cc0000;
        }

        .clear-chat-btn i {
            font-size: 14px;
        }

        /* 反馈模态框样式 */
        .feedback-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 2000;
            justify-content: center;
            align-items: center;
        }
        
        .feedback-content {
            background-color: #fff;
            padding: 25px;
            border-radius: 12px;
            text-align: left;
            position: relative;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .feedback-content .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            cursor: pointer;
            font-size: 24px;
            color: #888;
            transition: color 0.3s;
        }
        
        .feedback-content .close-btn:hover {
            color: #333;
        }
        
        .feedback-title {
            margin-top: 0;
            margin-bottom: 15px;
            color: #4a90e2;
            font-size: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .feedback-body {
            margin-bottom: 20px;
        }
        
        .feedback-body textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            min-height: 150px;
            resize: vertical;
            font-family: inherit;
            font-size: 14px;
        }
        
        .feedback-actions {
            display: flex;
            justify-content: flex-end;
        }
        
        .feedback-btn {
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 15px;
            cursor: pointer;
            transition: background-color 0.3s;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .feedback-btn:hover {
            background-color: #3a75c4;
        }
        
        .feedback-btn:disabled {
            background-color: #a9a9a9;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <h1>
                    <img src="<?php echo WEBSITE_LOGO; ?>" alt="Logo">
                    <?php echo WEBSITE_NAME; ?>
                    <span class="mobile-menu-toggle" id="mobileMenuToggle"><i class="fas fa-bars"></i></span>
                </h1>
                <div class="user-info" id="userInfo">
                    <span><i class="fas fa-user-circle"></i> 欢迎 <?php echo htmlspecialchars($username); ?></span>
                    <a href="#" id="feedbackBtn"><i class="fas fa-comment-dots"></i> 实时反馈[暂停使用]</a>
                    <a href="logout.php"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
                </div>
            </div>
        </header>
        
        <!-- 移动端菜单 -->
        <div class="mobile-menu" id="mobileMenu">
            <div class="mobile-menu-header">
                <img src="<?php echo WEBSITE_LOGO; ?>" alt="Logo" class="mobile-menu-logo">
                <span class="mobile-menu-title"><?php echo WEBSITE_NAME; ?></span>
                <div class="mobile-menu-close" id="mobileMenuClose">
                    <i class="fas fa-times"></i>
                </div>
            </div>
            <div class="mobile-menu-item">
                <a href="#stock-section"><i class="fas fa-chart-line"></i> 我的股票</a>
            </div>
            <div class="mobile-menu-item">
                <a href="#search-section"><i class="fas fa-search"></i> 搜索股票</a>
            </div>
            <div class="mobile-menu-item">
                <a href="#storage-section"><i class="fas fa-database"></i> 存储设置</a>
            </div>
            <div class="mobile-menu-item">
                <a href="#agent-section"><i class="fas fa-robot"></i> 量化结果</a>
            </div>
            <div class="mobile-menu-item">
                <a href="logout.php"><i class="fas fa-sign-out-alt"></i> 退出登录</a>
            </div>
        </div>

        <!-- 移动端底部导航 -->
        <div class="mobile-nav">
            <div class="mobile-nav-items">
                <a href="#stock-section" class="mobile-nav-item active">
                    <i class="fas fa-chart-line"></i>
                    <span>股票</span>
                </a>
                <a href="#search-section" class="mobile-nav-item">
                    <i class="fas fa-search"></i>
                    <span>搜索</span>
                </a>
                <a href="#storage-section" class="mobile-nav-item">
                    <i class="fas fa-database"></i>
                    <span>设置</span>
                </a>
                <a href="#agent-section" class="mobile-nav-item">
                    <i class="fas fa-robot"></i>
                    <span>量化</span>
                </a>
            </div>
        </div>
        
        <main>
            <div class="dashboard-container">
                <div class="sidebar">
                    <!-- API 密钥设置 -->
                    <section>
                        <h3 class="section-title">
                            <span><i class="fas fa-key"></i>量化</span>
                        </h3>
                        <div class="api-key-section" style="text-align: right; padding-right: 30px;">
                            <!-- 隐藏API密钥，但保留data-api-key属性供JS使用 -->
                            <?php if (isset($user['api_key']) && !empty($user['api_key'])): ?>
                                <div class="api-key-display" data-api-key="<?php echo htmlspecialchars($user['api_key']); ?>" style="display:none;"></div>
                            <?php else: ?>
                                <div class="api-key-display" style="display:none;"></div>
                            <?php endif; ?>
                            
                            <div class="remaining-requests">
                                <i class="fas fa-chart-line"></i>
                                剩余次数: <span><?php echo isset($user['remaining_requests']) ? htmlspecialchars($user['remaining_requests']) : '0'; ?></span>
                            </div>
                        </div>
                    </section>
                    
                    <!-- 存储模式设置 -->
                    <section>
                        <h3 class="section-title">
                            <span><i class="fas fa-database"></i>同步</span>
                        </h3>
                        <div class="storage-mode-section" id="storage-section">
                            <div class="toggle-switch">
                                <input type="checkbox" id="frontendStorageToggle" checked> 
                          <!--      <span class="toggle-slider"></span>-->
                                <label for="frontendStorageToggle" class="toggle-label">说明:</label>
                            </div>
                            <div>为了保证量化数据的时效,个股数据计算将在本地进行.如果更换设备或多设备使用,请在拥有最新数据的设备上传后使用同步功能!</div>
                            
                            <div class="cloud-sync-buttons">
                                <button id="uploadToCloud" class="cloud-button upload">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="17 8 12 3 7 8"></polyline>
                                        <line x1="12" y1="3" x2="12" y2="15"></line>
                                    </svg>
                                    上传到云端
                                </button>
                                <button id="downloadFromCloud" class="cloud-button download">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                        <polyline points="7 10 12 15 17 10"></polyline>
                                        <line x1="12" y1="15" x2="12" y2="3"></line>
                                    </svg>
                                    从云端同步
                                </button>
                            </div>
                        </div>
                    </section>
                            
                    <!-- 公告区域 -->
                    <section>
                        <h3 class="section-title">
                            <span><i class="fas fa-bullhorn"></i>公告</span>
                        </h3>
                        <?php if (empty($announcements)): ?>
                            <p class="empty-message">暂无公告</p>
                        <?php else: ?>
                            <?php foreach ($announcements as $announcement): ?>
                                <div class="announcement">
                                    <div class="announcement-title"><?php echo htmlspecialchars($announcement['title']); ?></div>
                                    <div class="announcement-date"><?php echo htmlspecialchars($announcement['created_at'] ?? ''); ?></div>
                                    <div class="announcement-content"><?php echo htmlspecialchars($announcement['content']); ?></div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                        
                        <!-- 二维码按钮区域 -->
                        <div class="qrcode-buttons">
                            <div class="qrcode-button wechat-button" onclick="showQRCode('wechat')">
                                <img src="wxtb.png" alt="微信图标">
                                <p>微信</p>
                            </div>
                            <div class="qrcode-button recharge-button" onclick="showQRCode('recharge')">
                                <img src="cztb.png" alt="充值图标">
                                <p>充值</p>
                            </div>
                        </div>

                        <!-- 二维码弹窗 -->
                        <div class="qrcode-modal" id="wechatModal">
                            <div class="qrcode-content">
                                <span class="close-btn" onclick="hideQRCode('wechat')">&times;</span>
                                <img src="wx.jpg" alt="管理员微信二维码">
                                <p>扫码添加管理员微信</p>
                            </div>
                        </div>
                        <div class="qrcode-modal" id="rechargeModal">
                            <div class="qrcode-content">
                                <span class="close-btn" onclick="hideQRCode('recharge')">&times;</span>
                                <img src="zsm.png" alt="充值二维码">
                                <p>扫码进行充值</p>
                                <p class="recharge-notice">务必填写充值账号在消息区!10分钟内到账</p>
                            </div>
                        </div>
                    </section>
                </div>
                
                <div class="main-content">
                    <!-- 股票搜索 -->
                    <section class="search-section" id="search-section">
                        <h3 class="section-title">
                            <span><i class="fas fa-search"></i> 搜索股票</span>
                        </h3>
                        <div class="input-section">
                            <input type="text" id="searchInput" class="search-input" placeholder="输入股票代码或名称">
                            <button id="searchButton" class="search-button"><i class="fas fa-search"></i> 搜索</button>
                        </div>
                        <div id="searchResults" class="search-results">
                            <!-- 搜索结果将通过 JavaScript 动态加载 -->
                        </div>
                    </section>
                    
                    <!-- 主要指数行情 -->
                    <section class="market-index-section">
                        <h3 class="section-title">
                            <span><i class="fas fa-chart-area"></i> 大盘指数</span>
                        </h3>
                        <div class="market-indices" id="marketIndices">
                            <div class="loading-indices">正在加载指数数据...</div>
                        </div>
                    </section>
                    
                    <!-- 股票变动滚动条 -->
                    <section class="stock-changes-section">
                        <h3 class="section-title">
                            <span><i class="fas fa-fire-alt"></i> 市场动态</span>
                        </h3>
                        <div class="stock-changes-container">
                            <div class="stock-changes-tabs">
                                <span class="changes-tab active" data-type="大笔买入"><i class="fas fa-money-bill-wave"></i> 大笔买入</span>
                                <span class="changes-tab" data-type="快速反弹"><i class="fas fa-bolt"></i> 快速反弹</span>
                                <span class="changes-tab" data-type="火箭发射"><i class="fas fa-rocket"></i> 火箭发射</span>
                            </div>
                            <div class="stock-changes-marquee" id="stockChangesMarquee">
                                <div class="loading-changes">正在加载市场动态数据...</div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- 已添加的股票列表 -->
                    <section id="stock-section">
                        <h3 class="section-title">
                            <span><i class="fas fa-list"></i> 我的股票</span>
                            <div style="display: inline-flex; align-items: center;">
                                <label class="toggle-switch" style="margin-right: 10px; margin-bottom: 0;">
                                    <input type="checkbox" id="autoRefreshToggle">
                                    <span class="toggle-slider"></span>
                                </label>
                                <span class="auto-refresh-text">自动更新</span>
                            </div>
                        </h3>
                            <p style="color: #ff9800; margin-top: 5px; font-size: 14px;">
        卡片每30秒更新一次,建议手动刷新后再使用量化功能!量化需要50-70秒,完成后会有弹窗提示,可通过股票卡片的记录功能或量化窗口查看!</p>
                        <div id="stockList">
                            <!-- 股票列表将通过 JavaScript 动态加载 -->
                            <div class="loading">正在加载股票列表...</div>
                        </div>
                    </section>
                    
                    <!-- 高评分股票区域 -->
                    <section class="high-score-stocks-section" id="high-score-section">
                        <h3 class="section-title">
                            <span><i class="fas fa-award"></i> 高评分股票</span>
                            <small style="color: #666; font-size: 12px; margin-left: 10px;"></small>
                            <button id="extractScoreBtn" class="extract-score-btn" title="从最近量化分析中提取评分">
                        <!--  class="fas fa-sync-alt"></i> 刷新 -->
                            </button>
                        </h3>
                        <p style="color: #4a90e2; margin-top: 5px; font-size: 14px;">
                            <i class="fas fa-info-circle"></i> 自动显示所有用户量化分析中综合评分≥80分的股票. 如果该区域显示异常,刷新页面即可!
                        </p>
                        <div id="highScoreStocksContainer" class="high-score-stocks-container">
                            <!-- 高评分股票将通过JavaScript动态加载 -->
                            <div class="empty-message"><i class="fas fa-info-circle"></i> 暂无高评分股票数据</div>
                        </div>
                    </section>
                    
                    <!-- AI 分析区域 -->
                    <section class="chat-section" id="agent-section">
                        <h3 class="section-title">
                            <span><i class="fas fa-robot"></i> 量化结果</span>
                            <button id="clearChatBtn" class="clear-chat-btn" title="清空量化结果">
                                <i class="fas fa-trash-alt"></i> 清空
                            </button>
                        </h3>
                        <div id="chatContainer" class="chat-container">
                            <!-- 聊天内容将通过 JavaScript 动态加载 -->
                            <div class="empty-message"><i class="fas fa-info-circle"></i> 量化结果只保存最近3条,如有额外需求请另行复制保存!</div>
                        </div>
                    </section>
                </div>
            </div>
        </main>
        
        <footer>
            <p>&copy; <?php echo date('Y'); ?> <?php echo WEBSITE_NAME; ?> | 专业的个股量化平台</p>
        </footer>
    </div>

    <!-- AI服务控制 -->
    <script>
        // 从DOM元素中获取API密钥
        function getApiKeyFromDOM() {
            // 从带有data-api-key属性的元素获取API密钥
            const apiKeyElement = document.querySelector('.api-key-display');
            if (apiKeyElement && apiKeyElement.dataset.apiKey) {
                const apiKey = apiKeyElement.dataset.apiKey;
                // 将API密钥存储到sessionStorage中供其他脚本使用
                sessionStorage.setItem('api_key', apiKey);
                console.log('从DOM获取API密钥并保存到会话存储');
            }
        }
        
        // 页面加载后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 获取API密钥
            getApiKeyFromDOM();
        });
    </script>
    
    <!-- KDJ计算指标 -->
    <script src="js/indicators.js?v=<?php echo filemtime('js/indicators.js'); ?>"></script>
    
    <!-- 引入ECharts库 -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>

    <!-- 引入K线图功能 -->
    <script src="js/kline_chart.js?v=<?php echo filemtime('js/kline_chart.js'); ?>"></script>

    <!-- 前端股票管理 -->
    <script src="js/frontend_stocks.js?v=<?php echo filemtime('js/frontend_stocks.js'); ?>"></script>
    <!-- 再加载依赖于它的主脚本 -->
    <script src="js/main.js?v=<?php echo filemtime('js/main.js'); ?>"></script>
    <!-- 加载API跟踪器 -->
    <script src="js/api_tracker.js?v=<?php echo filemtime('js/api_tracker.js'); ?>"></script>
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    
    <!-- 保留原有的JavaScript代码 -->
    <script>
        // 初始化marked选项
        if (window.marked) {
            marked.setOptions({
                breaks: true, // 将回车符转换为<br>标签
                gfm: true,    // 启用GitHub Flavored Markdown
                headerIds: false, // 禁用自动生成header IDs
                mangle: false  // 禁用URL的mangle
            });
        }
        
        // 页面加载后执行
        document.addEventListener('DOMContentLoaded', function() {
            // ... 保留现有的JavaScript代码 ...
            
            // 移动端优化：底部导航激活状态
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
            mobileNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    mobileNavItems.forEach(i => i.classList.remove('active'));
                    this.classList.add('active');
                });
            });
            
            // 优化移动端菜单关闭方式
            const mobileMenuClose = document.getElementById('mobileMenuClose');
            if (mobileMenuClose) {
                mobileMenuClose.addEventListener('click', function() {
                    const mobileMenu = document.getElementById('mobileMenu');
                    if (mobileMenu) {
                        mobileMenu.classList.remove('active');
                        const overlay = document.querySelector('.mobile-menu-overlay');
                        if (overlay) {
                            document.body.removeChild(overlay);
                        }
                    }
                });
            }
            
            // 云同步功能
            const uploadButton = document.getElementById('uploadToCloud');
            const downloadButton = document.getElementById('downloadFromCloud');
            
            if (uploadButton && downloadButton) {
                // 上传按钮点击事件
                uploadButton.addEventListener('click', function() {
                    console.log('上传到云端按钮被点击');
                    
                    // 禁用按钮，防止重复点击
                    uploadButton.disabled = true;
                    uploadButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10" stroke-width="4"></circle><path d="M12 6v6l4 2"></path></svg> 上传中...';
                    
                    // 获取本地存储的股票数据
                    let stockData = {};
                    try {
                        const rawData = localStorage.getItem('user_stocks');
                        if (rawData) {
                            stockData = JSON.parse(rawData);
                            console.log('获取到本地存储的股票数据:', stockData);
                        } else {
                            showNotification('本地没有股票数据可上传', 'warning');
                            resetUploadButton();
                            return;
                        }
                    } catch (e) {
                        console.error('解析本地股票数据失败:', e);
                        showNotification('读取本地数据失败: ' + e.message, 'error');
                        resetUploadButton();
                        return;
                    }
                    
                    // 显示上传中提示
                    showNotification('正在上传数据到云端...', 'info');
                    
                    // 准备要发送的数据
                    const uploadData = {
                        action: 'upload',
                        stocks: stockData
                    };
                    
                    // 发送请求到服务器
                    fetch('api/sync_stocks.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(uploadData),
                        credentials: 'same-origin'
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应异常: ' + response.status);
                        }
                        return response.text();
                    })
                    .then(text => {
                        console.log('收到原始响应:', text);
                        // 尝试解析JSON
                        try {
                            const data = JSON.parse(text);
                            console.log('解析后的响应数据:', data);
                            
                            if (data.success) {
                                showNotification('数据已成功上传到云端', 'success');
                            } else {
                                showNotification('上传失败: ' + (data.message || '未知错误'), 'error');
                            }
                        } catch (e) {
                            console.error('解析响应JSON失败:', e);
                            throw new Error('无法解析服务器响应: ' + e.message);
                        }
                        resetUploadButton();
                    })
                    .catch(error => {
                        console.error('上传出错:', error);
                        showNotification('上传失败: ' + error.message, 'error');
                        resetUploadButton();
                    });
                    
                    function resetUploadButton() {
                        uploadButton.disabled = false;
                        uploadButton.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="17 8 12 3 7 8"></polyline>
                                <line x1="12" y1="3" x2="12" y2="15"></line>
                            </svg>
                            上传到云端
                        `;
                    }
                });
                
                // 下载按钮点击事件
                downloadButton.addEventListener('click', function() {
                    console.log('从云端同步按钮被点击');
                    
                    // 禁用按钮，防止重复点击
                    downloadButton.disabled = true;
                    downloadButton.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10" stroke-width="4"></circle><path d="M12 6v6l4 2"></path></svg> 同步中...';
                    
                    // 显示下载中提示
                    showNotification('正在从云端同步数据...', 'info');
                    
                    // 从服务器获取数据
                    downloadFromServer();
                    
                    function downloadFromServer(retryCount = 0) {
                        const maxRetries = 3;
                        
                        fetch('api/sync_stocks.php?action=download', {
                            method: 'GET',
                            credentials: 'same-origin'
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('网络响应异常: ' + response.status);
                            }
                            return response.text();
                        })
                        .then(text => {
                            console.log('收到原始响应:', text);
                            // 尝试解析JSON
                            try {
                                const data = JSON.parse(text);
                                console.log('解析后的响应数据:', data);
                                
                                if (data.success) {
                                    // 保存获取到的数据到本地
                                    if (data.stocks && Array.isArray(data.stocks)) {
                                        localStorage.setItem('user_stocks', JSON.stringify(data.stocks));
                                        showNotification('已成功从云端同步 ' + data.stocks.length + ' 支股票', 'success');
                                        
                                        // 重新加载股票列表
                                        if (typeof window.loadUserStocks === 'function') {
                                            window.loadUserStocks();
                                        } else if (window.FrontendStocks && typeof window.FrontendStocks.refreshStockList === 'function') {
                                            window.FrontendStocks.refreshStockList();
                                        }
                                    } else {
                                        showNotification('云端数据格式无效', 'error');
                                    }
                                } else {
                                    showNotification('同步失败: ' + (data.message || '未知错误'), 'error');
                                }
                            } catch (e) {
                                console.error('解析响应JSON失败:', e);
                                if (retryCount < maxRetries) {
                                    console.log(`第 ${retryCount + 1} 次重试...`);
                                    setTimeout(() => downloadFromServer(retryCount + 1), 1000);
                                    return;
                                }
                                throw new Error('无法解析服务器响应: ' + e.message);
                            }
                            resetDownloadButton();
                        })
                        .catch(error => {
                            console.error('同步出错:', error);
                            if (retryCount < maxRetries) {
                                console.log(`第 ${retryCount + 1} 次重试...`);
                                setTimeout(() => downloadFromServer(retryCount + 1), 1000);
                                return;
                            }
                            showNotification('同步失败: ' + error.message, 'error');
                            resetDownloadButton();
                        });
                    }
                    
                    function resetDownloadButton() {
                        downloadButton.disabled = false;
                        downloadButton.innerHTML = `
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                <polyline points="7 10 12 15 17 10"></polyline>
                                <line x1="12" y1="15" x2="12" y2="3"></line>
                            </svg>
                            从云端同步
                        `;
                    }
                });
            } else {
                console.error('云同步按钮未找到');
            }
            
            // 通知函数，如果不存在则定义
            if (typeof showNotification !== 'function') {
                window.showNotification = function(message, type = 'info') {
                    console.log(`[${type}] ${message}`);
                    // 简单的消息提示实现
                    const notificationDiv = document.createElement('div');
                    notificationDiv.className = `notification ${type}`;
                    notificationDiv.textContent = message;
                    document.body.appendChild(notificationDiv);
                    setTimeout(() => {
                        notificationDiv.classList.add('show');
                        setTimeout(() => {
                            notificationDiv.classList.remove('show');
                            setTimeout(() => {
                                document.body.removeChild(notificationDiv);
                            }, 300);
                        }, 3000);
                    }, 10);
                };
            }
        });
    </script>

    <!-- 移动端菜单控制脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 移动菜单控制
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileMenu = document.getElementById('mobileMenu');
            const body = document.body;
            const header = document.querySelector('header');
            
            // 修改滚动事件，只在顶部才显示标题栏，其他情况隐藏
            window.addEventListener('scroll', function() {
                let currentScroll = window.pageYOffset || document.documentElement.scrollTop;
                
                // 只在顶部显示标题栏，其他时候隐藏
                if (currentScroll <= 10) {
                    // 在页面顶部或接近顶部显示标题栏
                    header.style.transform = 'translateY(0)';
                } else {
                    // 否则隐藏标题栏
                    header.style.transform = 'translateY(-100%)';
                }
            }, { passive: true });
            
            // 页面加载时检查初始位置
            let initialScroll = window.pageYOffset || document.documentElement.scrollTop;
            if (initialScroll <= 10) {
                header.style.transform = 'translateY(0)';
            } else {
                header.style.transform = 'translateY(-100%)';
            }
            
            // 点击菜单图标切换菜单显示状态
            if (mobileMenuToggle) {
                mobileMenuToggle.addEventListener('click', function() {
                    mobileMenu.classList.toggle('active');
                    
                    // 添加一个遮罩，用于点击关闭菜单
                    if (mobileMenu.classList.contains('active')) {
                        const overlay = document.createElement('div');
                        overlay.className = 'mobile-menu-overlay';
                        body.appendChild(overlay);
                        
                        overlay.addEventListener('click', function() {
                            mobileMenu.classList.remove('active');
                            body.removeChild(overlay);
                        });
                    } else {
                        const existingOverlay = document.querySelector('.mobile-menu-overlay');
                        if (existingOverlay) {
                            body.removeChild(existingOverlay);
                        }
                    }
                });
            }
            
            // 监听窗口大小变化，调整菜单显示
            window.addEventListener('resize', function() {
                if (window.innerWidth > 992) {
                    mobileMenu.classList.remove('active');
                    
                    // 屏幕大小变化时，检查滚动位置，保持标题栏行为一致
                    let currentScroll = window.pageYOffset || document.documentElement.scrollTop;
                    if (currentScroll <= 10) {
                        header.style.transform = 'translateY(0)';
                    } else {
                        header.style.transform = 'translateY(-100%)';
                    }
                    
                    const existingOverlay = document.querySelector('.mobile-menu-overlay');
                    if (existingOverlay) {
                        body.removeChild(existingOverlay);
                    }
                }
            });
            
            // 移动菜单项点击事件
            const menuItems = document.querySelectorAll('.mobile-menu-item a');
            menuItems.forEach(function(item) {
                item.addEventListener('click', function() {
                    // 关闭菜单
                    mobileMenu.classList.remove('active');
                    
                    // 移除遮罩
                    const existingOverlay = document.querySelector('.mobile-menu-overlay');
                    if (existingOverlay) {
                        body.removeChild(existingOverlay);
                    }
                });
            });
        });
    </script>

    <!-- 消息通知窗口 -->
    <div class="message-modal" id="messageModal" style="display:none;">
        <div class="message-content">
            <span class="close-btn" onclick="closeMessageModal()">&times;</span>
            <h3 class="message-title"><i class="fas fa-envelope"></i> 系统消息</h3>
            <div class="message-body" id="messageBody"></div>
            <div class="message-time" id="messageTime"></div>
            <div class="message-actions">
                <button class="message-btn" onclick="markMessageAsRead()">已读</button>
            </div>
        </div>
    </div>
    
    <!-- 消息通知提示 -->
    <div class="message-notification" id="messageNotification" style="display:none;" onclick="showMessageModal()">
        <i class="fas fa-bell"></i> 您有新的系统消息
    </div>

    <script>
        // 反馈功能
        document.addEventListener('DOMContentLoaded', function() {
            // 获取元素
            const feedbackBtn = document.getElementById('feedbackBtn');
            const feedbackModal = document.getElementById('feedbackModal');
            const closeFeedbackBtn = document.getElementById('closeFeedbackBtn');
            const sendFeedbackBtn = document.getElementById('sendFeedbackBtn');
            const feedbackContent = document.getElementById('feedbackContent');
            
            if (feedbackBtn && feedbackModal && closeFeedbackBtn && sendFeedbackBtn && feedbackContent) {
                // 显示反馈模态框
                feedbackBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    feedbackModal.style.display = 'flex';
                    feedbackContent.focus();
                });
                
                // 关闭反馈模态框
                closeFeedbackBtn.addEventListener('click', function() {
                    feedbackModal.style.display = 'none';
                    feedbackContent.value = '';
                });
                
                // 点击模态框背景关闭
                feedbackModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        feedbackModal.style.display = 'none';
                        feedbackContent.value = '';
                    }
                });
                
                // 发送反馈
                sendFeedbackBtn.addEventListener('click', function() {
                    const content = feedbackContent.value.trim();
                    if (!content) {
                        showNotification('请输入反馈内容', 'warning');
                        return;
                    }
                    
                    // 禁用发送按钮
                    sendFeedbackBtn.disabled = true;
                    sendFeedbackBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
                    
                    // 获取用户名
                    const username = "<?php echo htmlspecialchars($username); ?>";
                    
                    // 准备消息内容
                    const messageContent = `
用户: ${username}
时间: ${new Date().toLocaleString()}
反馈内容: 
${content}
                    `;
                    
                    // 构造请求数据
                    const requestData = {
                        appToken: "AT_WzxW58F3AAoiza7biiSQquUq4FJHuBBl",
                        content: messageContent,
                        contentType: 1,
                        uids: ["UID_b0ac91i9ULC1bK4MV0zZTi2zZKhy"],
                        summary: "收到新的量化平台反馈"
                    };
                    
                    // 发送请求
                    fetch('https://wxpusher.zjiecode.com/api/send/message', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('网络响应异常: ' + response.status);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('反馈响应数据:', data);
                        
                        if (data.success) {
                            showNotification('反馈已成功发送，感谢您的反馈！', 'success');
                            feedbackModal.style.display = 'none';
                            feedbackContent.value = '';
                        } else {
                            showNotification('发送失败: ' + (data.msg || '未知错误'), 'error');
                        }
                    })
                    .catch(error => {
                        console.error('发送反馈出错:', error);
                        showNotification('发送失败: ' + error.message, 'error');
                    })
                    .finally(() => {
                        // 恢复发送按钮
                        sendFeedbackBtn.disabled = false;
                        sendFeedbackBtn.innerHTML = '<i class="fas fa-paper-plane"></i> 发送';
                    });
                });
                
                // 按Esc关闭模态框
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && feedbackModal.style.display === 'flex') {
                        feedbackModal.style.display = 'none';
                        feedbackContent.value = '';
                    }
                });
            }
        });
    </script>

    <!-- 清空聊天记录功能 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const clearChatBtn = document.getElementById('clearChatBtn');
            const chatContainer = document.getElementById('chatContainer');

            if (clearChatBtn && chatContainer) {
                clearChatBtn.addEventListener('click', function() {
                    // 保留初始提示信息
                    chatContainer.innerHTML = `
                        <div class="empty-message"><i class="fas fa-info-circle"></i> 量化时间通常需要50-70秒通常40-60秒,量化完成后会有弹窗提示,可通过股票卡片的记录功能查看</div>
                        <div class="empty-message"><i class="fas fa-history"></i> 量化结果只保存最近3条</div>
                    `;
                    // 滚动到底部
                    chatContainer.scrollTop = chatContainer.scrollHeight;
                });
            }
        });
    </script>

    <!-- 反馈对话框 -->
    <div class="feedback-modal" id="feedbackModal" style="display:none;">
        <div class="feedback-content">
            <span class="close-btn" id="closeFeedbackBtn">&times;</span>
            <h3 class="feedback-title"><i class="fas fa-comment-dots"></i> 意见反馈</h3>
            <div class="feedback-body">
                <textarea id="feedbackContent" placeholder="请输入您的反馈或咨询内容..."></textarea>
            </div>
            <div class="feedback-actions">
                <button id="sendFeedbackBtn" class="feedback-btn"><i class="fas fa-paper-plane"></i> 发送</button>
            </div>
        </div>
    </div>

    <script>
        // 保留原有的消息相关JavaScript代码
        let currentMessageId = null;
        let messageCheckInterval = null;
        
        document.addEventListener('DOMContentLoaded', function() {
            // 设置定时检查新消息
            checkForNewMessages();
            messageCheckInterval = setInterval(checkForNewMessages, 60000); // 每分钟检查一次
        });
        
        // 检查新消息
        function checkForNewMessages() {
            fetch('fly/api/message_api.php?action=get_messages')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应错误');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('消息检查响应:', data);
                    if (data.success && data.messages && data.messages.length > 0) {
                        // 显示最新的消息
                        const latestMessage = data.messages[0];
                        showMessageNotification();
                        currentMessageId = latestMessage.id;
                        
                        // 预加载消息内容
                        document.getElementById('messageBody').innerHTML = latestMessage.content;
                        document.getElementById('messageTime').innerText = '发送时间: ' + latestMessage.created_at;
                    }
                })
                .catch(error => {
                    console.error('获取消息出错:', error);
                });
        }
        
        // 显示消息通知提示
        function showMessageNotification() {
            const notification = document.getElementById('messageNotification');
            notification.style.display = 'block';
            
            // 5秒后自动显示消息弹窗
            setTimeout(() => {
                if (notification.style.display !== 'none') {
                    showMessageModal();
                }
            }, 5000);
        }
        
        // 显示消息弹窗
        function showMessageModal() {
            document.getElementById('messageNotification').style.display = 'none';
            document.getElementById('messageModal').style.display = 'flex';
        }
        
        // 关闭消息弹窗
        function closeMessageModal() {
            document.getElementById('messageModal').style.display = 'none';
        }
        
        // 标记消息为已读
        function markMessageAsRead() {
            if (!currentMessageId) return;
            
            fetch('fly/api/message_api.php?action=mark_read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message_id: currentMessageId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeMessageModal();
                    currentMessageId = null;
                }
            })
            .catch(error => {
                console.error('标记消息出错:', error);
            });
        }
        
        // 显示量化分析完成通知
        function showAnalysisNotification(stockName, stockCode) {
            // 创建通知元素
            const notificationDiv = document.createElement('div');
            notificationDiv.className = 'analysis-notification';
            
            // 设置通知内容
            notificationDiv.innerHTML = `
                <div class="analysis-notification-header">
                    <i class="fas fa-chart-line"></i>
                    <div class="analysis-notification-title">量化分析完成</div>
                </div>
                <div class="analysis-notification-content">
                    <div class="analysis-notification-code">${stockCode}</div>
                    <div class="analysis-notification-message">${stockName}</div>
                </div>
            `;
            
            // 添加到页面
            document.body.appendChild(notificationDiv);
            
            // 显示通知
            setTimeout(() => {
                notificationDiv.classList.add('show');
                
                // 3秒后自动隐藏
                setTimeout(() => {
                    notificationDiv.classList.remove('show');
                    setTimeout(() => {
                        if (notificationDiv.parentNode) {
                            document.body.removeChild(notificationDiv);
                        }
                    }, 400);
                }, 3000);
            }, 100);
        }
        
        // 点击模态框背景关闭
        window.addEventListener('DOMContentLoaded', function() {
            const messageModal = document.getElementById('messageModal');
            if (messageModal) {
                messageModal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeMessageModal();
                    }
                });
            }
        });
    </script>
</body>
</html>