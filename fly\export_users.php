<?php
session_start();

// 检查是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

// 数据库连接
$db_host = 'localhost';
$db_name = 'agpt';
$db_user = 'agpt';
$db_pass = 'hunterl6628096';

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}
$conn->set_charset("utf8mb4");

// 获取所有用户数据
$query = "SELECT id, username, api_key, remaining_requests, created_at FROM users ORDER BY id";
$result = $conn->query($query);

// 设置文件名和HTTP头
$filename = 'user_data_' . date('Ymd_His') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');

// 创建输出流
$output = fopen('php://output', 'w');

// 添加UTF-8 BOM，解决Excel中中文乱码问题
fprintf($output, "\xEF\xBB\xBF");

// 写入CSV标题行
fputcsv($output, ['ID', '用户名', 'API密钥', '剩余次数', '创建时间']);

// 写入数据行
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        fputcsv($output, [
            $row['id'],
            $row['username'],
            $row['api_key'] ?: '未设置',
            $row['remaining_requests'] ?? '0',
            $row['created_at']
        ]);
    }
}

// 关闭数据库连接
$conn->close();

// 结束脚本
exit;
?> 