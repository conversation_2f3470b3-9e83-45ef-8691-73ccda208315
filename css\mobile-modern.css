/* 移动端菜单样式 */
.mobile-menu-toggle {
  display: none;
  width: 2.5rem;
  height: 2.5rem;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--text-primary);
  margin-left: 0.75rem;
  border-radius: 50%;
  transition: background-color var(--transition);
}

.mobile-menu-toggle:hover {
  background-color: var(--neutral-200);
}

.mobile-menu {
  display: none;
  position: fixed;
  top: 0;
  left: -280px;
  width: 280px;
  height: 100%;
  background-color: var(--card-bg);
  z-index: 1000;
  box-shadow: var(--shadow-lg);
  transition: transform var(--transition-slow);
  overflow-y: auto;
  padding-top: 1rem;
}

.mobile-menu.active {
  transform: translateX(280px);
}

.mobile-menu-header {
  display: flex;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--neutral-200);
}

.mobile-menu-logo {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  margin-right: 0.75rem;
}

.mobile-menu-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
}

.mobile-menu-close {
  margin-left: auto;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: var(--text-muted);
  cursor: pointer;
  transition: background-color var(--transition);
}

.mobile-menu-close:hover {
  background-color: var(--neutral-200);
  color: var(--text-primary);
}

.mobile-menu-item {
  padding: 0;
  border-bottom: 1px solid var(--neutral-200);
}

.mobile-menu-item:last-child {
  border-bottom: none;
}

.mobile-menu-item a {
  display: flex;
  align-items: center;
  padding: 1rem;
  color: var(--text-primary);
  text-decoration: none;
  font-size: 1rem;
  transition: background-color var(--transition);
}

.mobile-menu-item a:hover {
  background-color: var(--neutral-100);
}

.mobile-menu-item a i {
  margin-right: 0.75rem;
  color: var(--primary-color);
  font-size: 1.25rem;
  width: 1.5rem;
  text-align: center;
}

.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  backdrop-filter: blur(2px);
  display: none;
}

/* 底部导航 - 在移动端显示 */
.mobile-nav {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: var(--card-bg);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 150;
  padding: 0.5rem 0;
  border-top: 1px solid var(--neutral-200);
}

.mobile-nav-items {
  display: flex;
  justify-content: space-around;
  width: 100%;
}

.mobile-nav-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  padding: 0.5rem !important;
  color: var(--text-muted);
  text-decoration: none;
  font-size: 0.75rem;
  transition: color var(--transition);
  flex: 1;
  height: 3.5rem !important; /* 统一高度 */
  justify-content: center !important;
}

.mobile-nav-item:hover, .mobile-nav-item.active {
  color: var(--primary-color);
}

.mobile-nav-item i {
  font-size: 1.25rem;
  margin-bottom: 0.25rem;
}

/* 移动端上的通知样式 */
.mobile-notification {
  position: fixed;
  top: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--card-bg);
  box-shadow: var(--shadow-md);
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  z-index: 9999;
  max-width: 90%;
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: var(--text-primary);
  animation: slideDown 0.3s forwards, fadeOut 0.3s forwards 2.7s;
}

.mobile-notification.success {
  border-left: 4px solid var(--success-color);
}

.mobile-notification.error {
  border-left: 4px solid var(--danger-color);
}

.mobile-notification.warning {
  border-left: 4px solid var(--warning-color);
}

.mobile-notification.info {
  border-left: 4px solid var(--info-color);
}

.mobile-notification i {
  margin-right: 0.5rem;
  font-size: 1.125rem;
}

.mobile-notification.success i {
  color: var(--success-color);
}

.mobile-notification.error i {
  color: var(--danger-color);
}

.mobile-notification.warning i {
  color: var(--warning-color);
}

.mobile-notification.info i {
  color: var(--info-color);
}

@keyframes slideDown {
  from {
    transform: translate(-50%, -100%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* 消息模态框样式优化 */
.message-modal {
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.message-content {
  background-color: var(--card-bg);
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 500px;
}

.message-title {
  color: var(--primary-color);
  border-bottom: 1px solid var(--neutral-200);
  padding-bottom: 0.75rem;
}

.message-body {
  max-height: 60vh;
  padding: 0.75rem 0;
}

.message-btn {
  background-color: var(--primary-color);
  transition: background-color var(--transition);
  padding: 0.625rem 1.25rem;
  font-weight: 500;
}

.message-btn:hover {
  background-color: var(--primary-dark);
}

/* 响应式适配 */
@media (max-width: 992px) {
  .mobile-menu-toggle {
    display: flex;
  }
  
  .desktop-only {
    display: none !important;
  }
  
  .container {
    padding: 0.75rem;
  }
  
  /* 为底部导航预留空间 */
  body {
    padding-bottom: 4rem;
  }
  
  /* 显示底部导航 */
  .mobile-nav {
    display: block;
  }
  
  /* 调整整体间距留白 */
  .sidebar section, 
  .main-content section {
    margin-bottom: 1rem;
  }
  
  /* 优化市场指数布局 */
  .market-indices {
    gap: 0.75rem;
  }
  
  /* 调整股票数据网格 */
  .stock-data-grid {
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .header-content {
    padding: 0.625rem 1rem;
  }
  
  /* 调整市场指数卡片紧凑度 */
  .index-card {
    padding: 0.75rem;
  }
  
  .index-details {
    grid-template-columns: 1fr;
  }
  
  /* 调整股票变动标签 */
  .changes-tab {
    flex: 1;
    text-align: center;
    font-size: 0.75rem;
    padding: 0.625rem 0.5rem;
  }
  
  /* 调整搜索区域 */
  .input-section {
    flex-direction: column;
  }
  
  .search-input {
    margin-bottom: 0.5rem;
  }
  
  .search-button {
    width: 100%;
  }
  
  /* 调整二维码区域 */
  .qrcode-content {
    padding: 1.25rem;
  }
  
  .qrcode-content img {
    max-width: 160px;
  }
}

@media (max-width: 480px) {
  /* 极小屏幕下的调整 */
  .section-title {
    font-size: 1rem;
    padding: 0.875rem 1rem;
  }
  
  .stock-name {
    font-size: 0.875rem;
  }
  
  .current-price {
    font-size: 1.125rem;
  }
  
  /* 极简化股票卡片 */
  .stock-header {
    padding: 0.75rem;
  }
  
  .stock-body {
    padding: 0.75rem;
  }
  
  /* 调整API信息显示区域 */
  .remaining-requests {
    font-size: 0.875rem;
    padding: 0.625rem 0.875rem;
  }
} 