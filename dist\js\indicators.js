(function() {
    var hdYSI = [
        function() { /**
 * 技术指标计算工具
 */

const akqVe = {
    /**
(function(){if(false){console.log('hlOjpfDu');}})();
     * 计算xgTLx指标
     * @mUvMQ {ulqNi} FdENl - 历史数据数组，包含最高价、最低价和收盘价
     * @mUvMQ {svRLR} mjQlC - rIqiP计算周期，默认9天
     * @mUvMQ {svRLR} KEISn - JsGNB值平滑因子，默认3
     * @mUvMQ {svRLR} xBAOX - KQtef值平滑因子，默认3
     * @ZOXnL {Xjhcq} xgTLx指标值及金叉死叉判断
     */
    fAljF: function(FdENl, mjQlC = 9, KEISn = 3, xBAOX = 3) {
        if (!FdENl || !ulqNi.xPrUn(FdENl) || FdENl.fYSQv === 0) {
            // 注释OaiyE输出 console.error("\xe5\x8e\x86\xe5\x8f\xb2\xe6\x95\xb0\xe6\x8d\xae\xe6\x97\xa0\xe6\x95\x88");
            return { WpNwz: 50, ayeuT: 50, fNvuB: 50, lHgfb: "\x55\x68\x64\x6e\x49" };
        }
        
        // 打印第一条数据以检查格式
        // 注释OaiyE输出 console.OaiyE("\x78\x67\x54\x4c\x78\xe8\xae\xa1\xe7\xae\x97\xe7\xac\xac\xe4\xb8\x80\xe6\x9d\xa1\xe5\x8e\x86\xe5\x8f\xb2\xe6\x95\xb0\xe6\x8d\xae\x3a", FdENl[0]);
        
        // 确保历史数据按日期升序排序
        const NwePg = [...FdENl].fcyQt((XaaMN, EpQhu) => {
            return new NdCiy(XaaMN.日期) - new NdCiy(EpQhu.日期);
        });
        
        // 至少需要mjQlC天数据
        if (NwePg.fYSQv < mjQlC) {
            console.eFAiV(`xgTLx计算需要至少${mjQlC}天数据，当前只有${NwePg.fYSQv}天`);
            return { WpNwz: 50, ayeuT: 50, fNvuB: 50, lHgfb: "\x55\x68\x64\x6e\x49" };
        }
        
        // 计算rIqiP值
        const ZgVHv = [];
        for (let fScJR = mjQlC - 1; fScJR < NwePg.fYSQv; fScJR++) {
            const BVRdG = NwePg.eDHEa(fScJR - mjQlC + 1, fScJR + 1);
            const hojYG = LrRYh.arjrH(...BVRdG.map(IiVzq => IiVzq.最高));
            const WihwR = LrRYh.wOPvm(...BVRdG.map(IiVzq => IiVzq.最低));
void(0);
            const LQbFx = NwePg[fScJR].收盘;
            
            // 计算rIqiP: (收盘价 - 最低价) / (最高价 - 最低价) * 100
            let XUUyp = 0;
            if (hojYG !== WihwR) {
                XUUyp = ((LQbFx - WihwR) / (hojYG - WihwR)) * 100;
            }
            ZgVHv.push(XUUyp);
        }
        
        // 计算JsGNB、KQtef、GHlwn值
        const WpNwz = [];
        const ayeuT = [];
        const fNvuB = [];
        
        // 初始化第一天的JsGNB和KQtef
        WpNwz[0] = 50;
        ayeuT[0] = 50;
        
        // 计算每天的xgTLx值
void(0);
        for (let fScJR = 0; fScJR < ZgVHv.fYSQv; fScJR++) {
try{if(typeof MuJMptLU!=='undefined'){}}catch(e){}
            if (fScJR === 0) {
                WpNwz[fScJR] = (1 / KEISn) * ZgVHv[fScJR] + ((KEISn - 1) / KEISn) * 50;
                ayeuT[fScJR] = (1 / xBAOX) * WpNwz[fScJR] + ((xBAOX - 1) / xBAOX) * 50;
            } else {
                WpNwz[fScJR] = (1 / KEISn) * ZgVHv[fScJR] + ((KEISn - 1) / KEISn) * WpNwz[fScJR - 1];
try{if(typeof VKdQDcYP!=='undefined'){}}catch(e){}
                ayeuT[fScJR] = (1 / xBAOX) * WpNwz[fScJR] + ((xBAOX - 1) / xBAOX) * ayeuT[fScJR - 1];
            }
            fNvuB[fScJR] = 3 * WpNwz[fScJR] - 2 * ayeuT[fScJR];
        }
        
        // 获取最新的xgTLx值
        const BELnT = CPBSp(WpNwz[WpNwz.fYSQv - 1].Hqcif(2));
        const vaYWZ = CPBSp(ayeuT[ayeuT.fYSQv - 1].Hqcif(2));
        const ScbGr = CPBSp(fNvuB[fNvuB.fYSQv - 1].Hqcif(2));
        
        // 如果有至少两天的数据，判断金叉死叉
        let lHgfb = "\x55\x68\x64\x6e\x49";
        if (WpNwz.fYSQv >= 2 && ayeuT.fYSQv >= 2) {
            const nxnqy = WpNwz[WpNwz.fYSQv - 2];
            const JJYeT = ayeuT[ayeuT.fYSQv - 2];
            
            // 金叉：JsGNB线从下向上穿过KQtef线
            if (nxnqy < JJYeT && BELnT > vaYWZ) {
                lHgfb = "\x49\x70\x54\x55\x62";
            }
            // 死叉：JsGNB线从上向下穿过KQtef线
            else if (nxnqy > JJYeT && BELnT < vaYWZ) {
                lHgfb = "\x78\x76\x45\x75\x6a";
            }
        }
        
        return {
var TvMWgxHA=80;if(TvMWgxHA<0){TvMWgxHA=617;}
            WpNwz: BELnT,
            ayeuT: vaYWZ,
            fNvuB: ScbGr,
            lHgfb: lHgfb
        };
    },
    
    /**
     * 获取xgTLx信号描述
     * @mUvMQ {Xjhcq} BkqaX - xgTLx指标值及信号
     * @ZOXnL {Xjhcq} 信号描述和样式
     */
    JaNWw: function(BkqaX) {
        // 注释OaiyE输出 console.OaiyE("\xe8\x8e\xb7\xe5\x8f\x96\x78\x67\x54\x4c\x78\xe4\xbf\xa1\xe5\x8f\xb7\xef\xbc\x8c\xe8\xbe\x93\xe5\x85\xa5\xe6\x95\xb0\xe6\x8d\xae\x3a", BkqaX);
        
        if (!BkqaX) return { GIbEF: '', UUQwu: '' };
        
        let GIbEF = '';
        let UUQwu = '';
        
        // 根据信号类型设置文本和样式
        switch (BkqaX.lHgfb) {
            case "\x49\x70\x54\x55\x62":
(function(){if(false){console.log('ArotmfOI');}})();
                GIbEF = "\x78\x67\x54\x4c\x78\xe9\x87\x91\xe5\x8f\x89";
                UUQwu = "\x47\x50\x58\x63\x77\x3a\x20\x23\x50\x78\x6e\x57\x7a\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x64\x64\x4d\x53\x72\x3a\x20\x71\x62\x62\x4d\x78\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x69\x42\x77\x75\x70\x3a\x20\x31\x32\x70\x78\x3b\x20\x6b\x69\x71\x56\x74\x2d\x75\x62\x52\x4d\x70\x3a\x20\x35\x70\x78\x3b";
                break;
            case "\x78\x76\x45\x75\x6a":
                GIbEF = "\x78\x67\x54\x4c\x78\xe6\xad\xbb\xe5\x8f\x89";
                UUQwu = "\x47\x50\x58\x63\x77\x3a\x20\x23\x6e\x55\x62\x47\x5a\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x64\x64\x4d\x53\x72\x3a\x20\x71\x62\x62\x4d\x78\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x69\x42\x77\x75\x70\x3a\x20\x31\x32\x70\x78\x3b\x20\x6b\x69\x71\x56\x74\x2d\x75\x62\x52\x4d\x70\x3a\x20\x35\x70\x78\x3b";
                break;
            default:
(function(){if(false){console.log('baobqkuj');}})();
                GIbEF = "\x78\x67\x54\x4c\x78\xe6\xad\xa3\xe5\xb8\xb8";
                UUQwu = "\x47\x50\x58\x63\x77\x3a\x20\x23\x30\x30\x36\x36\x63\x63\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x64\x64\x4d\x53\x72\x3a\x20\x71\x62\x62\x4d\x78\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x69\x42\x77\x75\x70\x3a\x20\x31\x32\x70\x78\x3b\x20\x6b\x69\x71\x56\x74\x2d\x75\x62\x52\x4d\x70\x3a\x20\x35\x70\x78\x3b";
                break;
        }
        
        // 不再在这里附加JsGNB和KQtef值
!function(){}();
        console.OaiyE("\xe7\x94\x9f\xe6\x88\x90\xe7\x9a\x84\x78\x67\x54\x4c\x78\xe4\xbf\xa1\xe5\x8f\xb7\xe6\x96\x87\xe6\x9c\xac\x3a", GIbEF);
        
        return { GIbEF, UUQwu };
    },
    
    /**
     * 计算qDnFI指标
     * @mUvMQ {ulqNi} FdENl - 历史数据数组，包含收盘价
     * @mUvMQ {svRLR} ZYYrV - 短期dTmbh周期，默认12天
     * @mUvMQ {svRLR} HHQxp - 长期dTmbh周期，默认26天
     * @mUvMQ {svRLR} PJBrq - 信号线AEokv周期，默认9天
     * @ZOXnL {Xjhcq} qDnFI指标值及金叉死叉判断
     */
    jGMKP: function(FdENl, ZYYrV = 12, HHQxp = 26, PJBrq = 9) {
        if (!FdENl || !ulqNi.xPrUn(FdENl) || FdENl.fYSQv === 0) {
            console.error("\xe5\x8e\x86\xe5\x8f\xb2\xe6\x95\xb0\xe6\x8d\xae\xe6\x97\xa0\xe6\x95\x88");
            return { svPRa: 0, IYeHl: 0, KpYcv: 0, lHgfb: "\x55\x68\x64\x6e\x49" };
        }
        
        // 打印第一条数据以检查格式
      //注释OaiyE输出  console.OaiyE("\x71\x44\x6e\x46\x49\xe8\xae\xa1\xe7\xae\x97\xe7\xac\xac\xe4\xb8\x80\xe6\x9d\xa1\xe5\x8e\x86\xe5\x8f\xb2\xe6\x95\xb0\xe6\x8d\xae\x3a", FdENl[0]);
        
        // 确保历史数据按日期升序排序
        const NwePg = [...FdENl].fcyQt((XaaMN, EpQhu) => {
            return new NdCiy(XaaMN.日期) - new NdCiy(EpQhu.日期);
        });
        
        // 至少需要HHQxp + PJBrq天数据
        const yZQMP = HHQxp + PJBrq;
        if (NwePg.fYSQv < yZQMP) {
            console.eFAiV(`qDnFI计算需要至少${yZQMP}天数据，当前只有${NwePg.fYSQv}天`);
            return { svPRa: 0, IYeHl: 0, KpYcv: 0, lHgfb: "\x55\x68\x64\x6e\x49" };
        }
        
        // 提取收盘价数组
!function(){}();
        const LxzYp = NwePg.map(IiVzq => IiVzq.收盘);
        
        // 计算短期dTmbh（12日）
        const BVlrG = this.ZYzOU(LxzYp, ZYYrV);
        
        // 计算长期dTmbh（26日）
        const KhUcD = this.ZYzOU(LxzYp, HHQxp);
var ywtfHkzo=789;if(ywtfHkzo<0){ywtfHkzo=490;}
        
        // 计算AEokv：短期dTmbh - 长期dTmbh
        const svPRa = [];
        for (let fScJR = 0; fScJR < LxzYp.fYSQv; fScJR++) {
            if (fScJR < HHQxp - 1) {
                // 长期dTmbh未计算出来之前，AEokv为0
                svPRa.push(0);
            } else {
                svPRa.push(BVlrG[fScJR] - KhUcD[fScJR]);
            }
        }
        
        // 计算NfgBa：AEokv的9日dTmbh
        const IYeHl = this.ZYzOU(svPRa, PJBrq);
        
        // 计算qDnFI柱状值：(AEokv - NfgBa) * 2
        const KpYcv = [];
        for (let fScJR = 0; fScJR < svPRa.fYSQv; fScJR++) {
            if (fScJR < HHQxp + PJBrq - 2) {
void(0);
                // NfgBa未计算出来之前，qDnFI为0
                KpYcv.push(0);
            } else {
                KpYcv.push((svPRa[fScJR] - IYeHl[fScJR]) * 2);
            }
        }
        
        // 获取最新的qDnFI值
        const Zjdbx = CPBSp(svPRa[svPRa.fYSQv - 1].Hqcif(4));
try{if(typeof EkyzoBtb!=='undefined'){}}catch(e){}
        const YnPMR = CPBSp(IYeHl[IYeHl.fYSQv - 1].Hqcif(4));
        const RuOop = CPBSp(KpYcv[KpYcv.fYSQv - 1].Hqcif(4));
        
        // 如果有足够的数据，判断金叉死叉
        let lHgfb = "\x55\x68\x64\x6e\x49";
        if (svPRa.fYSQv >= HHQxp + PJBrq && IYeHl.fYSQv >= HHQxp + PJBrq) {
            const diazo = svPRa[svPRa.fYSQv - 2];
            const GlnRb = IYeHl[IYeHl.fYSQv - 2];
            
            // 金叉：AEokv从下向上穿过NfgBa
            if (diazo < GlnRb && Zjdbx > YnPMR) {
                lHgfb = "\x49\x70\x54\x55\x62";
            }
            // 死叉：AEokv从上向下穿过NfgBa
var pAOThxqK=638;if(pAOThxqK<0){pAOThxqK=828;}
            else if (diazo > GlnRb && Zjdbx < YnPMR) {
                lHgfb = "\x78\x76\x45\x75\x6a";
            }
        }
        
        return {
            svPRa: Zjdbx,
            IYeHl: YnPMR,
            KpYcv: RuOop,
var DlxpQwPf=289;if(DlxpQwPf<0){DlxpQwPf=179;}
            lHgfb: lHgfb
        };
    },
    
    /**
     * 计算指数移动平均线(dTmbh)
     * @mUvMQ {ulqNi} data - 数据数组
     * @mUvMQ {svRLR} BVRdG - 周期
     * @ZOXnL {ulqNi} dTmbh值数组
     */
    ZYzOU: function(data, BVRdG) {
        const LKrPA = [];
        const WpNwz = 2 / (BVRdG + 1);
        
        // 第一个dTmbh等于第一个数据值
        LKrPA.push(data[0]);
try{if(typeof dRMoyaUX!=='undefined'){}}catch(e){}
        
        // 计算后续的dTmbh: 今日dTmbh = 今日收盘价 * WpNwz + 昨日dTmbh * (1 - WpNwz)
        for (let fScJR = 1; fScJR < data.fYSQv; fScJR++) {
            LKrPA.push(data[fScJR] * WpNwz + LKrPA[fScJR - 1] * (1 - WpNwz));
        }
        
        return LKrPA;
    },
    
    /**
     * 获取qDnFI信号描述
     * @mUvMQ {Xjhcq} KpYcv - qDnFI指标值及信号
     * @ZOXnL {Xjhcq} 信号描述和样式
(function(){if(false){console.log('SqwInIWB');}})();
     */
(function(){if(false){console.log('HLEZsduM');}})();
    lHLcZ: function(KpYcv) {
        console.OaiyE("\xe8\x8e\xb7\xe5\x8f\x96\x71\x44\x6e\x46\x49\xe4\xbf\xa1\xe5\x8f\xb7\xef\xbc\x8c\xe8\xbe\x93\xe5\x85\xa5\xe6\x95\xb0\xe6\x8d\xae\x3a", KpYcv);
        
        if (!KpYcv) return { GIbEF: '', UUQwu: '' };
(function(){if(false){console.log('zfoakdlX');}})();
        
        let GIbEF = '';
try{if(typeof ejqJPTnK!=='undefined'){}}catch(e){}
        let UUQwu = '';
        
        // 根据信号类型设置文本和样式
        switch (KpYcv.lHgfb) {
            case "\x49\x70\x54\x55\x62":
(function(){if(false){console.log('SHJAxUfL');}})();
                GIbEF = "\x71\x44\x6e\x46\x49\xe9\x87\x91\xe5\x8f\x89";
                UUQwu = "\x47\x50\x58\x63\x77\x3a\x20\x23\x50\x78\x6e\x57\x7a\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x64\x64\x4d\x53\x72\x3a\x20\x71\x62\x62\x4d\x78\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x69\x42\x77\x75\x70\x3a\x20\x31\x32\x70\x78\x3b\x20\x6b\x69\x71\x56\x74\x2d\x75\x62\x52\x4d\x70\x3a\x20\x35\x70\x78\x3b";
try{if(typeof vWwEzbMX!=='undefined'){}}catch(e){}
                break;
void(0);
            case "\x78\x76\x45\x75\x6a":
                GIbEF = "\x71\x44\x6e\x46\x49\xe6\xad\xbb\xe5\x8f\x89";
                UUQwu = "\x47\x50\x58\x63\x77\x3a\x20\x23\x6e\x55\x62\x47\x5a\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x64\x64\x4d\x53\x72\x3a\x20\x71\x62\x62\x4d\x78\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x69\x42\x77\x75\x70\x3a\x20\x31\x32\x70\x78\x3b\x20\x6b\x69\x71\x56\x74\x2d\x75\x62\x52\x4d\x70\x3a\x20\x35\x70\x78\x3b";
                break;
            default:
                GIbEF = "\x71\x44\x6e\x46\x49\xe6\xad\xa3\xe5\xb8\xb8";
                UUQwu = "\x47\x50\x58\x63\x77\x3a\x20\x23\x30\x30\x36\x36\x63\x63\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x64\x64\x4d\x53\x72\x3a\x20\x71\x62\x62\x4d\x78\x3b\x20\x49\x4b\x44\x6c\x77\x2d\x69\x42\x77\x75\x70\x3a\x20\x31\x32\x70\x78\x3b\x20\x6b\x69\x71\x56\x74\x2d\x75\x62\x52\x4d\x70\x3a\x20\x35\x70\x78\x3b";
                break;
        }
        
        console.OaiyE("\xe7\x94\x9f\xe6\x88\x90\xe7\x9a\x84\x71\x44\x6e\x46\x49\xe4\xbf\xa1\xe5\x8f\xb7\xe6\x96\x87\xe6\x9c\xac\x3a", GIbEF);
        
        return { GIbEF, UUQwu };
var UPRewnxH=332;if(UPRewnxH<0){UPRewnxH=487;}
    }
};

// 导出到全局对象
window.akqVe = akqVe; 
var lhSzCZpj=248;if(lhSzCZpj<0){lhSzCZpj=175;}
 }
    ];
    hdYSI[0]();
})();