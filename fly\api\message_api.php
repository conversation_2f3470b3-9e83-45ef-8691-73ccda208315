<?php
// 设置响应头
header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');

// 加载配置文件
require_once dirname(__FILE__) . '/../includes/config.php';
require_once dirname(__FILE__) . '/../includes/functions.php';

// 错误处理函数
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode(['success' => false, 'message' => $message]);
    exit;
}

// 为用户页面和管理员页面都提供服务
// 获取用户名 - 检查多种会话变量
$isAdmin = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
$isUser = isset($_SESSION['user_id']);

if (!$isAdmin && !$isUser) {
    sendError('您未登录', 401);
}

// 获取用户名
$username = '';
if ($isAdmin) {
    $username = $_SESSION['admin_username'] ?? '';
} else {
    $username = $_SESSION['username'] ?? '';
}

if (empty($username)) {
    sendError('无法获取用户信息', 401);
}

// 获取数据库连接
$db_host = 'localhost';
$db_name = 'agpt';
$db_user = 'agpt';
$db_pass = 'hunterl6628096';

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// 检查连接
if ($conn->connect_error) {
    sendError("数据库连接失败: " . $conn->connect_error, 500);
}
$conn->set_charset("utf8mb4");

// 获取请求方法和操作
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

// 根据请求方法和操作进行处理
switch ($method) {
    case 'GET':
        if ($action === 'get_messages') {
            // 获取用户的未读消息
            $sql = "SELECT * FROM messages WHERE 
                    (username = ? AND is_read = 0) 
                    ORDER BY created_at DESC 
                    LIMIT 10";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('s', $username);
            $stmt->execute();
            $result = $stmt->get_result();
            
            $messages = [];
            if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    $messages[] = [
                        'id' => $row['id'],
                        'content' => $row['content'],
                        'created_at' => $row['created_at']
                    ];
                }
            }
            
            echo json_encode(['success' => true, 'messages' => $messages]);
        } else {
            sendError('未知操作');
        }
        break;
        
    case 'POST':
        if ($action === 'mark_read') {
            // 获取消息ID
            $data = json_decode(file_get_contents('php://input'), true);
            $message_id = $data['message_id'] ?? 0;
            
            if (!$message_id) {
                sendError('消息ID不能为空');
            }
            
            // 标记消息为已读
            $sql = "UPDATE messages SET is_read = 1 
                    WHERE id = ? AND username = ?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param('is', $message_id, $username);
            $stmt->execute();
            
            if ($stmt->affected_rows > 0) {
                echo json_encode(['success' => true, 'message' => '消息已标记为已读']);
            } else {
                sendError('标记消息失败或消息不存在');
            }
        } else {
            sendError('未知操作');
        }
        break;
        
    default:
        sendError('不支持的请求方法');
}

// 关闭数据库连接
$conn->close();
?> 