<?php
// 先加载配置文件，它会处理会话启动
require_once 'includes/config.php';
require_once 'includes/functions.php';

// 检查是否已登录
if (!isLoggedIn()) {
    header('Location: index.php');
    exit;
}

// 获取当前用户信息
$user = getCurrentUser();

// 允许所有登录用户访问
if (!isset($user) || empty($user)) {
    header('Location: dashboard.php');
    exit;
}

// 处理表单提交
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_stocks':
                // 执行更新股票名称脚本
                $output = [];
                $returnVal = 0;
                exec('php update_stock_names.php 2>&1', $output, $returnVal);
                
                if ($returnVal === 0) {
                    $message = '股票名称列表更新成功';
                    $messageType = 'success';
                } else {
                    $message = '更新失败: ' . implode('<br>', $output);
                    $messageType = 'error';
                }
                break;
                
            case 'clear_cache':
                // 清除股票名称缓存
                if (file_exists('cache/stock_names.json')) {
                    unlink('cache/stock_names.json');
                }
                if (file_exists('cache/stock_names_index.json')) {
                    unlink('cache/stock_names_index.json');
                }
                $message = '股票名称缓存已清除';
                $messageType = 'success';
                break;
        }
    }
}

// 获取缓存信息
$cacheFile = 'cache/stock_names.json';
$indexFile = 'cache/stock_names_index.json';
$cacheExists = file_exists($cacheFile);
$indexExists = file_exists($indexFile);
$cacheTime = $cacheExists ? date('Y-m-d H:i:s', filemtime($cacheFile)) : '未缓存';
$cacheSize = $cacheExists ? round(filesize($cacheFile) / 1024, 2) . ' KB' : '0 KB';
$stockCount = 0;

if ($indexExists) {
    $indexData = json_decode(file_get_contents($indexFile), true);
    $stockCount = is_array($indexData) ? count($indexData) : 0;
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票管理 - <?php echo WEBSITE_NAME; ?></title>
    <link rel="icon" href="<?php echo WEBSITE_LOGO; ?>" type="image/png">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .admin-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        .admin-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .admin-title {
            margin-top: 0;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 8px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .admin-button {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .update-button {
            background-color: #4CAF50;
            color: white;
        }
        .clear-button {
            background-color: #f44336;
            color: white;
        }
        .message {
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        .success {
            background-color: #dff0d8;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }
        .error {
            background-color: #f2dede;
            color: #a94442;
            border: 1px solid #ebccd1;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <h1><img src="<?php echo WEBSITE_LOGO; ?>" alt="网站图标"><?php echo WEBSITE_NAME; ?></h1>
                <div class="user-info">
                    欢迎，<?php echo htmlspecialchars($user['username']); ?>
                    <a href="dashboard.php">返回个人中心</a>
                    <a href="logout.php">退出登录</a>
                </div>
            </div>
        </header>
        
        <main>
            <div class="admin-container">
                <h2>股票数据管理</h2>
                
                <?php if (!empty($message)): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
                <?php endif; ?>
                
                <div class="admin-section">
                    <h3 class="admin-title">股票名称缓存信息</h3>
                    <div class="info-item">
                        <span>缓存状态:</span>
                        <span><?php echo $cacheExists ? '已缓存' : '未缓存'; ?></span>
                    </div>
                    <div class="info-item">
                        <span>股票数量:</span>
                        <span><?php echo $stockCount; ?></span>
                    </div>
                    <div class="info-item">
                        <span>最后更新时间:</span>
                        <span><?php echo $cacheTime; ?></span>
                    </div>
                    <div class="info-item">
                        <span>缓存大小:</span>
                        <span><?php echo $cacheSize; ?></span>
                    </div>
                    
                    <form method="post" action="">
                        <div class="button-group">
                            <button type="submit" name="action" value="update_stocks" class="admin-button update-button">更新股票名称列表</button>
                            <button type="submit" name="action" value="clear_cache" class="admin-button clear-button">清除缓存</button>
                        </div>
                    </form>
                </div>
                
                <div class="admin-section">
                    <h3 class="admin-title">操作说明</h3>
                    <ul>
                        <li>更新股票名称列表：从API获取最新的股票名称数据并缓存到本地</li>
                        <li>清除缓存：删除本地缓存的股票名称数据</li>
                    </ul>
                    <p>注意：此功能为维护功能,不知道什么用不要瞎点!</p>
                </div>
            </div>
        </main>
        
        <footer>
            <p>&copy; <?php echo date('Y'); ?> <?php echo WEBSITE_NAME; ?> | 版权所有</p>
        </footer>
    </div>
</body>
</html> 