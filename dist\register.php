<?php
// 先加载配置文件，它会处理会话启动
require_once 'includes/config.php';
require_once 'includes/functions.php';

// 检查是否已登录，如果已登录则重定向到仪表盘
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

// 处理注册表单提交
$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = isset($_POST['username']) ? sanitizeInput($_POST['username']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
    
    if (empty($username)) {
        $error = '请输入用户名';
    } elseif (empty($password)) {
        $error = '请输入密码';
    } elseif ($password !== $confirm_password) {
        $error = '两次输入的密码不一致';
    } elseif (strlen($password) < 6) {
        $error = '密码长度不能少于6个字符';
    } else {
        try {
            // 连接数据库
            $db = connectDB();
            
            // 检查用户名是否已存在
            $stmt = $db->prepare("SELECT id FROM users WHERE username = ?");
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $error = '用户名已存在';
            } else {
                // 密码加密
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // 输出调试信息
                if (DEBUG_MODE) {
                    error_log("即将插入用户: $username");
                }
                
                // 插入新用户(不包含email字段)
                $stmt = $db->prepare("INSERT INTO users (username, password_hash) VALUES (?, ?)");
                if (!$stmt) {
                    $error = "预处理失败: " . $db->error;
                } else {
                    $stmt->bind_param("ss", $username, $hashed_password);
                    
                    if ($stmt->execute()) {
                        $success = '注册成功，请登录';
                        if (DEBUG_MODE) {
                            error_log("用户注册成功: $username");
                        }
                    } else {
                        $error = '注册失败: ' . $stmt->error;
                        if (DEBUG_MODE) {
                            error_log("用户注册失败: " . $stmt->error);
                        }
                    }
                }
            }
        } catch (Exception $e) {
            $error = '注册失败: ' . $e->getMessage();
            if (DEBUG_MODE) {
                error_log("注册异常: " . $e->getMessage());
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - <?php echo WEBSITE_NAME; ?></title>
    <link rel="icon" href="<?php echo WEBSITE_LOGO; ?>" type="image/png">
    <!-- 内联反调试代码 -->
    <script>
    (function() {
        // 提前标记已加载，防止重复
        window.__antiDebugLoaded = true;
        
        // 初始设置 - 非本地环境启用保护
        var isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
        if(!isProduction) return;
        
        // 记录原始console方法
        var _console = {
            log: console.log,
            warn: console.warn,
            error: console.error,
            info: console.info,
            debug: console.debug
        };
        
        // 立即禁用控制台 - 早期保护
        console.log = console.warn = console.error = console.info = console.debug = function(){};
        
        // 禁用F12和其他开发工具快捷键
        document.addEventListener('keydown', function(e) {
            if(e.key === 'F12' || 
              (e.ctrlKey && e.shiftKey && e.key === 'I') || 
              (e.ctrlKey && e.shiftKey && e.key === 'J') ||
              (e.ctrlKey && e.key === 'U')) {
                e.preventDefault();
                return false;
            }
        }, true);
        
        // 检测是否已打开开发工具
        function isDevToolsOpened() {
            var threshold = 160;
            var widthThreshold = 160;
            var heightThreshold = 160;
            
            // 检测大小差异
            var widthDiff = window.outerWidth - window.innerWidth > widthThreshold;
            var heightDiff = window.outerHeight - window.innerHeight > heightThreshold;
            
            return widthDiff || heightDiff;
        }
        
        // 周期性检查 - 使用非阻塞方式
        var checkInterval;
        
        function startChecking() {
            if(!checkInterval) {
                checkInterval = setInterval(function() {
                    if(isDevToolsOpened()) {
                        // 检测到开发工具，但不重定向，只禁用功能
                        disableDevTools();
                    }
                }, 1000);
            }
        }
        
        // 禁用开发工具而不是重定向
        function disableDevTools() {
            // 覆盖控制台对象，而不是重定向
            Object.defineProperty(window, 'console', {
                get: function() {
                    return {
                        log: function(){},
                        warn: function(){},
                        error: function(){},
                        info: function(){},
                        debug: function(){}
                    };
                },
                set: function(){}
            });
            
            // 可以添加其他措施，但不影响核心功能
        }
        
        // 页面加载后启动检查
        if(document.readyState === 'complete') {
            startChecking();
        } else {
            window.addEventListener('load', startChecking);
        }
    })();
    </script>
    <script src="js/anti-debug.js"></script>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/mobile.css">
</head>
<body class="auth-page">
    <div class="container">
        <div class="auth-wrapper">
            <div class="auth-features">
              <h1>π弈-专业个股量化平台</h1>
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-icon">📈</div>
                        <h3>全维度数据融合</h3>
                        <p>多模型配合+多源数据接入,非结构化解析:财务指标300+字段,产业链/舆情/龙虎榜</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📊</div>
                        <h3>智能模型矩阵</h3>
                        <p>自适应参数优化MACD/KDJ解决传统指标滞后性问题.主力资金拆解算法识别隐形大单与筹码异动</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🤖</div>
                        <h3>Agent决策矩阵</h3>
                        <p>技术+事件量化驱动走势预测,政策-行业-个股传导链分析.风险控制模块根据波动率动态仓位调整</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">⭐</div>
                        <h3>专注个股</h3>
                        <p>个性化投资组合管理,快速提取关键信号.通过龙虎榜席位资金匹配算法，预警"涨停敢死队"介入标的准确率82.6%</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📅</div>
                        <h3>动态决策输出</h3>
                        <p>科学策略建议,避免情绪化交易.动态权重评分模型,缩短80%的标的筛选时间,自动计算止损位</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">💡</div>
                        <h3>Deepseek评价</h3>
                        <p>即使不追求全自动量化，该平台已具备显著优势,信息整合效率比人工复盘快10倍以上</p>
                    </div>
                </div>
            </div>

            <div class="register-container">
                <div class="auth-logo">
                    <img src="<?php echo WEBSITE_LOGO; ?>" alt="Logo">
                </div>
                
                <h2 class="form-title">用户注册</h2>
              <p class="form-subtitle">为保证算力,暂停新用户注册.</a></p>
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success"><?php echo $success; ?></div>
                    <div class="form-footer">
                        <a href="index.php" class="submit-btn" style="display: inline-block; text-align: center; text-decoration: none; margin-top: 15px;">前往登录</a>
                    </div>
                <?php else: ?>
                  <!--  <form method="post" action="">
                        <div class="form-group">
                            <label for="username">用户名</label>
                            <input type="text" id="username" name="username" class="form-control" value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="password">密码</label>
                            <input type="password" id="password" name="password" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">确认密码</label>
                            <input type="password" id="confirm_password" name="confirm_password" class="form-control" required>
                        </div>
                        
                      <button type="submit" class="submit-btn">注册</button>
                    </form>
                    
                    <div class="form-footer">
                        已有账号？<a href="index.php">立即登录</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <footer>
            <p>&copy; <?php echo date('Y'); ?> <?php echo WEBSITE_NAME; ?> - 专业的个股量化平台</p>
        </footer>
    </div>
</body>
</html> 