/**
 * 高级反调试和代码保护工具
 * 检测到调试模式开启后隐藏关键代码并混淆
 */
(function() {
    // 
    const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
    
    // 
    if (typeof window.__antiDebugLoaded !== 'undefined') {
        // 
        enhanceProtection();
        return;
    }
    
    // 
    window.__antiDebugLoaded = true;
    
    // 
    if (isProduction) {
        // 
        console.log = console.info = console.warn = console.error = console.debug = console.trace = function() {
            // 
        };
        
        //
        setupBasicProtection();
    }
    
    // 
    function enhanceProtection() {
        if (!isProduction) return;
        
        // 
        
        // 1
        function detectDevToolsViaStack() {
            try {
                throw new Error();
            } catch (e) {
                const stack = e.stack || '';
                return stack.indexOf('chrome-extension') > -1 || 
                       stack.indexOf('devtools://') > -1;
            }
        }
        
        // 2. 
        let lastNodeCount = document.querySelectorAll('*').length;
        function checkNodeCountChange() {
            const currentNodeCount = document.querySelectorAll('*').length;
            const significant = Math.abs(currentNodeCount - lastNodeCount) > 10;
            lastNodeCount = currentNodeCount;
            return significant && detectDevToolsViaElement();
        }
        
        // 3.
        function detectDevToolsViaElement() {
            const div = document.createElement('div');
            Object.defineProperty(div, 'id', {
                get: function() {
                    return false;
                }
            });
            
            // 
            try {
                const result = !!div.id.toString();
                return result;
            } catch (e) {
                return false;
            }
        }
        
        // 
        let enhancedCheckCount = 0;
        const maxEnhancedChecks = 10; // 
        
        const enhancedTimer = setInterval(function() {
            // 限制运行次数
            if (enhancedCheckCount++ > maxEnhancedChecks) {
                clearInterval(enhancedTimer);
                return;
            }
            
            try {
                // 
                if (detectDevToolsViaStack() || checkNodeCountChange()) {
                    // 
                    // 只清除敏感数据
                    clearSensitiveData();
                }
            } catch (e) {
                // 
            }
        }, 3000); // 3秒检查一次，减少性能影响
        
        // 清除可能的敏感数据
        function clearSensitiveData() {
            try {
                // 
                // 
                
                // 
                if (window.localStorage) {
                    // 
                    const keysToPreserve = [
                        'use_frontend_storage',
                        'auto_refresh_enabled',
                        'user_stocks'
                    ];
                    
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        if (key && !keysToPreserve.includes(key) && key.indexOf('stock_') !== 0) {
                            localStorage.removeItem(key);
                        }
                    }
                }
                
                // 
                if (typeof window.userApiKey !== 'undefined') {
                    window.userApiKey = '******'; // 
                }
            } catch (e) {
                // 静默失败
            }
        }
    }
    
    // 
    function setupBasicProtection() {
        // 防止查看源代码
        document.addEventListener('contextmenu', function(e) {
            // 只在非输入区域禁用右键
            if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                return false;
            }
        });

        // 
        document.addEventListener('keydown', function(e) {
            if (
                // F12 键
                e.key === 'F12' || 
                // Ctrl+Shift+I (打开开发者工具)
                (e.ctrlKey && e.shiftKey && e.key === 'I') || 
                // Ctrl+Shift+J (打开控制台)
                (e.ctrlKey && e.shiftKey && e.key === 'J') ||
                // Ctrl+U (查看源码)
                (e.ctrlKey && e.key === 'U')
            ) {
                e.preventDefault();
                return false;
            }
        });
    }
    
    // 
    enhanceProtection();
})(); 