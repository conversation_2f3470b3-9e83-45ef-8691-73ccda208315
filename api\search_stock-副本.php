<?php
// 首先关闭错误显示，防止错误输出影响JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 必须在输出前启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once '../includes/functions.php';

// 增加会话调试信息，使用error_log而非直接输出
error_log("检查会话状态: SESSION=" . json_encode($_SESSION ?? []));
error_log("Cookie信息: " . json_encode($_COOKIE));
error_log("当前会话状态: user_id=" . ($_SESSION['user_id'] ?? '未设置'));

// 允许未登录用户访问API
// if (!isset($_SESSION['username'])) {
//     http_response_code(401);
//     echo json_encode(['error' => '请先登录']);
//     exit;
// }

// 获取搜索关键词 - 同时支持query和keyword参数
$keyword = $_GET['keyword'] ?? $_GET['query'] ?? '';

if (empty($keyword)) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => '请输入搜索关键词']);
    exit;
}

try {
    $db = connectDB();
    // 搜索股票
    $stmt = $db->prepare("
        SELECT DISTINCT id as stock_id, code as stock_code, name as stock_name 
        FROM stocks 
        WHERE code LIKE ? OR name LIKE ? 
        LIMIT 10
    ");
    $searchTerm = "%{$keyword}%";
    $stmt->bind_param("ss", $searchTerm, $searchTerm);
    $stmt->execute();
    $result = $stmt->get_result();
    
    $results = [];
    while ($row = $result->fetch_assoc()) {
        $results[] = $row;
    }
    
    // 如果数据库没有找到结果，尝试从本地缓存文件查找
    if (empty($results)) {
        // 尝试从缓存读取
        $cacheFile = "../cache/stock_names.json";
        if (file_exists($cacheFile)) {
            $cachedNames = file_get_contents($cacheFile);
            $nameData = json_decode($cachedNames, true);
            
            if (is_array($nameData)) {
                $matchedStocks = [];
                foreach ($nameData as $stock) {
                    // 检查是否包含关键词（支持代码或名称匹配）
                    if ((isset($stock['dm']) && stripos($stock['dm'], $keyword) !== false) || 
                        (isset($stock['mc']) && stripos($stock['mc'], $keyword) !== false) ||
                        (isset($stock['code']) && stripos($stock['code'], $keyword) !== false) ||
                        (isset($stock['name']) && stripos($stock['name'], $keyword) !== false)) {
                        
                        // 确保返回格式统一
                        $matchedStocks[] = [
                            'stock_code' => $stock['dm'] ?? $stock['code'] ?? '',
                            'stock_name' => $stock['mc'] ?? $stock['name'] ?? '',
                            // 同时保留dm/mc格式用于兼容
                            'dm' => $stock['dm'] ?? $stock['code'] ?? '',
                            'mc' => $stock['mc'] ?? $stock['name'] ?? '',
                            // 同时保留code/name格式用于兼容
                            'code' => $stock['dm'] ?? $stock['code'] ?? '',
                            'name' => $stock['mc'] ?? $stock['name'] ?? ''
                        ];
                        
                        // 最多返回10条结果
                        if (count($matchedStocks) >= 10) {
                            break;
                        }
                    }
                }
                
                if (!empty($matchedStocks)) {
                    $results = $matchedStocks;
                }
            }
        }
    } else {
        // 转换数据库结果格式以匹配缓存文件中的格式
        $formattedResults = [];
        foreach ($results as $stock) {
            $formattedResults[] = [
                'stock_code' => $stock['stock_code'],
                'stock_name' => $stock['stock_name'],
                // 同时保留dm/mc格式用于兼容
                'dm' => $stock['stock_code'],
                'mc' => $stock['stock_name'],
                // 同时保留code/name格式用于兼容
                'code' => $stock['stock_code'],
                'name' => $stock['stock_name']
            ];
        }
        $results = $formattedResults;
    }
    
    // 响应同时包含data和stocks两种格式，确保兼容性
    echo json_encode([
        'success' => true, 
        'data' => $results,
        'stocks' => $results,
        'message' => count($results) > 0 ? '成功获取股票数据' : '未找到匹配的股票'
    ]);
    
} catch (Exception $e) {
    error_log("搜索股票失败: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => '搜索股票失败：' . $e->getMessage()]);
}

exit;
?>