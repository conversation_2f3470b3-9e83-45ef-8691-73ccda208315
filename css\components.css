/* 存储模式卡片 */
.storage-mode-section {
  padding: 1.25rem;
  background-color: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  margin-bottom: 1rem;
}

.toggle-switch {
  display: flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  margin-bottom: 1rem;
}

.toggle-switch input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 1.5rem;
  background-color: var(--neutral-300);
  border-radius: 1.5rem;
  transition: all var(--transition);
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 1.125rem;
  width: 1.125rem;
  left: 0.1875rem;
  bottom: 0.1875rem;
  background-color: white;
  border-radius: 50%;
  transition: all var(--transition);
  box-shadow: var(--shadow-sm);
}

input:checked + .toggle-slider {
  background-color: var(--success-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(1.5rem);
}

.toggle-label {
  color: var(--text-primary);
  font-size: 0.875rem;
  font-weight: 500;
}

.storage-info {
  color: var(--text-muted);
  font-size: 0.8125rem;
  margin-top: 0.5rem;
  line-height: 1.4;
  padding-left: 3.75rem;
}

/* 云同步按钮 */
.cloud-sync-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.625rem;
  margin-top: 1rem;
}

.cloud-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.625rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition);
  width: 100%;
}

.cloud-button:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.cloud-button svg {
  margin-right: 0.5rem;
  flex-shrink: 0;
  width: 1rem;
  height: 1rem;
}

.cloud-button.download {
  background-color: var(--info-color);
}

.cloud-button.download:hover {
  background-color: var(--primary-dark);
}

/* API 密钥区域 */
.api-key-section {
  padding: 1.25rem;
}

.api-key-display {
  background-color: var(--neutral-100);
  border: 1px solid var(--neutral-200);
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
}

.key-label {
  font-weight: 500;
  color: var(--text-secondary);
  margin-right: 0.5rem;
}

.key-value {
  font-family: monospace;
  background-color: var(--neutral-200);
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  color: var(--primary-color);
  letter-spacing: 0.05em;
}

.key-info {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

.remaining-requests {
  display: inline-flex;
  align-items: center;
  background-color: rgba(37, 99, 235, 0.1);
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  color: var(--primary-color);
  font-weight: 500;
  font-size: 1rem;
  margin-top: 0.5rem;
}

.remaining-requests i {
  margin-right: 0.5rem;
}

.remaining-requests span {
  font-weight: 700;
  color: var(--primary-dark);
  margin-left: 0.25rem;
}

/* 公告区域 */
.announcement {
  padding: 1rem;
  border-bottom: 1px solid var(--neutral-200);
}

.announcement:last-child {
  border-bottom: none;
}

.announcement-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.announcement-date {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-bottom: 0.5rem;
}

.announcement-content {
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 二维码按钮区域 */
.qrcode-buttons {
  display: flex;
  justify-content: space-around;
  padding: 1.25rem;
  gap: 1.25rem;
}

.qrcode-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 1rem;
  border-radius: var(--border-radius-md);
  transition: all var(--transition);
  background-color: white;
  box-shadow: var(--shadow-sm);
  flex: 1;
  max-width: 120px;
}

.qrcode-button:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.qrcode-button img {
  width: 2rem;
  height: 2rem;
  margin-bottom: 0.5rem;
}

.qrcode-button p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

/* 二维码弹窗 */
.qrcode-modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(4px);
}

.qrcode-content {
  background-color: white;
  padding: 1.5rem;
  border-radius: var(--border-radius-lg);
  text-align: center;
  position: relative;
  box-shadow: var(--shadow-lg);
  max-width: 90%;
  width: 300px;
}

.qrcode-content img {
  max-width: 200px;
  height: auto;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

.qrcode-content .close-btn {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  cursor: pointer;
  font-size: 1.25rem;
  color: var(--text-muted);
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all var(--transition);
}

.qrcode-content .close-btn:hover {
  background-color: var(--neutral-200);
  color: var(--text-primary);
}

.qrcode-content p {
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.recharge-notice {
  color: var(--danger-color);
  font-size: 0.875rem;
  background-color: rgba(239, 68, 68, 0.1);
  padding: 0.5rem;
  border-radius: var(--border-radius);
  margin-top: 0.75rem;
}

/* 我的股票区域 */
#stockList {
  padding: 0 1rem 1rem;
}

.stock-card {
  background-color: white;
  border-radius: var(--border-radius-md);
  margin-bottom: 1rem;
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition);
}

.stock-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stock-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid var(--neutral-200);
}

.stock-title {
  display: flex;
  flex-direction: column;
}

.stock-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.stock-code {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.stock-price {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.current-price {
  font-size: 1.25rem;
  font-weight: 700;
}

.price-up .current-price {
  color: var(--up-color);
}

.price-down .current-price {
  color: var(--down-color);
}

.price-change {
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.price-up .price-change {
  color: var(--up-color);
}

.price-down .price-change {
  color: var(--down-color);
}

.price-change-percentage {
  background-color: rgba(239, 68, 68, 0.1);
  padding: 0.125rem 0.375rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
}

.price-down .price-change-percentage {
  background-color: rgba(16, 185, 129, 0.1);
}

.stock-body {
  padding: 1rem;
}

.stock-data-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.data-item {
  padding: 0.5rem;
  background-color: var(--neutral-100);
  border-radius: var(--border-radius-sm);
}

.data-label {
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-bottom: 0.25rem;
}

.data-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
}

.stock-actions {
  display: flex;
  gap: 0.625rem;
  justify-content: space-between;
  width: 100%;
  margin-top: 0.75rem;
}

/* 股票卡片按钮 */
.stock-btn {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition);
  text-align: center;
  flex: 1;
  width: 33.33%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stock-btn.analyze {
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.stock-btn.analyze:hover {
  background-color: var(--primary-dark);
}

.stock-btn.refresh {
  background-color: var(--neutral-100);
  color: var(--text-primary);
  border: 1px solid var(--neutral-300);
}

.stock-btn.refresh:hover {
  background-color: var(--neutral-200);
}

.stock-btn.remove {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
  border: 1px solid rgba(220, 53, 69, 0.2);
}

.stock-btn.remove:hover {
  background-color: rgba(220, 53, 69, 0.15);
}

/* 其他按钮样式 */
.add-button, 
.search-button {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition);
  text-align: center;
  background-color: var(--primary-color);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 70px;
}

.add-button:hover, 
.search-button:hover {
  background-color: var(--primary-dark);
}

.stock-btn i,
.add-button i, 
.search-button i {
  margin-right: 0.25rem;
}

/* Agent 分析区域 */
.chat-section {
  padding: 1.5rem;
}

.chat-section .section-title {
  justify-content: flex-start;
}

.chat-section .section-title span i {
  margin-right: 0.5rem;
}

.chat-container {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.empty-message {
  text-align: center;
  padding: 1rem;
  color: var(--text-muted);
  font-size: 0.875rem;
  background-color: var(--neutral-100);
  border-radius: var(--border-radius);
  margin-bottom: 0.5rem;
}

.chat-message {
  margin-bottom: 1rem;
  background-color: var(--neutral-100);
  padding: 1rem;
  border-radius: var(--border-radius);
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.message-sender {
  font-weight: 600;
  color: var(--text-primary);
}

.message-time {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.message-content {
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

.message-agent {
  background-color: rgba(37, 99, 235, 0.05);
  border-left: 3px solid var(--primary-color);
}

.message-user {
  background-color: var(--neutral-100);
  border-left: 3px solid var(--neutral-400);
}

.message-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.message-loading::after {
  content: '';
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid var(--neutral-300);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s infinite linear;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 搜索结果样式 */
.search-results {
  margin-top: 1rem;
}

.search-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--neutral-200);
  transition: background-color var(--transition);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: var(--neutral-100);
}

.stock-info {
  display: flex;
  flex-direction: column;
}

.stock-info-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.stock-info-code {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .stock-data-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .qrcode-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .qrcode-button {
    width: 100%;
    max-width: none;
  }
  
  /* 统一移动端按钮大小 */
  .stock-btn, 
  .add-button, 
  .cloud-button, 
  .message-btn,
  .search-button {
    min-height: 40px !important;
    height: 40px !important;
    padding: 0 5px !important;
  }
}

@media (max-width: 480px) {
  .storage-info {
    padding-left: 0;
  }
  
  .stock-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stock-price {
    margin-top: 0.5rem;
  }
  
  .stock-data-grid {
    grid-template-columns: 1fr;
  }
  
  /* 移动端按钮横向显示，三个按钮同等宽度 */
  .stock-actions {
    flex-direction: row !important;
    width: 100%;
    gap: 0.5rem;
  }
  
  .stock-btn {
    flex: 1;
    min-width: 0;
    width: 33.33% !important;
    min-height: 40px !important;
    height: 40px !important;
    padding: 0 !important;
    font-size: 13px !important;
  }
  
  /* 图标字体减小，保证在按钮中正确显示 */
  .stock-btn i {
    font-size: 12px;
    margin-right: 2px;
  }
}

/* 市场指数卡片样式 - 苹果风格 */
.market-indices {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  gap: 12px;
  padding: 1rem;
}

.index-card {
  flex: 1;
  min-width: 0;
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.index-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.index-name {
  font-size: 16px;
  font-weight: 600;
  color: #333333;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.index-name::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

/* 上证指数特殊标记 */
.index-card:nth-child(1) .index-name::before {
  background-color: #007AFF;
}

/* 深证成指特殊标记 */
.index-card:nth-child(2) .index-name::before {
  background-color: #5AC8FA;
}

/* 创业板指特殊标记 */
.index-card:nth-child(3) .index-name::before {
  background-color: #AF52DE;
}

.index-price {
  font-size: 26px;
  font-weight: 700;
  margin: 5px 0;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  letter-spacing: -0.5px;
}

.index-change {
  display: flex;
  gap: 8px;
  margin-top: 5px;
  align-items: center;
}

.index-change-item {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: -0.2px;
  display: inline-flex;
  align-items: center;
}

.price-up {
  color: #FF3B30;
  background: rgba(255, 59, 48, 0.1);
}

.price-down {
  color: #30D158;
  background: rgba(48, 209, 88, 0.1);
}

.index-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-top: 12px;
  font-size: 12px;
  color: #8E8E93;
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  padding-top: 12px;
}

.index-detail-item {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  justify-content: space-between;
}

.index-detail-item span:first-child {
  color: #8E8E93;
}

.index-detail-item span:last-child {
  font-weight: 500;
  color: #333333;
}

.loading-indices {
  width: 100%;
  text-align: center;
  padding: 20px;
  color: #8E8E93;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-indices::before {
  content: '';
  width: 18px;
  height: 18px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top-color: #007AFF;
  border-radius: 50%;
  margin-right: 10px;
  animation: spin 1s infinite linear;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .market-indices {
    flex-direction: column;
    padding: 12px;
  }
  
  .index-card {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .index-price {
    font-size: 22px;
  }
}

/* 高评分股票显示区域样式 */
.high-score-stocks-container {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
}

.high-score-item {
    background: #ffffff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: calc(33.33% - 10px);
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 4px solid #4a90e2;
}

.high-score-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

.stock-info {
    display: flex;
    flex-direction: column;
}

.stock-name-code {
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
}

.score-time {
    font-size: 12px;
    color: #888;
}

.submitted-by {
    font-size: 11px;
    color: #666;
    margin-top: 3px;
    font-style: italic;
    background-color: rgba(0, 0, 0, 0.03);
    padding: 2px 5px;
    border-radius: 3px;
    display: inline-block;
}

.stock-score {
    font-size: 22px;
    font-weight: bold;
    padding: 8px 12px;
    border-radius: 8px;
    background: rgba(74, 144, 226, 0.1);
    color: #4a90e2;
}

.score-high {
    background: rgba(76, 175, 80, 0.1);
    color: #4caf50;
}

.score-good {
    background: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.score-moderate {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.score-low {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.highlight-stock {
    animation: highlight-pulse 2s;
}

@keyframes highlight-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(74, 144, 226, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(74, 144, 226, 0);
    }
}

/* 移动端适配 */
@media (max-width: 992px) {
    .high-score-item {
        width: calc(50% - 10px);
    }
}

@media (max-width: 576px) {
    .high-score-item {
        width: 100%;
    }
}

.extract-score-btn {
    background: transparent;
    border: 1px solid #4a90e2;
    color: #4a90e2;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    margin-left: auto;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.extract-score-btn:hover {
    background: #4a90e2;
    color: white;
}

.extract-score-btn i {
    margin-right: 4px;
}

@media (max-width: 768px) {
    .extract-score-btn {
        font-size: 11px;
        padding: 2px 6px;
    }
} 