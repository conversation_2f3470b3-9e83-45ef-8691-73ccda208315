/**
 * 纯前端股票管理模块
 * 使用localStorage存储用户数据到本地
 */

// 轻量级反调试处理，确保功能正常
if (typeof window.__antiDebugLoaded === 'undefined') {
    // 仅在生产环境下使用轻量级日志屏蔽
    if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
        console.log = console.info = console.warn = console.error = console.debug = function() {
            // 空函数，但不阻止后续代码执行
        };
    }
}

// 确保console方法已被覆盖
if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
    console.log = console.info = console.warn = console.error = console.debug = function() {
        // 静默失败或替换为空操作
    };
}

const FrontendStocks = {
    // 存储键名
    STORAGE_KEY: 'user_stocks',
    STORAGE_MODE_KEY: 'use_frontend_storage',
    AUTO_REFRESH_KEY: 'auto_refresh_enabled',
    AUTO_REFRESH_INTERVAL: 28000, // 自动刷新间隔，默认18秒（与原来保持一致）
    
    // 添加缓存相关的属性
    CACHE_KEY_PREFIX: 'stock_detail_cache_',
    CACHE_EXPIRY: 5 * 60 * 1000, // 缓存过期时间，5分钟
    CACHE_ENABLED: true, // 是否启用缓存
    
    // 股票名称映射缓存键
    STOCK_NAME_MAPPING_KEY: 'stock_name_mapping',
    
    // 添加高评分股票存储键
    HIGH_SCORE_STOCKS_KEY: 'high_score_stocks',
    
    // 添加预加载队列
    preloadQueue: [],
    preloadTimer: null,
    
    // 添加价格有效性检查方法
    isValidPrice: function(price) {
        // 保持与现有逻辑一致,只检查undefined、null和空字符串
        if (price === undefined || price === null || price === '') {
            return false;
        }
        return true;
    },
    
    // 初始化
    init: function() {
        console.log('前端股票管理模块初始化');
        // 初始化样式
        this.initStyles();
        // 初始化自动刷新功能
        this.initAutoRefresh();
        // 初始化预加载功能
        this.initPreload();
        // 初始化高评分股票区域
        this.initHighScoreStocks();
    },
    
    // 初始化预加载功能
    initPreload: function() {
        // 获取所有股票代码
        const stocks = this.getAllStocks();
        if (stocks && stocks.length > 0) {
            // 将股票代码添加到预加载队列
            this.preloadQueue = stocks.map(stock => stock.code);
            // 开始预加载
            this.startPreload();
        }
    },
    
    // 开始预加载
    startPreload: function() {
        if (this.preloadTimer) {
            clearInterval(this.preloadTimer);
        }
        
        // 每3秒预加载一个股票的数据
        this.preloadTimer = setInterval(() => {
            if (this.preloadQueue.length > 0) {
                const stockCode = this.preloadQueue.shift();
                this.preloadStockData(stockCode);
            } else {
                clearInterval(this.preloadTimer);
            }
        }, 3000);
    },
    
    // 预加载股票数据
    preloadStockData: function(stockCode) {
        if (!this.CACHE_ENABLED) return;
        
        // 检查缓存是否已过期
        const cacheKey = this.CACHE_KEY_PREFIX + stockCode;
        const cachedData = this.getCache(cacheKey);
        if (cachedData) return;
        
        // 获取实时数据并缓存
        this.fetchStockRealTimeData(stockCode)
            .then(data => {
                this.setCache(cacheKey, data);
            })
            .catch(error => {
                console.error(`预加载股票 ${stockCode} 数据失败:`, error);
            });
    },
    
    // 获取缓存数据
    getCache: function(key) {
        try {
            const cached = localStorage.getItem(key);
            if (!cached) return null;
            
            const { data, timestamp } = JSON.parse(cached);
            if (Date.now() - timestamp > this.CACHE_EXPIRY) {
                localStorage.removeItem(key);
                return null;
            }
            return data;
        } catch (e) {
            console.error('获取缓存失败:', e);
            return null;
        }
    },
    
    // 设置缓存数据
    setCache: function(key, data) {
        try {
            const cacheData = {
                data: data,
                timestamp: Date.now()
            };
            localStorage.setItem(key, JSON.stringify(cacheData));
        } catch (e) {
            console.error('设置缓存失败:', e);
        }
    },
    
    // 清除缓存数据
    clearCache: function(stockCode) {
        try {
            localStorage.removeItem(this.CACHE_KEY_PREFIX + stockCode);
        } catch (e) {
            console.error('清除缓存失败:', e);
        }
    },
    
    // 初始化样式
    initStyles: function() {
        const style = document.createElement('style');
        style.textContent = `
            .ma-container {
                display: flex;
                justify-content: space-between;
                margin: 10px 0;
                padding: 8px;
                background: #f8f9fa;
                border-radius: 6px;
            }
            .ma-item {
                text-align: center;
                padding: 4px 8px;
                border-radius: 4px;
                color: #fff;
                font-size: 12px;
            }
            .ma5 { background: #2196F3; }
            .ma10 { background: #4CAF50; }
            .ma20 { background: #FF9800; }
                font-size: 12px;
            }
        `;
        document.head.appendChild(style);
    },

    // 初始化自动刷新功能
    initAutoRefresh: function() {
        // 从localStorage读取自动刷新设置
        const autoRefreshEnabled = localStorage.getItem(this.AUTO_REFRESH_KEY) === 'true';
        
        // 如果页面上有自动刷新开关，更新其状态
        const autoRefreshToggle = document.getElementById('autoRefreshToggle');
        if (autoRefreshToggle) {
            autoRefreshToggle.checked = autoRefreshEnabled;
            
            // 绑定开关事件
            autoRefreshToggle.addEventListener('change', () => {
                if (autoRefreshToggle.checked) {
                    this.startAutoRefresh();
                } else {
                    this.stopAutoRefresh();
                }
            });
        }
        
        // 如果启用了自动刷新，启动定时器
        if (autoRefreshEnabled) {
            this.startAutoRefresh();
        }
    },
    
    // 开始自动刷新
    startAutoRefresh: function() {
        // 先停止可能存在的定时器，避免重复
        this.stopAutoRefresh();
        
        // 设置自动刷新状态为启用
        localStorage.setItem(this.AUTO_REFRESH_KEY, 'true');
        
        // 创建新的定时器，定期触发所有股票的刷新按钮
        this.autoRefreshTimer = setInterval(() => {
            // 获取所有刷新按钮并点击
            const refreshButtons = document.querySelectorAll('.btn-refresh');
            refreshButtons.forEach((btn, index) => {
                // 设置延迟，避免同时发送太多请求
                setTimeout(() => {
                    if (btn && !btn.disabled) {
                        btn.click();
                    }
                }, index * 300);
            });
        }, this.AUTO_REFRESH_INTERVAL);
        
        // 通知用户
        if(typeof showNotification === 'function') {
            showNotification('自动刷新已开启', 'info');
        }
        
        return true;
    },
    
    // 停止自动刷新
    stopAutoRefresh: function() {
        // 清除现有定时器
        if(this.autoRefreshTimer) {
            clearInterval(this.autoRefreshTimer);
            this.autoRefreshTimer = null;
        }
        
        // 设置自动刷新状态为禁用
        localStorage.setItem(this.AUTO_REFRESH_KEY, 'false');
        
        // 通知用户
        if(typeof showNotification === 'function') {
            showNotification('自动刷新已关闭', 'info');
        }
        
        return true;
    },
    
    // 刷新所有股票
    refreshAllStocks: function() {
        const stocks = this.getAllStocks();
        
        if (!stocks || stocks.length === 0) {
            return;
        }
        
        let successCount = 0;
        let failCount = 0;
        
        // 为每只股票设置随机延迟，避免同时发送太多请求
        stocks.forEach((stock, index) => {
            // 使用更稳健的错误处理
            try {
                setTimeout(() => {
                    this.refreshStock(stock.code)
                        .then(() => {
                            successCount++;
                        })
                        .catch(error => {
                            failCount++;
                        })
                        .finally(() => {
                            // 当所有股票处理完成后显示通知
                            if (successCount + failCount === stocks.length && typeof showNotification === 'function') {
                                if (failCount > 0) {
                                    showNotification(`已刷新 ${successCount} 只股票数据，${failCount} 只失败`, 'warning');
                                } else {
                                    showNotification(`已刷新 ${successCount} 只股票数据`, 'success');
                                }
                            }
                        });
                }, index * 300); // 每只股票间隔300毫秒
            } catch (e) {
                // 捕获任何可能的异常，确保不中断循环
                failCount++;
            }
        });
        
        return true;
    },
    
    // 获取所有股票
    getAllStocks: function() {
        const stocksJson = localStorage.getItem(this.STORAGE_KEY);
        if (!stocksJson) {
            return [];
        }
        
        try {
            return JSON.parse(stocksJson);
        } catch (e) {
            console.error('解析股票数据失败:', e);
            return [];
        }
    },
    
    /**
     * 添加一支股票到前端存储
     */
    addStock: function(stockCode, stockName) {
        try {
            if (!stockCode) {
                return {
                    success: false,
                    message: '股票代码不能为空'
                };
            }
            
            // 如果没有提供股票名称，尝试获取
            if (!stockName || stockName === '未知股票') {
                stockName = '加载中...'; // 临时名称
            } else {
                // 如果提供了有效的股票名称，保存到映射中
                this.saveStockNameMapping(stockCode, stockName);
            }
            
            // 检查是否已存在
            const stocks = this.getAllStocks();
            const existingStock = stocks.find(s => s.code === stockCode);
            if (existingStock) {
                return {
                    success: false,
                    message: '股票已存在'
                };
            }
            
            // 添加新股票
            const stock = {
                code: stockCode,
                name: stockName,
                price: '0.00',
                change: '0.00',
                added_at: new Date().toISOString()
            };
            
            stocks.push(stock);
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(stocks));
            
            console.log(`股票添加成功: ${stockCode} ${stockName}`);
            
            // 立即获取最新数据并更新显示
            this.fetchStockRealTimeData(stockCode)
                .then(realTimeData => {
                    // 更新股票数据
                    this.updateStock({
                        ...stock,
                        ...realTimeData
                    });
                    
                    // 如果获取到了有效名称，更新映射
                    if (realTimeData && realTimeData.name && realTimeData.name !== '未知股票') {
                        this.saveStockNameMapping(stockCode, realTimeData.name);
                    }
                    
                    // 重新加载股票列表
                    if (typeof window.loadUserStocks === 'function') {
                        window.loadUserStocks();
                    } else {
                        // 如果loadUserStocks不可用，直接刷新页面
                        window.location.reload();
                    }
                })
                .catch(error => {
                    console.error('获取股票实时数据失败:', error);
                    // 即使获取实时数据失败，也要刷新显示
                    if (typeof window.loadUserStocks === 'function') {
                        window.loadUserStocks();
                    }
                });
            
            return {
                success: true,
                message: '股票添加成功'
            };
        } catch (error) {
            console.error('添加股票时发生错误:', error);
            return {
                success: false,
                message: '添加失败：' + error.message
            };
        }
    },
    
    /**
     * 更新股票信息
     */
    updateStock: function(stock) {
        if (!stock || !stock.code) {
            console.error('更新股票时缺少必要的股票代码');
            return false;
        }
        
        try {
            // 获取所有股票
            const stocks = this.getAllStocks();
            
            // 查找该股票
            const index = stocks.findIndex(item => item.code === stock.code);
            if (index === -1) {
                console.error('未找到要更新的股票');
                return false;
            }
            
            // 确保不会将有效的股票名称更新为"未知股票"
            if (stock.name === '未知股票' && stocks[index].name && stocks[index].name !== '未知股票') {
                console.log(`保留原有股票名称: ${stocks[index].name}，而不是更新为"未知股票"`);
                stock.name = stocks[index].name;
            }
            
            // 保护性检查:如果新价格无效,保留原有价格
            if (!this.isValidPrice(stock.price) && this.isValidPrice(stocks[index].price)) {
                console.log(`股票 ${stock.code} 的新价格无效,保留原有价格: ${stocks[index].price}`);
                stock.price = stocks[index].price;
            }
            
            // 更新该股票
            stocks[index] = Object.assign({}, stocks[index], stock);
            
            // 保存所有股票
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(stocks));
            
            return true;
        } catch (e) {
            console.error('更新股票数据失败:', e);
            return false;
        }
    },
    
    // 更新UI中的股票卡片
    updateStockUI: function(stockCode) {
        // 查找对应代码的股票卡片
        const stockCard = document.querySelector(`.stock-card[data-code="${stockCode}"]`);
        if (!stockCard) {
            console.warn(`未找到股票卡片: ${stockCode}`);
            return false;
        }
        
        // 获取最新的股票数据
        const stocks = this.getAllStocks();
        const stock = stocks.find(s => s.code === stockCode);
        
        if (!stock) {
            console.warn(`未找到股票数据: ${stockCode}`);
            return false;
        }
        
        try {
            console.log(`更新股票卡片UI: ${stockCode} ${stock.name}，价格=${stock.price}，涨跌幅=${stock.change || stock.price_change}`);
            
            // 标准化数据结构
            const standardizedStock = {
                code: stock.code,
                name: stock.name || '未知股票',
                price: stock.price || '0.00',
                change: stock.change || stock.price_change || '0.00',
                open: stock.open || '0.00',
                close: stock.close || '0.00',
                high: stock.high || '0.00',
                low: stock.low || '0.00',
                ma5: stock.ma5 || '-',
                ma10: stock.ma10 || '-',
                ma20: stock.ma20 || '-',
                volumeRatio: stock.volumeRatio || '0.00',
                kdj: stock.kdj || null,
                macd: stock.macd || null
            };
            
            // 尝试直接更新卡片内容，而不是完全替换卡片
            try {
                // 更新价格和涨跌幅
                const priceEl = stockCard.querySelector('.stock-price');
                const changeEl = stockCard.querySelector('.stock-change');
                
                if (priceEl) {
                    priceEl.textContent = standardizedStock.price;
                    console.log(`更新价格: ${standardizedStock.price}`);
                }
                
                if (changeEl) {
                    const changeValue = parseFloat(standardizedStock.change);
                    const changeClass = changeValue >= 0 ? 'price-up' : 'price-down';
                    const changeSymbol = changeValue >= 0 ? '+' : '';
                    
                    changeEl.className = `stock-change ${changeClass}`;
                    changeEl.textContent = `${changeSymbol}${standardizedStock.change}`;
                    console.log(`更新涨跌幅: ${changeSymbol}${standardizedStock.change}`);
                }
                
                // 更新其他详情
                const highEl = stockCard.querySelector('.detail-row:nth-child(1) .detail-value');
                const lowEl = stockCard.querySelector('.detail-row:nth-child(2) .detail-value');
                const ma5El = stockCard.querySelector('.detail-row:nth-child(3) .detail-value');
                const ma10El = stockCard.querySelector('.detail-row:nth-child(4) .detail-value');
                const ma20El = stockCard.querySelector('.detail-row:nth-child(5) .detail-value');
                
                if (highEl) highEl.textContent = standardizedStock.high || '-';
                if (lowEl) lowEl.textContent = standardizedStock.low || '-';
                if (ma5El) ma5El.textContent = standardizedStock.ma5 || '-';
                if (ma10El) ma10El.textContent = standardizedStock.ma10 || '-'; 
                if (ma20El) ma20El.textContent = standardizedStock.ma20 || '-';
                
                // 更新KDJ和MACD状态
                try {
                    this.updateIndicators(stockCard, standardizedStock);
                } catch (indicatorError) {
                    console.error(`更新指标显示失败: ${stockCode}`, indicatorError);
                }
                
                console.log(`股票卡片DOM元素直接更新成功: ${stockCode}`);
                return true;
            } catch (directUpdateError) {
                console.warn(`直接更新卡片内容失败，尝试替换整个卡片: ${stockCode}`, directUpdateError);
                
                // 如果直接更新失败，尝试创建新卡片并替换
                const newCard = this.createStockCard(standardizedStock);
                stockCard.replaceWith(newCard);
                console.log(`股票卡片已完全替换: ${stockCode}`);
                return true;
            }
        } catch (e) {
            console.error(`更新股票卡片失败: ${stockCode}`, e);
            return false;
        }
    },
    
    // 更新指标显示
    updateIndicators: function(stockCard, stock) {
        if (!stockCard || !stock) return;
        
        // 更新量比显示
        const volumeStatusEl = stockCard.querySelector('.volume-status');
        if (volumeStatusEl && stock.volumeRatio) {
            const volumeRatio = parseFloat(stock.volumeRatio);
            const changeValue = parseFloat(stock.change || stock.price_change || '0');
            
            let volumeStatus = '';
            if (volumeRatio > 1.5 && changeValue >= 0) {
                volumeStatus = '放量上涨';
            } else if (volumeRatio > 1.5 && changeValue < 0) {
                volumeStatus = '放量下跌';
            } else if (volumeRatio < 1 && changeValue >= 0) {
                volumeStatus = '缩量上涨';
            } else if (volumeRatio < 1 && changeValue < 0) {
                volumeStatus = '缩量下跌';
            } else if (volumeRatio >= 1 && volumeRatio <= 1.5) {
                volumeStatus = '放量温和';
            }
            
            if (volumeStatus) {
                volumeStatusEl.textContent = `${volumeStatus} (量比:${stock.volumeRatio})`;
            }
        }
        
        // 更新KDJ状态
        const kdjStatusEl = stockCard.querySelector('.kdj-status');
        if (kdjStatusEl && stock.kdj) {
            // 获取KDJ信号
            const kdjSignal = window.Indicators && typeof window.Indicators.getKDJSignal === 'function'
                ? window.Indicators.getKDJSignal(stock.kdj)
                : { text: 'KDJ正常', style: '' };
                
            if (kdjSignal.text) {
                kdjStatusEl.textContent = `${kdjSignal.text} (K:${stock.kdj.k.toFixed(2)} D:${stock.kdj.d.toFixed(2)} J:${stock.kdj.j.toFixed(2)})`;
            }
        }
        
        // 更新MACD状态
        const macdStatusEl = stockCard.querySelector('.macd-status');
        if (macdStatusEl && stock.macd) {
            // 获取MACD信号
            const macdSignal = window.Indicators && typeof window.Indicators.getMACDSignal === 'function'
                ? window.Indicators.getMACDSignal(stock.macd)
                : { text: 'MACD正常', style: '' };
                
            if (macdSignal.text) {
                macdStatusEl.textContent = `${macdSignal.text} (DIF:${stock.macd.dif.toFixed(4)} DEA:${stock.macd.dea.toFixed(4)})`;
            }
        }
    },
    
    // 删除股票
    removeStock: function(stockCode) {
        if (!stockCode) {
            throw new Error('股票代码不能为空');
        }
        
        // 获取现有股票
        const stocks = this.getAllStocks();
        
        // 过滤掉要删除的股票
        const newStocks = stocks.filter(stock => stock.code !== stockCode);
        
        // 如果长度一样，说明没有找到要删除的股票
        if (newStocks.length === stocks.length) {
            console.log(`股票 ${stockCode} 不在关注列表中`);
            return {
                success: false,
                message: '股票不在您的关注列表中'
            };
        }
        
        // 更新存储
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(newStocks));
        
        console.log(`股票 ${stockCode} 已删除`);
        return {
            success: true,
            message: '股票已从关注列表中删除'
        };
    },
    
    // 搜索股票 (模拟后端搜索接口)
    searchStock: async function(keyword) {
        if (!keyword) {
            return {
                success: false,
                message: '请输入搜索关键词'
            };
        }
        
        // 在这里我们可以调用真实的股票搜索API
        // 为了简单起见，这里使用原有的搜索API
        try {
            const response = await fetch(`/api/search_stock.php?keyword=${encodeURIComponent(keyword)}`);
            return await response.json();
        } catch (e) {
            console.error('搜索股票失败:', e);
            return {
                success: false,
                message: '搜索失败，请稍后再试'
            };
        }
    },
    
    // 使用前端模块替换后端API
    replaceBackendApi: function() {
        // 保存原始方法的引用
        if (window.addStock && typeof window.addStock === 'function') {
            window._originalAddStock = window.addStock;
        }
        
        // 替换添加股票方法
        window.addStock = (stockCode, stockName) => {
            try {
                // 显示加载状态
                const addBtn = document.querySelector('.add-btn[data-code="' + stockCode + '"]');
                if (addBtn) {
                    addBtn.disabled = true;
                    addBtn.innerText = '添加中...';
                }
                
                // 调用前端方法
                const result = this.addStock(stockCode, stockName);
                
                // 恢复按钮状态
                if (addBtn) {
                    addBtn.disabled = false;
                    addBtn.innerText = result.already_exists ? '已添加' : '添加';
                }
                
                // 显示结果
                if (typeof showMessage === 'function') {
                    showMessage(result.message);
                } else {
                    alert(result.message);
                }
                
                // 如果添加成功且页面上有刷新股票列表的函数，则调用它
                if (result.success && !result.already_exists && typeof loadUserStocks === 'function') {
                    loadUserStocks();
                }
                
                return result;
            } catch (e) {
                console.error('添加股票失败:', e);
                alert('添加失败: ' + e.message);
                
                // 恢复按钮状态
                const addBtn = document.querySelector('.add-btn[data-code="' + stockCode + '"]');
                if (addBtn) {
                    addBtn.disabled = false;
                    addBtn.innerText = '添加';
                }
                
                return {
                    success: false,
                    message: e.message
                };
            }
        };
        
        // 替换获取用户股票列表的方法
        if (typeof loadUserStocks === 'function') {
            window._originalLoadUserStocks = loadUserStocks;
            
            window.loadUserStocks = () => {
                console.log('前端模式：加载用户股票列表');
                
                try {
                    const stocks = this.getAllStocks();
                    console.log('从localStorage加载的股票数据:', stocks);
                    
                    // 获取股票列表容器
                    const stockList = document.getElementById('stockList');
                    if (!stockList) {
                        console.error('找不到股票列表容器');
                        return;
                    }
                    
                    // 清空列表
                    stockList.innerHTML = '';
                    
                    // 如果没有股票，显示提示
                    if (!stocks || stocks.length === 0) {
                        stockList.innerHTML = '<div class="empty-message text-center py-3">您还没有添加任何股票</div>';
                        return;
                    }
                    
                    // 创建股票网格
                    const stockGrid = document.createElement('div');
                    stockGrid.className = 'stock-grid';
                    
                    // 添加股票到列表
                    stocks.forEach(stock => {
                        console.log('处理股票数据:', stock);
                        
                        // 标准化数据结构
                        const standardizedStock = {
                            code: stock.code,
                            name: stock.name,
                            price: stock.price || '0.00',
                            change: stock.price_change || '0.00',
                            open: stock.open || '0.00',
                            close: stock.close || '0.00',
                            high: stock.high || '0.00',
                            low: stock.low || '0.00'
                        };
                        
                        // 创建股票卡片
                        const stockCard = FrontendStocks.createStockCard(standardizedStock);
                        stockGrid.appendChild(stockCard);
                    });
                    
                    stockList.appendChild(stockGrid);
                    
                } catch (e) {
                    console.error('加载股票列表失败:', e);
                    
                    // 获取股票列表容器
                    const stockList = document.getElementById('stockList');
                    if (stockList) {
                        stockList.innerHTML = `<div class="error-message">加载股票列表失败: ${e.message}</div>`;
                    }
                }
            };
        }
        
        console.log('前端股票管理模块已替换后端API');
    },
    
    // 恢复使用后端API
    restoreBackendApi: function() {
        if (window._originalAddStock) {
            window.addStock = window._originalAddStock;
        }
        
        if (window._originalLoadUserStocks) {
            window.loadUserStocks = window._originalLoadUserStocks;
        }
        
        console.log('已恢复使用后端API');
    },
    
    /**
     * 创建股票卡片
     */
    createStockCard: function(stock) {
        const self = this;
        const card = document.createElement('div');
        card.className = 'stock-card';
        card.setAttribute('data-code', stock.code);
        
        // 添加分析按钮和事件处理
        const analysisButton = document.createElement('button');
        analysisButton.className = 'analysis-button';
        analysisButton.textContent = 'AI分析';
        analysisButton.onclick = async function(e) {
            e.stopPropagation(); // 阻止事件冒泡
            
            try {
                // 获取最新的股票数据
                const stockData = await self.fetchStockRealTimeData(stock.code);
                if (!stockData) {
                    throw new Error('无法获取股票数据');
                }
                
                // 准备分析内容
                const content = `
                    请分析这支股票的表现:
                    股票代码: ${stock.code}
                    股票名称: ${stock.name}
                    当前价格: ${stockData.price || '-'}
                    涨跌幅: ${stockData.price_change || '-'}%
                    今开: ${stockData.open || '-'}
                    昨收: ${stockData.prev_close || '-'}
                    最高: ${stockData.high || '-'}
                    最低: ${stockData.low || '-'}
                    成交量: ${stockData.volume || '-'}
                    成交额: ${stockData.amount || '-'}
                    换手率: ${stockData.turnover || '-'}%
                `;
                
                // 调用AI分析
                await callAnalysisAPI(content);
                
            } catch (error) {
                console.error('分析过程出错:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'analysis-error';
                errorDiv.textContent = `分析失败: ${error.message}`;
                
                let analysisContainer = document.querySelector('.stock-analysis');
                if (!analysisContainer) {
                    analysisContainer = document.createElement('div');
                    analysisContainer.className = 'stock-analysis';
                    document.querySelector('.stock-detail-modal').appendChild(analysisContainer);
                }
                
                analysisContainer.innerHTML = '';
                analysisContainer.appendChild(errorDiv);
            }
        };
        
        // 将分析按钮添加到卡片
        card.appendChild(analysisButton);
        
        // 判断股价变化是正还是负
        const changeValue = parseFloat(stock.change || stock.price_change || '0');
        const changeClass = changeValue >= 0 ? 'price-up' : 'price-down';
        const changeSymbol = changeValue >= 0 ? '+' : '';
        
        // 确保显示价格是有效的
        const displayPrice = stock.price || '0.00';
        // 确保显示涨跌幅是有效的
        const displayChange = (changeSymbol + (stock.change || stock.price_change || '0.00'));
        
        // 处理量比情况
        let volumeStatus = '';
        let volumeStyle = '';
        if (stock.volumeRatio) {
            const volumeRatio = parseFloat(stock.volumeRatio);
            if (volumeRatio > 1.5 && changeValue >= 0) {
                volumeStatus = '放量上涨';
            } else if (volumeRatio > 1.5 && changeValue < 0) {
                volumeStatus = '放量下跌';
            } else if (volumeRatio < 1 && changeValue >= 0) {
                volumeStatus = '缩量上涨';
            } else if (volumeRatio < 1 && changeValue < 0) {
                volumeStatus = '缩量下跌';
            } else if (volumeRatio >= 1 && volumeRatio <= 1.5) {
                volumeStatus = '放量温和';
            }
            
            if (volumeStatus) {
                volumeStyle = 'color: red; font-weight: bold; font-size: 12px; margin-top: 5px;';
            }
        }
        
        // 处理KDJ指标
        let kdjStatus = '';
        let kdjStyle = '';
        
        console.log('开始处理KDJ指标，完整的stock对象:', stock);
        
        if (stock.kdj) {
        //    console.log(`处理股票${stock.code}的KDJ数据:`, stock.kdj);
         //   console.log('KDJ数据类型:', typeof stock.kdj);
          //  console.log('KDJ数据是否包含signal:', stock.kdj.signal);
          //  console.log('KDJ数据是否包含k:', stock.kdj.k);
          //  console.log('KDJ数据是否包含d:', stock.kdj.d);
            
            // 确保KDJ数据格式正确
            if (typeof stock.kdj === 'string') {
                try {
                    stock.kdj = JSON.parse(stock.kdj);
           //         console.log('已将KDJ数据从字符串转换为对象:', stock.kdj);
                } catch (e) {
           //         console.error('KDJ数据解析失败:', e);
                }
            }
            
            // 获取KDJ信号
        //    console.log('Window.Indicators存在:', !!window.Indicators);
       //     console.log('GetKDJSignal函数存在:', window.Indicators && typeof window.Indicators.getKDJSignal === 'function');
            
            const kdjSignal = window.Indicators && typeof window.Indicators.getKDJSignal === 'function'
                ? window.Indicators.getKDJSignal(stock.kdj)
                : { text: '', style: '' };
            
            console.log(`获取到KDJ信号:`, kdjSignal);
            
            if (kdjSignal.text) {
                kdjStatus = kdjSignal.text;
                kdjStyle = kdjSignal.style || 'color: #0066cc; font-weight: bold; font-size: 12px; margin-top: 5px;';
        //        console.log(`设置KDJ状态为: ${kdjStatus}`);
            } else {
        //        console.log('KDJ信号文本为空，不显示');
            }
        } else {
     //       console.log(`股票${stock.code}没有KDJ数据`);
        }
        
        // 处理MACD指标
        let macdStatus = '';
        let macdStyle = '';
        
        console.log('开始处理MACD指标，完整的stock对象:', stock);
        
        if (stock.macd) {
       //     console.log(`处理股票${stock.code}的MACD数据:`, stock.macd);
       //     console.log('MACD数据类型:', typeof stock.macd);
       //     console.log('MACD数据是否包含signal:', stock.macd.signal);
       //     console.log('MACD数据是否包含dif:', stock.macd.dif);
       //     console.log('MACD数据是否包含dea:', stock.macd.dea);
            
            // 确保MACD数据格式正确
            if (typeof stock.macd === 'string') {
                try {
                    stock.macd = JSON.parse(stock.macd);
        //            console.log('已将MACD数据从字符串转换为对象:', stock.macd);
                } catch (e) {
         //           console.error('MACD数据解析失败:', e);
                }
            }
            
            // 获取MACD信号
       //     console.log('Window.Indicators存在:', !!window.Indicators);
            console.log('GetMACDSignal函数存在:', window.Indicators && typeof window.Indicators.getMACDSignal === 'function');
            
            const macdSignal = window.Indicators && typeof window.Indicators.getMACDSignal === 'function'
                ? window.Indicators.getMACDSignal(stock.macd)
                : { text: '', style: '' };
            
            console.log(`获取到MACD信号:`, macdSignal);
            
            if (macdSignal.text) {
                macdStatus = macdSignal.text;
                macdStyle = macdSignal.style || 'color: purple; font-weight: bold; font-size: 12px; margin-top: 5px;';
          //      console.log(`设置MACD状态为: ${macdStatus}`);
            } else {
       //         console.log('MACD信号文本为空，不显示');
            }
        } else {
     //       console.log(`股票${stock.code}没有MACD数据`);
        }
        
        card.innerHTML = `
            <div class="stock-header">
                <div class="stock-title clickable" data-code="${stock.code}">
                    <span class="stock-name">${stock.name}</span>
                    <span class="stock-code">${stock.code}</span>
                </div>
            </div>
            <div class="stock-price-container">
                <span class="stock-price">${displayPrice}</span>
                <span class="stock-change ${changeClass}">${displayChange}</span>
            </div>
            <div class="stock-indicators">
                ${volumeStatus ? `<div class="volume-status" style="${volumeStyle}">${volumeStatus} (量比:${stock.volumeRatio})</div>` : ''}
                ${stock.kdj ? `<div class="kdj-status" style="${kdjStyle || 'color: #0066cc; font-weight: bold; font-size: 12px; margin-top: 5px;'}">${kdjStatus || 'KDJ正常'} (K:${stock.kdj.k.toFixed(2)} D:${stock.kdj.d.toFixed(2)} J:${stock.kdj.j.toFixed(2)})</div>` : '<div class="kdj-status" style="color: #999; font-size: 12px; margin-top: 5px;">KDJ数据加载中...</div>'}
                ${stock.macd ? `<div class="macd-status" style="${macdStyle || 'color: purple; font-weight: bold; font-size: 12px; margin-top: 5px;'}">${macdStatus || 'MACD正常'} (DIF:${stock.macd.dif.toFixed(4)} DEA:${stock.macd.dea.toFixed(4)})</div>` : '<div class="macd-status" style="color: #999; font-size: 12px; margin-top: 5px;">MACD数据加载中...</div>'}
            </div>
            <div class="stock-details">
                <div class="detail-row">
                    <span class="detail-label">最高</span>
                    <span class="detail-value">${stock.high || '-'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">最低</span>
                    <span class="detail-value">${stock.low || '-'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">MA5</span>
                    <span class="detail-value">${stock.ma5 || '-'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">MA10</span>
                    <span class="detail-value">${stock.ma10 || '-'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">MA20</span>
                    <span class="detail-value">${stock.ma20 || '-'}</span>
                </div>
            </div>
            <div class="stock-actions">
                <button class="btn-analyze" data-code="${stock.code}" title="AI分析该股票">分析</button>
                <button class="btn-refresh" data-code="${stock.code}" title="刷新数据">刷新</button>
                <button class="btn-delete" data-code="${stock.code}" title="从关注列表中移除">删除</button>
            </div>
        `;
        
        // 添加CSS样式
        card.style.border = '1px solid #e0e0e0';
        card.style.borderRadius = '8px';
        card.style.padding = '15px';
        card.style.margin = '10px';
        card.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';
        card.style.backgroundColor = '#fff';
        
        // 美化头部样式
        const stockHeader = card.querySelector('.stock-header');
        if(stockHeader) {
            stockHeader.style.display = 'flex';
            stockHeader.style.justifyContent = 'space-between';
            stockHeader.style.marginBottom = '10px';
            stockHeader.style.borderBottom = '1px solid #eee';
            stockHeader.style.paddingBottom = '5px';
        }
        
        // 添加可点击标题的样式
        const stockTitle = card.querySelector('.stock-title');
        if(stockTitle) {
            stockTitle.style.cursor = 'pointer';
            stockTitle.style.display = 'flex';
            stockTitle.style.gap = '10px';
            stockTitle.style.alignItems = 'center';
            
            // 修改事件绑定方式，使用箭头函数保持this上下文
            stockTitle.addEventListener('click', (e) => {
                e.stopPropagation(); // 阻止事件冒泡
                FrontendStocks.showStockDetails(stock.code);
                // 点击标题时也触发刷新
                const refreshBtn = card.querySelector('.btn-refresh');
                if (refreshBtn) {
                    refreshBtn.click();
                }
            });

            // 为整个卡片添加点击事件，触发刷新按钮
            card.addEventListener('click', () => {
                const refreshBtn = card.querySelector('.btn-refresh');
                if (refreshBtn) {
                    refreshBtn.click();
                }
            });
        }
        
        // 美化价格部分
        const stockPriceContainer = card.querySelector('.stock-price-container');
        if(stockPriceContainer) {
            stockPriceContainer.style.marginBottom = '15px';
            stockPriceContainer.style.display = 'flex';
            stockPriceContainer.style.alignItems = 'baseline';
            stockPriceContainer.style.gap = '10px';
        }
        
        const stockPrice = card.querySelector('.stock-price');
        if(stockPrice) {
            stockPrice.style.fontSize = '24px';
            stockPrice.style.fontWeight = 'bold';
        }
        
        // 美化按钮
        const buttons = card.querySelectorAll('button');
        buttons.forEach(btn => {
            btn.style.padding = '6px 0';
            btn.style.border = 'none';
            btn.style.borderRadius = '6px';
            btn.style.cursor = 'pointer';
            btn.style.fontSize = '12px';
            btn.style.fontWeight = '500';
            btn.style.flex = '1';
            btn.style.minWidth = '0';
            btn.style.textAlign = 'center';
            btn.style.display = 'flex';
            btn.style.alignItems = 'center';
            btn.style.justifyContent = 'center';
            btn.style.width = '33%'; // 设置所有按钮宽度一致
        });
        
        // 设置按钮容器为flex布局
        const stockActions = card.querySelector('.stock-actions');
        if(stockActions) {
            stockActions.style.display = 'flex';
            stockActions.style.justifyContent = 'space-between';
            stockActions.style.gap = '6px';
            stockActions.style.marginTop = '8px';
        }
        
        // 获取所有按钮并应用样式
        const analyzeBtn = card.querySelector('.btn-analyze');
        const refreshBtn = card.querySelector('.btn-refresh');
        const deleteBtn = card.querySelector('.btn-delete');
        
        if(analyzeBtn) {
            analyzeBtn.style.backgroundColor = '#4CAF50';
            analyzeBtn.style.color = 'white';
            
            // 绑定分析按钮事件
            analyzeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const stockCode = this.getAttribute('data-code');
                console.log(`分析股票: ${stockCode}`);
                
                // 清空聊天区域
               // const chatContainer = document.getElementById('chatContainer'); 注释掉
                if (chatContainer) {
                    // 创建新的分析请求UI
                    const analysisRequest = document.createElement('div');
                    //analysisRequest.className = 'chat-message user-message';
                 //   analysisRequest.innerHTML = `<div class="message-content">分析${stock.name} (${stockCode})</div>`;
                    chatContainer.appendChild(analysisRequest);
                    
                    // 创建AI响应占位符
                    const analysisResponse = document.createElement('div');
                    analysisResponse.className = 'chat-message ai-message';
                    analysisResponse.innerHTML = `<div class="loading-dots">量化中<span>.</span><span>.</span><span>.</span></div>`;
                    chatContainer.appendChild(analysisResponse);
                    
                    // 滚动到底部
                    //chatContainer.scrollTop = chatContainer.scrollHeight; 注释无效代码
                     chatContainer.scrollIntoView({ behavior: 'smooth' });
                    
                    // 检查API密钥是否存在
                    const apiKey = getApiKey();
                    if (!apiKey) {
                        analysisResponse.innerHTML = `<div class="message-content">无法获取分析结果：未设置API密钥。请在仪表板的API密钥设置中保存有效的密钥。</div>`;
                        chatContainer.scrollTop = chatContainer.scrollHeight;
                        
                        // 显示通知
                        if (typeof showNotification === 'function') {
                            showNotification('请在设置中配置API密钥', 'error');
                        }
                        return;
                    }
                    
            //        console.log('使用API密钥发送分析请求:', apiKey.substring(0, 3) + '...');
                    
                    // 分析API调用函数 - 在事件处理程序内部定义
                    function callAnalysisAPI(content) {
              //          console.log('发送到AI服务的消息:', content);
                        
                        // 使用后端代理发送请求
                        fetch('/api/chat_proxy.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                api_key: apiKey,
                                content: content
                            })
                        })
                        .then(response => {
                            console.log('AI分析API响应状态:', response.status);
                            // 检查响应状态
                            if (!response.ok) {
                                if (response.status === 401) {
                                    throw new Error('API密钥无效或未授权');
                                } else if (response.status === 403) {
                                    throw new Error('API访问被拒绝，可能是密钥权限不足');
                                } else if (response.status === 429) {
                                    throw new Error('请求次数超过限制，请稍后再试');
                                } else {
                                    throw new Error(`API请求失败，状态码：${response.status}`);
                                }
                            }
                            return response.json();
                        })
                        .then(data => {
                            console.log('AI分析响应:', data);
                            
                            // 更新AI响应 - 支持Markdown渲染
                            if (data.choices && data.choices[0] && data.choices[0].message) {
                                // 使用marked.js渲染Markdown (需要先加载marked库)
                                if (window.marked) {
                                    // 创建内容容器
                                    analysisResponse.innerHTML = `<div class="message-content markdown-content">${marked.parse(data.choices[0].message.content)}</div>
                                    <div class="analysis-complete"><i class="fas fa-check-circle"></i> 量化完成</div>`;
                                } else {
                                    // 如果marked库不可用，回退到普通文本显示，但保留换行
                                    const formattedContent = data.choices[0].message.content.replace(/\n/g, '<br>');
                                    analysisResponse.innerHTML = `<div class="message-content">${formattedContent}</div>
                                    <div class="analysis-complete"><i class="fas fa-check-circle"></i> 量化完成</div>`;
                                }
                            } else {
                                analysisResponse.innerHTML = `<div class="message-content">无法获取分析结果，API没有返回预期的数据格式。请确认API密钥权限和模型可用性。</div>
                                <div class="analysis-complete"><i class="fas fa-exclamation-circle"></i> 量化未完成</div>`;
                            }
                            
                            // 滚动到底部
                            chatContainer.scrollTop = chatContainer.scrollHeight;

                            // 保存分析记录到历史
                            if (window.ChatHistory && typeof ChatHistory.saveMessage === 'function') {
                                const userQuery = `分析 ${stock.name} (${stockCode})`; // 定义用户请求内容
                                const aiContent = data.choices[0].message.content;
                                ChatHistory.saveMessage(stockCode, stock.name, userQuery, aiContent);
                                console.log('分析记录已保存');
                            }
                            
                            // 显示分析完成通知
                            if (typeof window.showAnalysisNotification === 'function') {
                                window.showAnalysisNotification(stock.name, stockCode);
                            }
                        })
                        .catch(error => {
                            console.error('AI分析错误:', error);
                            analysisResponse.innerHTML = `<div class="message-content">分析请求失败: ${error.message}</div>
                            <div class="analysis-complete"><i class="fas fa-exclamation-circle"></i> 量化未完成</div>`;
                            
                            // 如果是API密钥问题，显示特别提示
                            if (error.message.includes('密钥') || error.message.includes('401') || error.message.includes('403')) {
                                analysisResponse.innerHTML = `<div class="message-content">分析请求失败: ${error.message}<br><br>请在仪表板的API密钥设置中检查您的密钥是否正确。</div>
                                <div class="analysis-complete"><i class="fas fa-exclamation-circle"></i> 量化未完成</div>`;
                            }
                            
                            chatContainer.scrollTop = chatContainer.scrollHeight;
                        });
                    }
                    
                    // 先获取股票实时数据
                    FrontendStocks.fetchStockRealTimeData(stockCode)
                        .then(stockData => {
                            // 格式化股票数据为中文
                            let stockInfo = "";
                            if (stockData && Object.keys(stockData).length > 0) {
                                stockInfo = formatStockDataToChinese(stockData);
                                console.log('获取到股票实时数据:', stockInfo);
                            }
                            
                            // 构建请求内容，包含实时数据
                            const baseMessage = `请分析${stock.name} (${stockCode})的走势`;
                            const fullMessage = stockInfo ? `${baseMessage}\n\n实时数据：${stockInfo}` : baseMessage;
                            
              //              console.log('发送分析消息包含实时数据:', fullMessage);
                            
                            // 先获取历史数据，再调用分析API
                            fetch(`api/stock_proxy.php?symbol=${stockCode}&type=hist`)
                                .then(res => res.json())
                                .then(histData => {
                                    let messageWithHistory = fullMessage;
                                    
                                    // 添加历史数据
                                    if (histData && histData.data && Array.isArray(histData.data)) {
                                        messageWithHistory += `\n\n最近7日历史交易数据: ${JSON.stringify(histData.data)}`;
                                    }
                                    
                                    // 调用AI分析API，包含历史数据
                                    callAnalysisAPI(messageWithHistory);
                                })
                                .catch(error => {
                                    console.error('获取历史数据失败:', error);
                                    // 即使没有历史数据，也继续发送原始请求
                                    callAnalysisAPI(fullMessage);
                                });
                        })
                        .catch(error => {
              //              console.error('获取股票实时数据失败:', error);
                            // 失败时发送基本请求，不包含实时数据
                            callAnalysisAPI(`请分析${stock.name} (${stockCode})的走势`);
                        });
                } else {
                    console.error('找不到聊天容器');
                    showNotification('无法显示分析结果，聊天区域不可用', 'error');
                }
            });
        }
        
        // 添加聊天按钮
        const consultBtn = document.createElement('button');
        consultBtn.className = 'btn-consult';
        consultBtn.setAttribute('data-code', stock.code);
        consultBtn.textContent = '咨询';
        consultBtn.style.backgroundColor = '#9c27b0';
        consultBtn.style.color = 'white';
        consultBtn.style.padding = '6px 12px';
        consultBtn.style.border = 'none';
        consultBtn.style.borderRadius = '4px';
        consultBtn.style.cursor = 'pointer';
        consultBtn.style.margin = '0 5px';
        consultBtn.style.fontSize = '14px';
        
        // 在分析按钮后插入咨询按钮
        if (analyzeBtn && analyzeBtn.parentNode) {
            analyzeBtn.parentNode.insertBefore(consultBtn, analyzeBtn.nextSibling);
        }
        
        // 绑定咨询按钮事件
        consultBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const stockCode = this.getAttribute('data-code');
            
            // 显示输入框
            showChatInputDialog(stockCode, stock.name);
        });
        
        if(refreshBtn) {
            refreshBtn.style.backgroundColor = '#2196F3';
            refreshBtn.style.color = 'white';
            
            // 绑定刷新数据按钮事件
            refreshBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const stockCode = this.getAttribute('data-code');
                console.log(`刷新股票数据: ${stockCode}`);
                
                // 显示加载状态
                this.disabled = true;
                this.textContent = '更新中...';
                
                // 获取最新数据
                FrontendStocks.fetchStockRealTimeData(stockCode)
                    .then(realTimeData => {
                        console.log('获取到最新数据:', realTimeData);
                        
                        // 更新本地存储
                        const stocks = FrontendStocks.getAllStocks();
                        const updatedStocks = stocks.map(s => {
                            if (s.code === stockCode) {
                                // 确保MA数据正确更新
                                const ma5 = realTimeData.ma5 !== undefined && realTimeData.ma5 !== null && realTimeData.ma5 !== '-' ? realTimeData.ma5 : s.ma5 || '-';
                                const ma10 = realTimeData.ma10 !== undefined && realTimeData.ma10 !== null && realTimeData.ma10 !== '-' ? realTimeData.ma10 : s.ma10 || '-';
                                const ma20 = realTimeData.ma20 !== undefined && realTimeData.ma20 !== null && realTimeData.ma20 !== '-' ? realTimeData.ma20 : s.ma20 || '-';
                                
                      //          console.log(`更新MA数据: MA5=${ma5}, MA10=${ma10}, MA20=${ma20}`);
                      //          console.log(`更新KDJ数据:`, realTimeData.kdj);
                                
                                return {
                                    ...s,
                                    price: realTimeData.price || s.price,
                                    price_change: realTimeData.change || s.price_change,
                                    high: realTimeData.high || s.high,
                                    low: realTimeData.low || s.low,
                                    name: realTimeData.name || s.name,
                                    // 确保MA数据正确更新
                                    ma5: realTimeData.ma5 !== undefined && realTimeData.ma5 !== null ? realTimeData.ma5 : s.ma5 || '-',
                                    ma10: realTimeData.ma10 !== undefined && realTimeData.ma10 !== null ? realTimeData.ma10 : s.ma10 || '-',
                                    ma20: realTimeData.ma20 !== undefined && realTimeData.ma20 !== null ? realTimeData.ma20 : s.ma20 || '-',
                                    // 添加量比数据
                                    volumeRatio: realTimeData.volumeRatio !== undefined && realTimeData.volumeRatio !== null ? realTimeData.volumeRatio : s.volumeRatio,
                                    // 添加KDJ数据
                                    kdj: realTimeData.kdj || s.kdj,
                                    // 添加MACD数据
                                    macd: realTimeData.macd || s.macd
                                };
                            }
                            return s;
                        });
                        
                        // 保存更新后的数据
                        localStorage.setItem(FrontendStocks.STORAGE_KEY, JSON.stringify(updatedStocks));
                        
                        // 重新加载股票列表
                        window.loadUserStocks();
                        
                        // 显示通知
                        showNotification(`${stock.name} (${stockCode}) 数据已更新`, 'success');
                    })
                    .catch(error => {
                        console.error('获取最新数据失败:', error);
                        
                        // 恢复按钮状态
                        this.disabled = false;
                        this.textContent = '刷新';
                        
                        // 显示错误通知
                        showNotification(`数据更新失败: ${error.message}`, 'error');
                    });
            });
        }
        
        if(deleteBtn) {
            deleteBtn.style.backgroundColor = '#f44336';
            deleteBtn.style.color = 'white';
            
            // 绑定删除按钮事件
            deleteBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                const stockCode = this.getAttribute('data-code');
                if (confirm(`确定要删除 ${stock.name} (${stockCode}) 吗?`)) {
                    // 使用FrontendStocks的removeStock方法
                    FrontendStocks.removeStock(stockCode);
                    // 重新加载股票列表
                    window.loadUserStocks();
                    
                    console.log(`股票 ${stockCode} (${stock.name}) 已被删除`);
                }
            });
        }
        
        // 绑定更新按钮事件
        const updateBtn = card.querySelector('.btn-refresh');
        if (updateBtn) {
            updateBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                const code = this.getAttribute('data-code');
                if (code) {
                    // 只操作当前按钮
                    this.disabled = true;
                    this.textContent = '更新中';
                    
                    // 获取该股票的最新数据
                    FrontendStocks.fetchStockRealTimeData(code)
                        .then(realTimeData => {
                            console.log('获取到股票最新数据:', realTimeData);
                            
                            // 合并数据制作更新后的股票对象
                            const updatedStock = {
                                ...stock, // 基础数据保持不变
                                price: realTimeData.price || stock.price,
                                change: realTimeData.change || stock.change,
                                high: realTimeData.high || stock.high,
                                low: realTimeData.low || stock.low,
                                ma5: realTimeData.ma5 || stock.ma5,
                                ma10: realTimeData.ma10 || stock.ma10,
                                ma20: realTimeData.ma20 || stock.ma20,
                                volumeRatio: realTimeData.volumeRatio || stock.volumeRatio,
                                kdj: realTimeData.kdj || stock.kdj,
                                macd: realTimeData.macd || stock.macd
                            };
                            
                            console.log('合并后的股票数据:', updatedStock);
                            
                            // 使用updateStock保存更新后的数据
                            FrontendStocks.updateStock(updatedStock);
                            
                            // 创建一个新的卡片并替换旧卡片
                            const newCard = FrontendStocks.createStockCard(updatedStock);
                            
                            // 找到当前卡片并替换
                            const currentCard = document.querySelector(`.stock-card[data-code="${code}"]`);
                            if (currentCard) {
                                currentCard.replaceWith(newCard);
                            }
                            
                            // 显示通知
                            if (typeof showNotification === 'function') {
                                showNotification('股票数据已更新', 'success');
                            }
                            
                        })
                        .catch(error => {
                            console.error('获取股票数据失败:', error);
                            
                            // 恢复按钮状态
                            this.disabled = false;
                            this.textContent = '刷新';
                            
                            // 显示错误提示
                            if (typeof showNotification === 'function') {
                                showNotification('更新失败: ' + error.message, 'error');
                            }
                        });
                }
            });
        }
        
        return card;
    },
    
    // 添加fetchStockRealTimeData方法 - 获取股票实时数据
    fetchStockRealTimeData: function(stockCode) {
        console.log(`获取股票 ${stockCode} 的实时数据`);
        
        return new Promise((resolve, reject) => {
            // 并行获取实时数据和历史数据(包含MA)以及KDJ和MACD数据
            // 使用Promise.allSettled代替Promise.all以防止一个请求失败导致所有请求失败
            Promise.allSettled([
                fetch(`api/search_stock.php?keyword=${stockCode}`)
                    .then(res => {
                        if (!res.ok) throw new Error(`搜索API响应错误: ${res.status}`);
                        return res.text().then(text => {
                            try {
                                if (!text) throw new Error('响应为空');
                                return JSON.parse(text);
                            } catch (e) {
                                console.error('解析实时数据JSON失败:', e, text);
                                throw e;
                            }
                        });
                    }),
                fetch(`api/stock_proxy.php?symbol=${stockCode}&type=hist`)
                    .then(res => {
                        if (!res.ok) throw new Error(`历史数据API响应错误: ${res.status}`);
                        return res.text().then(text => {
                            try {
                                if (!text) throw new Error('响应为空');
                                return JSON.parse(text);
                            } catch (e) {
                                console.error('解析历史数据JSON失败:', e, text);
                                throw e;
                            }
                        });
                    }),
                fetch(`api/stock_proxy.php?symbol=${stockCode}`)
                    .then(res => {
                        if (!res.ok) throw new Error(`详细数据API响应错误: ${res.status}`);
                        return res.text().then(text => {
                            try {
                                if (!text) throw new Error('响应为空');
                                return JSON.parse(text);
                            } catch (e) {
                                console.error('解析详细数据JSON失败:', e, text);
                                throw e;
                            }
                        });
                    }),
                fetch(`api/get_kdj_data.php?symbol=${stockCode}`)
                    .then(res => {
                        if (!res.ok) throw new Error(`KDJ数据API响应错误: ${res.status}`);
                        return res.text().then(text => {
                            try {
                                if (!text) throw new Error('响应为空');
                                return JSON.parse(text);
                            } catch (e) {
                                console.error('解析KDJ数据JSON失败:', e, text);
                                throw e;
                            }
                        });
                    }),
                fetch(`api/get_macd_data.php?symbol=${stockCode}`)
                    .then(res => {
                        if (!res.ok) throw new Error(`MACD数据API响应错误: ${res.status}`);
                        return res.text().then(text => {
                            try {
                                if (!text) throw new Error('响应为空');
                                return JSON.parse(text);
                            } catch (e) {
                                console.error('解析MACD数据JSON失败:', e, text);
                                throw e;
                            }
                        });
                    })
            ])
            .then(results => {
                // 提取成功的结果
                const [realTimeResult, histResult, detailResult, kdjResult, macdResult] = results;
                
                // 确保至少有实时数据是成功的，否则无法继续
                if (realTimeResult.status !== 'fulfilled') {
                    console.error('获取实时数据失败:', realTimeResult.reason);
                    throw new Error('获取股票实时数据失败');
                }
                
                const realTimeData = realTimeResult.value || {};
                const histData = histResult.status === 'fulfilled' ? histResult.value : null;
                const detailData = detailResult.status === 'fulfilled' ? detailResult.value : null;
                const kdjData = kdjResult.status === 'fulfilled' ? kdjResult.value : null;
                const macdData = macdResult.status === 'fulfilled' ? macdResult.value : null;
                
                console.log('实时数据:', realTimeData);
                console.log('历史数据(MA):', histData);
                console.log('详细数据:', detailData);
                console.log('KDJ历史数据:', kdjData);
                console.log('MACD历史数据:', macdData);
                
                // 从搜索结果中获取股票数据
                let stockData = null;
                if (realTimeData && realTimeData.success) {
                    if (realTimeData.data && realTimeData.data.length > 0) {
                        stockData = realTimeData.data[0];
                    } else if (realTimeData.stocks && realTimeData.stocks.length > 0) {
                        stockData = realTimeData.stocks[0];
                    }
                }
                
                if (!stockData) {
                    stockData = {
                        code: stockCode,
                        name: '未知股票',
                        price: '0.00',
                        change: '0.00',
                        high: '0.00',
                        low: '0.00'
                    };
                }
                
                // 检查详细数据是否为item/value格式的数组
                const isArrayFormat = detailData && Array.isArray(detailData);
                if (isArrayFormat) {
            //        console.log('详细数据是item/value格式的数组');
                }
                
                // 检查histData是否有效
                const hasValidHistData = histData && typeof histData === 'object' && !Array.isArray(histData);
                console.log('历史数据是否有效:', hasValidHistData);
                
                // 获取量比数据
                let volumeRatio = null;
                
                // 1. 从实时数据中尝试获取量比数据
                if (realTimeData.data && realTimeData.data[0] && realTimeData.data[0].volumeRatio) {
                    volumeRatio = realTimeData.data[0].volumeRatio;
            //        console.log('从realTimeData.data中获取到量比:', volumeRatio);
                } else if (realTimeData.stocks && realTimeData.stocks[0] && realTimeData.stocks[0].volumeRatio) {
                    volumeRatio = realTimeData.stocks[0].volumeRatio;
           //         console.log('从realTimeData.stocks中获取到量比:', volumeRatio);
                } else if (realTimeData.volumeRatio) {
                    volumeRatio = realTimeData.volumeRatio;
         //           console.log('从realTimeData根级别获取到量比:', volumeRatio);
                }
                
                // 2. 从详细数据中尝试获取量比
                if (isArrayFormat) {
                    // 查找"量比"项
                    const volRatioItem = detailData.find(item => item.item === '量比');
                    if (volRatioItem && volRatioItem.value !== undefined) {
                        volumeRatio = volRatioItem.value;
          //              console.log('从item/value数组格式中提取到量比:', volumeRatio);
                    }
                }
                
                // 3. 提取API返回的详细数据转为扁平结构（用于价格和其他数据）
                let detailDataFlat = {};
                if (isArrayFormat) {
                    detailData.forEach(item => {
                        if (item.item && item.value !== undefined) {
                            detailDataFlat[item.item] = item.value;
                        }
                    });
            //        console.log('提取的扁平结构详细数据:', detailDataFlat);
                }
                
                // 4. 计算KDJ指标 - 使用专门的KDJ历史数据
                let kdjIndicator = null;
                if (kdjData && kdjData.success && kdjData.data && Array.isArray(kdjData.data) && kdjData.data.length >= 9) {
            //        console.log('使用KDJ历史数据计算KDJ值，数据长度:', kdjData.data.length);
                    try {
                        // 如果已加载indicators.js
                        if (window.Indicators && typeof window.Indicators.calculateKDJ === 'function') {
                            kdjIndicator = window.Indicators.calculateKDJ(kdjData.data);
                            console.log('计算得到的KDJ值:', kdjIndicator);
                        } else {
                            console.warn('Indicators.js未加载，无法计算KDJ值');
                            kdjIndicator = { k: 50, d: 50, j: 50, signal: 'normal' };
                        }
                    } catch (error) {
                        console.error('计算KDJ指标出错:', error);
                        kdjIndicator = { k: 50, d: 50, j: 50, signal: 'normal' };
                    }
                } else {
              //      console.warn('KDJ历史数据不足，无法计算KDJ指标，使用默认值');
                    kdjIndicator = { k: 50, d: 50, j: 50, signal: 'normal' };
                }
                
                // 5. 计算MACD指标 - 使用专门的MACD历史数据
                let macdIndicator = null;
                if (macdData && macdData.success && macdData.data && Array.isArray(macdData.data) && macdData.data.length >= 26) {
             //       console.log('使用MACD历史数据计算MACD值，数据长度:', macdData.data.length);
                    try {
                        // 如果已加载indicators.js
                        if (window.Indicators && typeof window.Indicators.calculateMACD === 'function') {
                            macdIndicator = window.Indicators.calculateMACD(macdData.data);
                            console.log('计算得到的MACD值:', macdIndicator);
                        } else {
                            console.warn('Indicators.js未加载，无法计算MACD值');
                            macdIndicator = { dif: 0, dea: 0, macd: 0, signal: 'normal' };
                        }
                    } catch (error) {
                        console.error('计算MACD指标出错:', error);
                        macdIndicator = { dif: 0, dea: 0, macd: 0, signal: 'normal' };
                    }
                } else {
            //        console.warn('MACD历史数据不足，无法计算MACD指标，使用默认值');
                    macdIndicator = { dif: 0, dea: 0, macd: 0, signal: 'normal' };
                }
                
                // 合并数据
                const mergedData = {
                    code: stockCode,
                    name: stockData.name === '未知股票' ? 
                          (histData?.name || detailDataFlat['名称'] || stockData.mc || stockData.stock_name || '未知股票') : 
                          stockData.name,
                    // 确保价格和涨跌幅有默认值，且使用当前存储的值作为备选
                    price: stockData.price || stockData.current || (detailDataFlat['最新'] ? detailDataFlat['最新'] : '0.00'),
                    change: stockData.change || stockData.price_change || (detailDataFlat['涨幅'] ? detailDataFlat['涨幅'] : '0.00'),
                    ...(stockData || {}),
                    // 直接从histData获取MA数据并确保正确合并
                    ma5: histData?.ma5 !== undefined && histData?.ma5 !== null ? histData.ma5 : '-',
                    ma10: histData?.ma10 !== undefined && histData?.ma10 !== null ? histData.ma10 : '-',
                    ma20: histData?.ma20 !== undefined && histData?.ma20 !== null ? histData.ma20 : '-',
                    // 添加量比数据
                    volumeRatio: volumeRatio,
                    // 添加KDJ数据
                    kdj: kdjIndicator,
                    // 添加MACD数据
                    macd: macdIndicator
                };
                
                // 从扁平结构补充其他数据
                if (Object.keys(detailDataFlat).length > 0) {
                    // 如果原始数据中没有这些字段，从扁平结构中复制
                    if (!mergedData.price && detailDataFlat['最新']) {
                        mergedData.price = detailDataFlat['最新'];
                    }
                    if (!mergedData.high && detailDataFlat['最高']) {
                        mergedData.high = detailDataFlat['最高'];
                    }
                    if (!mergedData.low && detailDataFlat['最低']) {
                        mergedData.low = detailDataFlat['最低'];
                    }
                    if (!mergedData.open && detailDataFlat['今开']) {
                        mergedData.open = detailDataFlat['今开'];
                    }
                    if (!mergedData.change && detailDataFlat['涨幅']) {
                        mergedData.change = detailDataFlat['涨幅'];
                    }
                    if (!mergedData.volumeRatio && detailDataFlat['量比']) {
                        mergedData.volumeRatio = detailDataFlat['量比'];
                    }
                }

                console.log('合并后的MA数据:', {
                    ma5: mergedData.ma5,
                    ma10: mergedData.ma10,
                    ma20: mergedData.ma20
                });
                
                console.log('合并后的量比数据:', mergedData.volumeRatio);
                console.log('合并后的KDJ数据:', mergedData.kdj);
                console.log('合并后的MACD数据:', mergedData.macd);
                console.log('合并后的最终数据:', mergedData);
                
                // 确保价格和涨跌幅数据有效，否则使用默认值或保持原值
                const existingStock = this.getAllStocks().find(s => s.code === stockCode);
                if (existingStock) {
                    // 如果新价格无效但原有价格有效,使用原有价格
                    if (!this.isValidPrice(mergedData.price) && this.isValidPrice(existingStock.price)) {
                        console.log(`股票 ${stockCode} 的新价格无效,使用原有价格: ${existingStock.price}`);
                        mergedData.price = existingStock.price;
                    }
                    // 如果新涨跌幅无效但原有涨跌幅有效,使用原有涨跌幅
                    if (!this.isValidPrice(mergedData.change) && this.isValidPrice(existingStock.price_change || existingStock.change)) {
                        console.log(`股票 ${stockCode} 的新涨跌幅无效,使用原有涨跌幅: ${existingStock.price_change || existingStock.change}`);
                        mergedData.change = existingStock.price_change || existingStock.change;
                    }
                }
                
                // 使用updateStock方法更新股票数据和UI
                try {
                    this.updateStock(mergedData);
                    console.log(`股票 ${stockCode} 数据和UI已更新`);
                } catch (updateError) {
                    console.error(`更新股票 ${stockCode} 数据和UI失败:`, updateError);
                }
                
                resolve(mergedData);
            })
            .catch(error => {
                console.error('获取数据失败:', error);
                // 返回一个最小化的股票对象，以防止UI崩溃
                const fallbackData = {
                    code: stockCode,
                    name: '未知股票',
                    price: '0.00',
                    change: '0.00',
                    high: '0.00',
                    low: '0.00',
                    ma5: '-',
                    ma10: '-',
                    ma20: '-',
                    volumeRatio: '-',
                    kdj: { k: 50, d: 50, j: 50, signal: 'normal' },
                    macd: { dif: 0, dea: 0, macd: 0, signal: 'normal' }
                };
                
                // 即使获取失败也尝试更新UI，以显示错误状态
                this.updateStock(fallbackData);
                
                resolve(fallbackData);
            });
        });
    },
    
    // 检查前端存储模式是否启用
    isEnabled: function() {
        // 检查localStorage支持
        if (!window.localStorage) {
            console.warn('浏览器不支持localStorage，前端模式无法使用');
            return false;
        }
        
        // 从localStorage读取设置
        return localStorage.getItem(this.STORAGE_MODE_KEY) === 'true';
    },
    
    // 启用前端存储模式
    enable: function() {
        if (!window.localStorage) {
            console.error('浏览器不支持localStorage，无法启用前端存储模式');
            return false;
        }
        
        localStorage.setItem(this.STORAGE_MODE_KEY, 'true');
        console.log('前端存储模式已启用');
        
        // 显示启用提示
        if (typeof showNotification === 'function') {
            showNotification('', 'info'); //'前端模式已启用'
        }
        
        // 如果页面上有存储模式开关，更新状态
        const modeSwitch = document.getElementById('storageMode');
        if (modeSwitch) {
            modeSwitch.checked = true;
            
            // 添加说明文本
            const storageModeSection = document.querySelector('.storage-mode-section');
            if (storageModeSection) {
                // 检查是否已存在说明
                if (!document.querySelector('.storage-desc')) {
                    const storageDesc = document.createElement('div');
                    storageDesc.className = 'storage-desc';
                    storageDesc.textContent = '数据将完全存储在您的本地设备中，确保隐私安全。但请注意，更换设备或清除浏览器数据后需要重新添加您的股票信息。';
                    storageModeSection.appendChild(storageDesc);
                }
            }
        }
        
        // 如果有UI开关，也更新它
        const frontendToggle = document.getElementById('frontendStorageToggle');
        if (frontendToggle) {
            frontendToggle.checked = true;
        }
        
        // 重新加载股票列表
        if (typeof window.loadUserStocks === 'function') {
            window.loadUserStocks();
        }
        
        return true;
    },
    
    // 禁用前端存储模式
    disable: function() {
        if (!window.localStorage) {
            console.error('浏览器不支持localStorage，无法禁用前端存储模式');
            return false;
        }
        
        localStorage.setItem(this.STORAGE_MODE_KEY, 'false');
        console.log('前端存储模式已禁用');
        
        // 如果有UI开关，也更新它
        const frontendToggle = document.getElementById('frontendStorageToggle');
        if (frontendToggle) {
            frontendToggle.checked = false;
        }
        
        // 重新加载股票列表
        if (typeof window.loadUserStocks === 'function') {
            window.loadUserStocks();
        }
        
        return true;
    },

    // 添加显示股票详情的方法
    showStockDetails: async function(stockCode) {
        try {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'stock-detail-modal';
            modal.style.position = 'fixed';
            modal.style.top = '0';
            modal.style.left = '0';
            modal.style.width = '100%';
            modal.style.height = '100%';
            modal.style.backgroundColor = 'rgba(0, 0, 0, 0.6)';
            modal.style.display = 'flex';
            modal.style.justifyContent = 'center';
            modal.style.alignItems = 'center';
            modal.style.zIndex = '1000';

            // 创建模态框内容
            const modalContent = document.createElement('div');
            modalContent.className = 'stock-detail-content';
            modalContent.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
            modalContent.style.padding = '20px';
            modalContent.style.borderRadius = '10px';
            modalContent.style.maxWidth = '800px';
            modalContent.style.maxHeight = '80vh';
            modalContent.style.overflow = 'auto';
            modalContent.style.position = 'relative';
            modalContent.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
            
            // 添加加载提示
            modalContent.innerHTML = '<div style="text-align: center;">正在加载该股票的详细数据...[数据可能存在延迟,注意更新时间.]</div>';
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // 保存this引用，防止在回调中丢失
            const self = this;
            
            // 检查缓存
            const cacheKey = self.CACHE_KEY_PREFIX + stockCode;
            let cachedData = self.getCache(cacheKey);
            
            // 并行请求数据
            const [licences, realTimeData, fiveLevelData, minData] = await Promise.all([
                fetch('licence.json').then(res => res.json()),
                cachedData ? Promise.resolve(cachedData) : self.fetchStockRealTimeData(stockCode),
                fetch(`api/stock_proxy.php?symbol=${stockCode}`).then(res => res.json()),
                fetch(`api/stock_proxy.php?symbol=${stockCode}&type=fsjy`).then(res => res.json())
            ]);
            
            // 更新缓存
            if (!cachedData) {
                self.setCache(cacheKey, realTimeData);
            }
            
            // 存储API数据
            let usedLicence = null;
            
            // 遍历所有证书，直到找到一个有效的或全部尝试失败
            for (let i = 0; i < licences.length; i++) {
                const currentLicence = licences[i];
                try {
                    // 检查数据有效性
                    if (realTimeData && typeof realTimeData === 'object' && !realTimeData.error && !realTimeData.msg) {
                        usedLicence = currentLicence;
                        break;
                    }
                } catch (error) {
                    continue;
                }
            }
            
            // 检查数据有效性
            const isValidRealTimeData = realTimeData && typeof realTimeData === 'object';
            
            // 处理五档盘口数据
            let fiveLevel = fiveLevelData;
            
            // 处理新的item/value格式的五档盘口数据
            if (fiveLevel && Array.isArray(fiveLevel) && fiveLevel.length > 0 && fiveLevel[0].item !== undefined) {
                // 转换item/value格式为扁平结构
                const flattenedData = {};
                fiveLevel.forEach(item => {
                    if (item.item && item.value !== undefined) {
                        flattenedData[item.item] = item.value;
                    }
                });
                
                // 映射买卖盘数据到现有格式
                if (flattenedData['sell_1'] !== undefined) {
                    fiveLevel = {
                        // 卖盘
                        ps1: flattenedData['sell_1'],
                        vs1: flattenedData['sell_1_vol'],
                        ps2: flattenedData['sell_2'],
                        vs2: flattenedData['sell_2_vol'],
                        ps3: flattenedData['sell_3'],
                        vs3: flattenedData['sell_3_vol'],
                        ps4: flattenedData['sell_4'],
                        vs4: flattenedData['sell_4_vol'],
                        ps5: flattenedData['sell_5'],
                        vs5: flattenedData['sell_5_vol'],
                        // 买盘
                        pb1: flattenedData['buy_1'],
                        vb1: flattenedData['buy_1_vol'],
                        pb2: flattenedData['buy_2'],
                        vb2: flattenedData['buy_2_vol'],
                        pb3: flattenedData['buy_3'],
                        vb3: flattenedData['buy_3_vol'],
                        pb4: flattenedData['buy_4'],
                        vb4: flattenedData['buy_4_vol'],
                        pb5: flattenedData['buy_5'],
                        vb5: flattenedData['buy_5_vol']
                    };
                }
            }
            
            // 修改五档盘口数据验证方式
            const isValidFiveLevel = fiveLevel && typeof fiveLevel === 'object' && 
                (fiveLevel.pb1 !== undefined || (fiveLevel.s && Array.isArray(fiveLevel.s)));
            const isValidMinData = minData && (Array.isArray(minData) || typeof minData === 'object');

            // 更新模态框内容
            const now = new Date();
            const updateTime = now.toLocaleString('zh-CN', { 
                hour12: false,
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            
            // 处理实时数据
            let flattenedRealTimeData = {};
            
            // 处理数据，确保它是正确的数据源
            if (Array.isArray(fiveLevelData)) {
                fiveLevelData.forEach(item => {
                    if (item.item && item.value !== undefined) {
                        flattenedRealTimeData[item.item] = item.value;
                    }
                });
            } else {
                flattenedRealTimeData = fiveLevelData;
            }

            // 获取价格和涨跌幅
            const price = flattenedRealTimeData['最新'] || '-';
            const change = flattenedRealTimeData['涨幅'] || '0.00';
            const stockName = realTimeData.name || await self.getStockName(stockCode);

            // 更新模态框内容
            modalContent.innerHTML = `
                <div class="stock-detail-header">
                    <h2>${stockName} (${stockCode})</h2>
                    <div class="update-time">更新时间: ${updateTime}</div>
                </div>
                <div class="stock-detail-grid">
                    <div class="price-section">
                        <h3>当前价格</h3>
                        <div class="current-price ${parseFloat(change) >= 0 ? 'price-up' : 'price-down'}">
                            ${price}
                            <span>${change}%</span>
                        </div>
                    </div>
                    <div class="price-section">
                        <h3>今日数据</h3>
                        <div class="data-grid">
                            <div class="data-item">
                                <span class="label">今开</span>
                                <span class="value">${flattenedRealTimeData['今开'] || '-'}</span>
                            </div>
                            <div class="data-item">
                                <span class="label">昨收</span>
                                <span class="value">${flattenedRealTimeData['昨收'] || '-'}</span>
                            </div>
                            <div class="data-item">
                                <span class="label">最高</span>
                                <span class="value">${flattenedRealTimeData['最高'] || '-'}</span>
                            </div>
                            <div class="data-item">
                                <span class="label">最低</span>
                                <span class="value">${flattenedRealTimeData['最低'] || '-'}</span>
                            </div>
                        </div>
                    </div>
                    <div class="price-section">
                        <h3>交易数据</h3>
                        <div class="data-grid">
                            <div class="data-item">
                                <span class="label">成交量</span>
                                <span class="value">${flattenedRealTimeData['总手'] ? (flattenedRealTimeData['总手']/100).toFixed(0) : '-'}</span>
                            </div>
                            <div class="data-item">
                                <span class="label">成交额</span>
                                <span class="value">${flattenedRealTimeData['金额'] ? (flattenedRealTimeData['金额']/10000).toFixed(2) + '万' : '-'}</span>
                            </div>
                            <div class="data-item">
                                <span class="label">量比</span>
                                <span class="value">${flattenedRealTimeData['量比'] || '-'}</span>
                            </div>
                            <div class="data-item">
                                <span class="label">换手率</span>
                                <span class="value">${flattenedRealTimeData['换手率'] || '-'}</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- K线图容器 -->
                <div class="kline-section">
                    <div class="kline-period-tabs">
                        <button class="period-tab active" data-type="timeline">分时图</button>
                        <button class="period-tab" data-type="kline" data-period="daily">日K</button>
                        <button class="period-tab" data-type="kline" data-period="weekly">周K</button>
                        <button class="period-tab" data-type="kline" data-period="monthly">月K</button>
                    </div>
                    <div id="klineChart" style="width: 100%; height: 400px;"></div>
                </div>
                
                <!-- 关闭按钮 -->
                <button class="modal-close-btn">&times;</button>
            `;
            
            // 添加关闭按钮事件
            const closeBtn = modalContent.querySelector('.modal-close-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    modal.remove();
                });
            }
            
            // 点击模态框背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.remove();
                }
            });
            
            // 阻止模态框内容点击事件冒泡
            modalContent.addEventListener('click', (e) => {
                e.stopPropagation();
            });
            
            // 确保ECharts库已加载
            if (typeof echarts === 'undefined') {
                // 动态加载ECharts
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js';
                script.onload = () => {
                    // ECharts加载完成后，显示分时图
                    const formattedCode = self.formatStockCode(stockCode);
                    KLineChart.showTimelineChart(formattedCode, stockName, 'klineChart');
                };
                document.head.appendChild(script);
            } else {
                // 直接显示分时图
                const formattedCode = self.formatStockCode(stockCode);
                KLineChart.showTimelineChart(formattedCode, stockName, 'klineChart');
            }
            
            // 注册K线周期切换事件
            const periodTabs = modalContent.querySelectorAll('.period-tab');
            periodTabs.forEach(tab => {
                tab.addEventListener('click', (e) => {
                    // 更新活动状态
                    periodTabs.forEach(t => t.classList.remove('active'));
                    e.target.classList.add('active');
                    
                    const chartType = e.target.dataset.type; // 图表类型：timeline或kline
                    const formattedCode = self.formatStockCode(stockCode);
                    
                    if (chartType === 'timeline') {
                        // 显示分时图
                        KLineChart.showTimelineChart(formattedCode, stockName, 'klineChart');
                    } else if (chartType === 'kline') {
                        // 获取选择的周期
                        const period = e.target.dataset.period;
                        
                        // 重新加载对应周期的K线图
                        KLineChart.showKLineChart(formattedCode, stockName, 'klineChart', period);
                    }
                    
                    // 防止事件冒泡
                    e.stopPropagation();
                });
            });
            
            const closeModal = () => modal.remove();
            
            // 添加ESC键关闭功能
            document.addEventListener('keydown', function escHandler(e) {
                if (e.key === 'Escape') {
                    closeModal();
                    document.removeEventListener('keydown', escHandler);
                }
            });
            
            return { modal, closeModal };
        } catch (error) {
            console.error('获取股票详情失败:', error);
            const modalContent = document.querySelector('.stock-detail-content');
            if (modalContent) {
                modalContent.innerHTML = `<div style="color: red; text-align: center;">
                    获取股票详情数据失败：${error.message || '未知错误'}
                </div>
                <button class="modal-close-btn">&times;</button>`;
                
                const closeBtn = modalContent.querySelector('.modal-close-btn');
                if (closeBtn) {
                    closeBtn.addEventListener('click', () => {
                        document.querySelector('.stock-detail-modal').remove();
                    });
                }
            }
        }
    },
    
    // 保存股票名称映射
    saveStockNameMapping: function(stockCode, stockName) {
        if (!stockCode || !stockName || stockName === '未知股票') return;
        
        try {
            // 获取现有映射
            const mappingJson = localStorage.getItem(this.STOCK_NAME_MAPPING_KEY);
            let mapping = {};
            if (mappingJson) {
                mapping = JSON.parse(mappingJson);
            }
            
            // 更新映射
            mapping[stockCode] = stockName;
            
            // 保存回存储
            localStorage.setItem(this.STOCK_NAME_MAPPING_KEY, JSON.stringify(mapping));
            console.log(`保存股票名称映射: ${stockCode} => ${stockName}`);
        } catch (e) {
            console.error('保存股票名称映射失败:', e);
        }
    },
    
    // 获取股票名称映射
    getStockNameMapping: function(stockCode) {
        try {
            const mappingJson = localStorage.getItem(this.STOCK_NAME_MAPPING_KEY);
            if (mappingJson) {
                const mapping = JSON.parse(mappingJson);
                return mapping[stockCode];
            }
        } catch (e) {
            console.error('获取股票名称映射失败:', e);
        }
        return null;
    },
    
    // 获取股票名称的辅助方法
    getStockName: async function(stockCode) {
        try {
            // 首先从名称映射缓存中获取
            const mappedName = this.getStockNameMapping(stockCode);
            if (mappedName) {
                console.log(`从名称映射获取股票名称: ${mappedName}`);
                return mappedName;
            }
            
            // 其次尝试从详情缓存中获取
            const cacheKey = this.CACHE_KEY_PREFIX + stockCode;
            const cachedData = this.getCache(cacheKey);
            if (cachedData && cachedData.name && cachedData.name !== '未知股票') {
                console.log(`从缓存获取股票名称: ${cachedData.name}`);
                // 更新名称映射
                this.saveStockNameMapping(stockCode, cachedData.name);
                return cachedData.name;
            }

            // 再尝试从本地存储的股票列表中获取
            const stocks = this.getAllStocks();
            const existingStock = stocks.find(s => s.code === stockCode);
            if (existingStock && existingStock.name && existingStock.name !== '未知股票') {
                console.log(`从本地存储获取股票名称: ${existingStock.name}`);
                // 更新名称映射
                this.saveStockNameMapping(stockCode, existingStock.name);
                return existingStock.name;
            }

            // 如果缓存中都没有，尝试从API获取
            console.log(`尝试从API获取股票 ${stockCode} 的名称`);
            
            // 使用更简单的请求，只获取名称
            const response = await fetch(`api/search_stock.php?keyword=${stockCode}`);
            const data = await response.json();
            
            if (data.success) {
                let stockData = null;
                if (data.data && data.data.length > 0) {
                    stockData = data.data[0];
                } else if (data.stocks && data.stocks.length > 0) {
                    stockData = data.stocks[0];
                }
                
                if (stockData) {
                    const name = stockData.name || stockData.mc || stockData.stock_name;
                    if (name && name !== '未知股票') {
                        console.log(`从API获取到股票名称: ${name}`);
                        // 更新名称映射
                        this.saveStockNameMapping(stockCode, name);
                        return name;
                    }
                }
            }
            
            return '未知股票';
        } catch (error) {
            console.error('获取股票名称失败:', error);
            return '未知股票';
        }
    },
    
    // 渲染单个股票的HTML
    renderStock: function(stockData, additionalData = {}) {
        // 先处理数据，如果没有某些关键数据则显示--
        if (!stockData || !stockData.price) {
            return `<div class="stock-card loading">正在加载股票数据...</div>`;
        }
        
        // 计算涨跌幅
        const changeClass = parseFloat(stockData.changePercent) >= 0 ? 'price-up' : 'price-down';
        const changeIcon = parseFloat(stockData.changePercent) >= 0 ? '<i class="fas fa-arrow-up"></i>' : '<i class="fas fa-arrow-down"></i>';
        
        // 确保所有数值都有值，避免显示undefined
        const price = stockData.price || '--';
        const change = stockData.change || '--';
        const changePercent = stockData.changePercent ? stockData.changePercent + '%' : '--';
        const high = stockData.high || '--';
        const low = stockData.low || '--';
        const open = stockData.open || '--';
        const prevClose = stockData.prevClose || '--';
        const volume = stockData.volume ? (parseInt(stockData.volume) / 10000).toFixed(2) + '万' : '--';
        const turnover = stockData.turnover ? (parseInt(stockData.turnover) / 10000).toFixed(2) + '万' : '--';
        
        // 计算额外指标
        let maHtml = '';
        if (additionalData.ma) {
            const { ma5, ma10, ma20 } = additionalData.ma;
            maHtml = `
            <div class="ma-container">
                <div class="ma-item ma5">MA5: ${ma5.toFixed(2)}</div>
                <div class="ma-item ma10">MA10: ${ma10.toFixed(2)}</div>
                <div class="ma-item ma20">MA20: ${ma20.toFixed(2)}</div>
            </div>`;
        }
        
        // 构建HTML
        return `
        <div class="stock-card ${changeClass}" data-code="${stockData.code}">
            <div class="stock-header">
                <div class="stock-title">
                    <span class="stock-name">${stockData.name}</span>
                    <span class="stock-code">${stockData.code}</span>
                </div>
                <div class="stock-price">
                    <span class="current-price">${price}</span>
                    <div class="price-change">
                        ${changeIcon} ${change} 
                        <span class="price-change-percentage">${changePercent}</span>
                    </div>
                </div>
            </div>
            <div class="stock-body">
                <div class="stock-data-grid">
                    <div class="data-item">
                        <div class="data-label">今开</div>
                        <div class="data-value">${open}</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">最高</div>
                        <div class="data-value">${high}</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">最低</div>
                        <div class="data-value">${low}</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">昨收</div>
                        <div class="data-value">${prevClose}</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">成交量</div>
                        <div class="data-value">${volume}</div>
                    </div>
                    <div class="data-item">
                        <div class="data-label">成交额</div>
                        <div class="data-value">${turnover}</div>
                    </div>
                </div>
                ${maHtml}
                <div class="stock-actions">
                    <button class="stock-btn analyze">
                        <i class="fas fa-chart-line"></i> 分析
                    </button>
                    <button class="stock-btn refresh">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                    <button class="stock-btn remove">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>`;
    },
    
    // 格式化股票代码
    formatStockCode: function(code) {
        if (!code) return '';
        
        // 已经包含前缀的情况
        if (code.startsWith('sh') || code.startsWith('sz')) {
            return code;
        }
        
        // 根据股票代码规则添加前缀
        // 6开头默认为上海交易所
        if (code.startsWith('6')) {
            return 'sh' + code;
        }
        // 0或3开头默认为深圳交易所
        else if (code.startsWith('0') || code.startsWith('3')) {
            return 'sz' + code;
        }
        // 处理特殊情况，如沪深300指数
        else if (code === '000300') {
            return 'sh' + code;
        }
        // 默认添加sz前缀
        else {
            return 'sz' + code;
        }
    },

    // 刷新单只股票数据
    refreshStock: function(stockCode) {
        return new Promise((resolve, reject) => {
            if (!stockCode) {
                reject(new Error('股票代码不能为空'));
                return;
            }
            
            // 获取按钮并显示加载状态
            const refreshBtn = document.querySelector(`.btn-refresh[data-code="${stockCode}"]`);
            if (refreshBtn) {
                refreshBtn.textContent = '刷新中...';
                refreshBtn.disabled = true;
            }
            
            // 调用获取实时数据的方法
            this.fetchStockRealTimeData(stockCode)
                .then(realTimeData => {
                    // 更新内存中的股票数据
                    const stock = this.getStock(stockCode);
                    if (stock) {
                        // 合并数据
                        const updatedStock = Object.assign({}, stock, realTimeData);
                        // 保存更新后的数据
                        this.updateStock(updatedStock);
                    }
                    resolve(realTimeData);
                })
                .catch(error => {
                    reject(error);
                })
                .finally(() => {
                    // 恢复按钮状态
                    if (refreshBtn) {
                        refreshBtn.textContent = '刷新';
                        refreshBtn.disabled = false;
                    }
                });
        });
    },
    
    // 提取AI响应中的综合评分
    extractStockScore: function(aiResponse, stockCode, stockName) {
        if (!aiResponse) return null;
        
        try {
            console.log('正在分析评分内容，长度:', aiResponse.length);
            
            // 使用多种正则表达式匹配评分表格中的"综合评分"行
            const patterns = [
                /综合评分\s*(\d+)/i,  // 简单匹配
                /综合评分[\s\S]*?(\d+)[\s\S]*?(?=短期|中期|操作|$)/i,  // 表格内匹配
                /【综合评分】[\s\S]*?(\d+)/i,  // 带括号的标题匹配
                /维度[\s\S]*?综合评分\s*(\d+)/i,  // 表格行匹配
                /综合评分\t*(\d+)/i,  // 使用制表符分隔的匹配
                /综合评分[:：]\s*(\d+)/i,  // 使用冒号分隔的匹配
                /综合评分.*?(\d+).*?(?=分|$)/i  // 带"分"字的匹配
            ];
            
            // 针对特殊表格格式的匹配
            if (aiResponse.includes('【综合评分】') || aiResponse.includes('综合评分')) {
                console.log('检测到评分相关关键词');
                
                // 尝试匹配包含表格的综合评分行
                const tableMatch = aiResponse.match(/维度.*评分.*说明[\s\S]*?综合评分[\s\S]*?(\d+)[\s\S]*?(?=\n|$)/i);
                if (tableMatch && tableMatch[1]) {
                    const score = parseInt(tableMatch[1], 10);
                    console.log('从表格中提取到综合评分:', score);
                    
                    // 处理评分
                    this._processExtractedScore(stockCode, stockName, score);
                    return score;
                }
            }
            
            // 尝试每一种模式
            for (const pattern of patterns) {
                const match = aiResponse.match(pattern);
                if (match && match[1]) {
                    const score = parseInt(match[1], 10);
                    console.log('使用模式提取到综合评分:', score, '使用的模式:', pattern);
                    
                    // 处理评分
                    this._processExtractedScore(stockCode, stockName, score);
                    return score;
                }
            }
            
            // 特殊处理：提取任何可能的分数（例如80/100这样的格式）
            const scoreMatch = aiResponse.match(/综合[^\n]*?(\d{2,3})(?:\s*\/\s*100|\s*分|\)|\])?/i);
            if (scoreMatch && scoreMatch[1]) {
                const score = parseInt(scoreMatch[1], 10);
                console.log('通过综合查找提取到评分:', score);
                
                // 处理评分
                this._processExtractedScore(stockCode, stockName, score);
                return score;
            }
            
            console.log('未找到评分信息:', stockCode, stockName);
        } catch (e) {
            console.error('提取评分失败:', e);
        }
        
        return null;
    },
    
    // 处理提取到的评分
    _processExtractedScore: function(stockCode, stockName, score) {
        if (!stockCode || !stockName || !score) return;
        
        console.log('处理提取到的评分:', score, '股票:', stockCode, stockName);
        
        // 保存评分到高评分股票列表
        this.updateHighScoreStocks(stockCode, stockName, score);
        
        // 如果评分大于等于80，上传到服务器与其他用户共享
        if (score >= 80) {
            console.log('发现高评分股票，正在上传到服务器:', stockCode, stockName, score);
            
            // 立即添加到本地高评分列表
            const stockData = {
                code: stockCode,
                name: stockName,
                score: score,
                timestamp: new Date().toISOString()
            };
            
            // 添加到高评分列表并刷新UI
            let highScoreStocks = this.getHighScoreStocks() || [];
            highScoreStocks.push(stockData);
            localStorage.setItem(this.HIGH_SCORE_STOCKS_KEY, JSON.stringify(highScoreStocks));
            this.refreshHighScoreStocksList();
            
            // 上传到服务器
            this.saveHighScoreStockToServer(stockCode, stockName, score);
            
            // 强制从服务器刷新数据
            setTimeout(() => {
                this.fetchHighScoreStocksFromServer();
                
                // 尝试直接点击刷新按钮
                const extractScoreBtn = document.getElementById('extractScoreBtn');
                if (extractScoreBtn) {
                    console.log('手动触发高评分股票刷新按钮');
                    extractScoreBtn.click();
                }
            }, 1000);
            
            // 显示通知
            if (typeof showNotification === 'function') {
                showNotification(`发现高评分(${score})股票: ${stockName}(${stockCode})`, 'success');
            }
        }
    },
    
    // 更新高评分股票列表
    updateHighScoreStocks: function(stockCode, stockName, score) {
        try {
            if (!stockCode || !stockName || !score || score <= 0) return;
            
            let highScoreStocks = this.getHighScoreStocks();
            
            // 创建或更新当前股票的评分
            const stockData = {
                code: stockCode,
                name: stockName,
                score: score,
                timestamp: new Date().toISOString()
            };
            
            // 检查是否已存在该股票
            const existingIndex = highScoreStocks.findIndex(item => item.code === stockCode);
            
            if (existingIndex >= 0) {
                // 更新现有记录
                highScoreStocks[existingIndex] = stockData;
            } else {
                // 添加新记录
                highScoreStocks.push(stockData);
            }
            
            // 按评分排序（降序）
            highScoreStocks.sort((a, b) => b.score - a.score);
            
            // 保留最高分的5只股票
            if (highScoreStocks.length > 5) {
                highScoreStocks = highScoreStocks.slice(0, 5);
            }
            
            // 保存到本地存储
            localStorage.setItem(this.HIGH_SCORE_STOCKS_KEY, JSON.stringify(highScoreStocks));
            
            // 更新界面上的高评分股票列表
            this.refreshHighScoreStocksList();
        } catch (e) {
            console.error('更新高评分股票失败:', e);
        }
    },
    
    // 上传高评分股票到服务器
    saveHighScoreStockToServer: function(stockCode, stockName, score) {
        if (!stockCode || !stockName || !score || score < 80) return;
        
        console.log('开始上传高评分股票到服务器:', stockCode, stockName, score);
        
        // 获取当前站点的基础路径，解决不同环境下的路径问题
        const basePath = window.location.pathname.includes('/index.php') 
            ? window.location.pathname.replace('/index.php', '')
            : '';
        
        // 立即先更新本地数据，不等待服务器响应
        this.updateHighScoreStocks(stockCode, stockName, score);
        
        fetch(basePath + '/api/high_score_stocks.php?action=save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                code: stockCode,
                name: stockName,
                score: score
            }),
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('服务器响应状态:', response.status, response.statusText);
            if (!response.ok) {
                throw new Error('接口请求错误: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('高评分股票上传结果:', data);
            // 上传成功后刷新高评分股票列表，获取所有用户的高评分股票
            if (data.success) {
                console.log('高评分股票上传成功，刷新列表');
                this.fetchHighScoreStocksFromServer();
                
                // 显示上传成功通知
                if (typeof showNotification === 'function') {
                    showNotification(`股票 ${stockName}(${stockCode}) 评分为${score}，已成功推荐给其他用户`, 'success');
                }
            } else {
                console.error('服务器拒绝保存高评分股票:', data.message);
                
                // 显示错误通知
                if (typeof showNotification === 'function') {
                    showNotification('上传高评分股票失败: ' + data.message, 'error');
                }
            }
        })
        .catch(error => {
            console.error('上传高评分股票失败:', error);
            
            // 显示错误通知
            if (typeof showNotification === 'function') {
                showNotification('上传高评分股票失败: ' + error.message, 'error');
            }
        });
    },
    
    // 从服务器获取高评分股票列表
    fetchHighScoreStocksFromServer: function() {
        console.log('开始从服务器获取高评分股票');
        
        // 获取当前站点的基础路径，解决不同环境下的路径问题
        const basePath = window.location.pathname.includes('/index.php') 
            ? window.location.pathname.replace('/index.php', '')
            : '';
        
        fetch(basePath + '/api/high_score_stocks.php?action=get', {
            method: 'GET',
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('服务器响应状态:', response.status, response.statusText);
            if (!response.ok) {
                throw new Error('接口请求错误: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('从服务器获取高评分股票结果:', data);
            if (data.success && data.stocks) {
                console.log('获取到', data.stocks.length, '支高评分股票');
                
                // 清空本地高评分列表，使用服务器返回的数据
                localStorage.removeItem(this.HIGH_SCORE_STOCKS_KEY);
                
                // 将服务器数据格式转换为本地存储格式
                const highScoreStocks = data.stocks.map(stock => ({
                    code: stock.stock_code,
                    name: stock.stock_name,
                    score: parseInt(stock.score, 10),
                    timestamp: stock.updated_at,
                    submittedBy: stock.submitted_by // 添加提交者信息，用于显示
                }));
                
                // 保存到本地存储
                localStorage.setItem(this.HIGH_SCORE_STOCKS_KEY, JSON.stringify(highScoreStocks));
                
                // 更新界面
                this.refreshHighScoreStocksList();
            } else {
                console.warn('服务器未返回有效的高评分股票数据');
                // 仍显示本地数据
                this.refreshHighScoreStocksList();
            }
        })
        .catch(error => {
            console.error('获取高评分股票失败:', error);
            // 失败时仍显示本地数据
            this.refreshHighScoreStocksList();
            
            // 显示错误通知
            if (typeof showNotification === 'function') {
                showNotification('获取高评分股票失败: ' + error.message, 'warning');
            }
        });
    },
    
    // 获取高评分股票列表
    getHighScoreStocks: function() {
        try {
            const data = localStorage.getItem(this.HIGH_SCORE_STOCKS_KEY);
            if (!data) return [];
            return JSON.parse(data);
        } catch (e) {
            console.error('获取高评分股票失败:', e);
            return [];
        }
    },
    
    // 刷新高评分股票列表界面
    refreshHighScoreStocksList: function() {
        const container = document.getElementById('highScoreStocksContainer');
        if (!container) return;
        
        const stocks = this.getHighScoreStocks();
        
        if (stocks.length === 0) {
            container.innerHTML = '<div class="empty-message"><i class="fas fa-info-circle"></i> 暂无高评分股票数据</div>';
            return;
        }
        
        let html = '';
        stocks.forEach(stock => {
            // 计算时间差以显示"多久前"的信息
            const timestamp = new Date(stock.timestamp);
            const now = new Date();
            const diffHours = Math.floor((now - timestamp) / (1000 * 60 * 60));
            const timeText = diffHours < 1 
                ? '刚刚' 
                : diffHours < 24 
                    ? `${diffHours}小时前` 
                    : `${Math.floor(diffHours / 24)}天前`;
            
            // 为不同评分范围设置不同颜色
            let scoreColorClass = 'score-moderate';
            if (stock.score >= 80) {
                scoreColorClass = 'score-high';
            } else if (stock.score >= 70) {
                scoreColorClass = 'score-good';
            } else if (stock.score < 50) {
                scoreColorClass = 'score-low';
            }
            
            // 添加提交者信息（如果有）
            const submittedByText = stock.submittedBy 
                 ? `<div class="submitted-by">由 π弈 量化</div>` 
                : '';
            
            html += `
                <div class="high-score-item">
                    <div class="stock-info">
                        <div class="stock-name-code">${stock.name} (${stock.code})</div>
                        <div class="score-time">${timeText}</div>
                        ${submittedByText}
                    </div>
                    <div class="stock-score ${scoreColorClass}">${stock.score}</div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    },
    
    // 初始化高评分股票区域
    initHighScoreStocks: function() {
        // 先从服务器获取高评分股票
        this.fetchHighScoreStocksFromServer();
        
        // 设置定时刷新（每5分钟刷新一次）
        setInterval(() => {
            this.fetchHighScoreStocksFromServer();
        }, 5 * 60 * 1000);
        
        // 绑定提取按钮事件
        const extractBtn = document.getElementById('extractScoreBtn');
        if (extractBtn) {
            extractBtn.addEventListener('click', () => {
                this.extractScoresFromLatestChat();
            });
        }
        
        // 添加通用事件处理
        const container = document.getElementById('highScoreStocksContainer');
        if (container) {
            // 当点击高评分股票项时，可以添加该股票或显示详情
            container.addEventListener('click', (e) => {
                const item = e.target.closest('.high-score-item');
                if (!item) return;
                
                // 从DOM中提取股票代码和名称
                const infoElement = item.querySelector('.stock-name-code');
                if (infoElement) {
                    const infoText = infoElement.textContent;
                    const matches = infoText.match(/(.+) \((.+)\)/);
                    
                    if (matches && matches.length >= 3) {
                        const stockName = matches[1];
                        const stockCode = matches[2];
                        
                        // 判断是否已添加到我的股票
                        const userStocks = this.getAllStocks();
                        const isAdded = userStocks.some(s => s.code === stockCode);
                        
                        if (!isAdded) {
                            // 如果未添加，则提示用户是否要添加
                            if (confirm(`是否将 ${stockName}(${stockCode}) 添加到我的股票？`)) {
                                this.addStock(stockCode, stockName);
                                showNotification(`已添加 ${stockName} 到我的股票`, 'success');
                            }
                        } else {
                            // 如果已添加，则滚动到该股票位置
                            const stockElement = document.querySelector(`.stock-card[data-code="${stockCode}"]`);
                            if (stockElement) {
                                stockElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                setTimeout(() => {
                                    stockElement.classList.add('highlight-stock');
                                    setTimeout(() => {
                                        stockElement.classList.remove('highlight-stock');
                                    }, 2000);
                                }, 500);
                            }
                        }
                    }
                }
            });
            

        }
    },
    
    // 从最近的量化分析中提取评分
    extractScoresFromLatestChat: function() {
        console.log('开始从最近的聊天记录中提取评分');
        
        // 获取所有聊天消息元素
        const chatMessages = document.querySelectorAll('.message-content');
        
        if (chatMessages.length === 0) {
            showNotification('未找到任何量化分析结果', 'warning');
            return;
        }
        
        // 遍历所有消息，尝试提取评分
        let foundScores = 0;
        let extractedScores = [];
        
        chatMessages.forEach(chatMsg => {
            // 获取消息文本内容
            const msgContent = chatMsg.textContent || chatMsg.innerText;
            
            // 尝试从内容中提取股票代码和名称
            let stockCode = null;
            let stockName = null;
            
            // 尝试匹配股票代码和名称模式: "股票名 (股票代码)"
            const stockInfoMatch = msgContent.match(/([^\s\(\)]+)\s*\(([0-9]{6})\)/);
            if (stockInfoMatch && stockInfoMatch.length >= 3) {
                stockName = stockInfoMatch[1].trim();
                stockCode = stockInfoMatch[2].trim();
                
                // 进一步尝试匹配带空格的股票名称
                const fullNameMatch = msgContent.match(/(.+?)\s*\(([0-9]{6})\)/);
                if (fullNameMatch && fullNameMatch.length >= 3) {
                    stockName = fullNameMatch[1].trim();
                }
                
                console.log('提取到股票信息:', stockName, stockCode);
                
                // 尝试提取评分
                const score = this.extractStockScore(msgContent, stockCode, stockName);
                
                if (score !== null) {
                    foundScores++;
                    extractedScores.push({ code: stockCode, name: stockName, score: score });
                }
            }
            
            // 添加通用提取方式，即使没有明确的股票代码格式
            if (!stockCode || !stockName) {
                // 尝试提取评分信息，即使没有标准格式的股票代码
                const scoreMatches = [
                    msgContent.match(/综合评分[^0-9]*(\d{2,3})/i),
                    msgContent.match(/综合[^0-9\n]*(\d{2,3})[^0-9\n]*分/i),
                    msgContent.match(/总分[^0-9]*(\d{2,3})/i)
                ];
                
                for (const match of scoreMatches) {
                    if (match && match[1]) {
                        const score = parseInt(match[1], 10);
                        if (score >= 80) {
                            console.log('找到高评分 (通用提取):', score);
                            // 检查是否能从页面其他位置获取股票信息
                            const titleElement = document.querySelector('.section-title span, .chat-header h3, .stock-title');
                            if (titleElement) {
                                const titleText = titleElement.textContent || '';
                                const titleMatch = titleText.match(/(.+?)\s*\(([0-9]{6})\)/);
                                if (titleMatch && titleMatch.length >= 3) {
                                    const extractedName = titleMatch[1].trim();
                                    const extractedCode = titleMatch[2].trim();
                                    console.log('从标题提取到股票信息:', extractedName, extractedCode);
                                    
                                    // 直接将提取到的高评分股票保存到服务器
                                    this.saveHighScoreStockToServer(extractedCode, extractedName, score);
                                    
                                    foundScores++;
                                    extractedScores.push({ code: extractedCode, name: extractedName, score: score });
                                    break; // 找到一个有效匹配后退出循环
                                }
                            }
                        }
                    }
                }
            }
        });
        
        // 另外，直接从最新的AI回复中提取内容
        const latestAIMessage = document.querySelector('.message.ai:last-child .message-content');
        if (latestAIMessage) {
            const text = latestAIMessage.textContent || latestAIMessage.innerText || '';
            
            // 尝试从文本中直接搜索评分
            const scoreMatches = [
                text.match(/综合评分[^0-9]*(\d{2,3})/i),
                text.match(/综合[^0-9\n]*(\d{2,3})[^0-9\n]*分/i),
                text.match(/总分[^0-9]*(\d{2,3})/i),
                text.match(/总体评分[^0-9]*(\d{2,3})/i)
            ];
            
            // 寻找股票信息
            const stockMatches = text.match(/(.+?)\s*\(([0-9]{6})\)/);
            let stockCode = null;
            let stockName = null;
            
            if (stockMatches && stockMatches.length >= 3) {
                stockName = stockMatches[1].trim();
                stockCode = stockMatches[2].trim();
                console.log('从最新AI回复中提取到股票信息:', stockName, stockCode);
            } else {
                // 尝试从页面其他位置获取
                const titleElement = document.querySelector('.section-title span, .chat-header h3, .stock-title');
                if (titleElement) {
                    const titleText = titleElement.textContent || '';
                    const titleMatch = titleText.match(/(.+?)\s*\(([0-9]{6})\)/);
                    if (titleMatch && titleMatch.length >= 3) {
                        stockName = titleMatch[1].trim();
                        stockCode = titleMatch[2].trim();
                        console.log('从标题提取到股票信息:', stockName, stockCode);
                    }
                }
            }
            
            // 如果有股票信息，尝试提取评分并保存
            if (stockCode && stockName) {
                for (const match of scoreMatches) {
                    if (match && match[1]) {
                        const score = parseInt(match[1], 10);
                        if (score >= 80) {
                            console.log('从最新AI回复中找到高评分:', score, stockCode, stockName);
                            
                            // 直接保存高评分股票
                            this.saveHighScoreStockToServer(stockCode, stockName, score);
                            console.log('已直接保存高评分股票到服务器:', stockCode, stockName, score);
                            
                            foundScores++;
                            extractedScores.push({ code: stockCode, name: stockName, score: score });
                            break; // 找到一个有效匹配后退出循环
                        }
                    }
                }
            }
        }
        
        if (foundScores > 0) {
            showNotification(`成功从量化分析中提取了${foundScores}个评分`, 'success');
            
            // 刷新高评分股票列表
            this.refreshHighScoreStocksList();
            
            // 打印提取的评分信息，用于调试
            console.log('提取的评分:', extractedScores);
            
            // 如果有80分以上的，显示特别通知并保存到服务器
            const highScores = extractedScores.filter(item => item.score >= 80);
            if (highScores.length > 0) {
                const highScoreNames = highScores.map(item => `${item.name}(${item.score}分)`).join('、');
                showNotification(`发现高评分股票: ${highScoreNames}`, 'info');
                
                // 强制确保所有高评分股票都保存到服务器
                highScores.forEach(stock => {
                    this.saveHighScoreStockToServer(stock.code, stock.name, stock.score);
                    console.log('强制再次保存高评分股票到服务器:', stock.code, stock.name, stock.score);
                });
                
                // 延迟刷新服务器数据，确保所有提交都已处理
                setTimeout(() => {
                    this.fetchHighScoreStocksFromServer();
                }, 1000);
            }
        } else {
            showNotification('未能从量化分析中提取到任何评分', 'warning');
        }
    },
    
    // API连接测试函数（仅用于调试）
    testHighScoreApi: function() {
        console.log('开始测试高评分股票API');
        
        // 创建一个测试记录
        const testStock = {
            code: '000001',
            name: '平安银行',
            score: 88
        };
        
        // 获取当前站点的基础路径，解决不同环境下的路径问题
        const basePath = window.location.pathname.includes('/index.php') 
            ? window.location.pathname.replace('/index.php', '')
            : '';
        
        // 先测试获取API
        fetch(basePath + '/api/high_score_stocks.php?action=get', {
            method: 'GET',
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('GET测试 - 状态码:', response.status);
            if (!response.ok) {
                throw new Error('GET API测试失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('GET测试 - 响应数据:', data);
            showNotification('GET API测试成功', 'success');
            
            // 再测试保存API
            return fetch(basePath + '/api/high_score_stocks.php?action=save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testStock),
                credentials: 'same-origin'
            });
        })
        .then(response => {
            console.log('POST测试 - 状态码:', response.status);
            if (!response.ok) {
                throw new Error('POST API测试失败: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('POST测试 - 响应数据:', data);
            showNotification('POST API测试成功', 'success');
            
            // 测试完成后刷新列表
            this.fetchHighScoreStocksFromServer();
        })
        .catch(error => {
            console.error('API测试失败:', error);
            showNotification('API测试失败: ' + error.message, 'error');
        });
    }
};

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('前端股票管理模块开始初始化...');
    
    // 初始化前端存储模块
    FrontendStocks.init();
    
    // 检查localStorage是否可用
    try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        console.log('localStorage测试成功');
    } catch (e) {
        console.error('localStorage不可用:', e);
    }
    
    // 如果localStorage中已经指定使用前端模式，则启用
    if (localStorage.getItem(FrontendStocks.STORAGE_MODE_KEY) === 'true') {
        console.log('自动启用前端存储模式');
        // 同步UI开关状态
        const frontendToggle = document.getElementById('frontendStorageToggle');
        if (frontendToggle) {
            frontendToggle.checked = true;
        }
    }

    // 强制启用前端模式，无论localStorage中的值如何
    console.log('强制启用前端存储模式');
    localStorage.setItem(FrontendStocks.STORAGE_MODE_KEY, 'true');
    
    // 更新UI开关状态
    const frontendToggle = document.getElementById('frontendStorageToggle');
    if (frontendToggle) {
        frontendToggle.checked = true;
        frontendToggle.disabled = true; // 确保不可更改
    }
    
    // 确保FrontendStocks启用了前端模式
    FrontendStocks.enable();
    
    // 测试获取股票列表
    const stocks = FrontendStocks.getAllStocks();
    console.log('当前本地存储的股票列表:', stocks);
    
    // 导出到全局对象
    window.FrontendStocks = FrontendStocks;
    console.log('前端股票管理模块初始化完成，已导出到window.FrontendStocks');
    
    // 添加全局聊天历史入口按钮
    setTimeout(function() {
        // 寻找适合添加按钮的位置
        const headerActions = document.querySelector('.header-actions');
        const dashboardActions = document.querySelector('.dashboard-actions');
        const actionContainer = headerActions || dashboardActions || document.querySelector('.user-actions');
        
        if (actionContainer) {
            // 创建聊天历史按钮
            const chatHistoryButton = document.createElement('button');
            chatHistoryButton.className = 'btn btn-secondary chat-history-btn';
            chatHistoryButton.textContent = '聊天历史';
            chatHistoryButton.title = '查看所有聊天历史记录';
            chatHistoryButton.style.marginLeft = '10px';
            
            // 添加点击事件
            chatHistoryButton.addEventListener('click', function() {
                if (window.ChatHistory && typeof ChatHistory.showChatHistoryDialog === 'function') {
                    ChatHistory.showChatHistoryDialog();
                } else {
                    console.error('ChatHistory模块未正确加载');
                    alert('聊天历史功能暂不可用');
                }
            });
            
            // 添加到页面
            actionContainer.appendChild(chatHistoryButton);
            console.log('已添加全局聊天历史按钮');
        } else {
            console.log('未找到适合添加聊天历史按钮的位置，尝试添加到导航栏');
            
            // 尝试添加到导航栏
            const navbar = document.querySelector('.navbar-nav');
            if (navbar) {
                const navItem = document.createElement('li');
                navItem.className = 'nav-item';
                
                const navLink = document.createElement('a');
                navLink.className = 'nav-link';
                navLink.href = '#';
                navLink.textContent = '聊天历史';
                navLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (window.ChatHistory && typeof ChatHistory.showChatHistoryDialog === 'function') {
                        ChatHistory.showChatHistoryDialog();
                    } else {
                        console.error('ChatHistory模块未正确加载');
                        alert('聊天历史功能暂不可用');
                    }
                });
                
                navItem.appendChild(navLink);
                navbar.appendChild(navItem);
                console.log('已添加聊天历史导航链接');
            } else {
                console.warn('未找到合适的位置添加聊天历史入口，将只能通过股票卡片上的按钮访问');
            }
        }
    }, 500);
    
    // 立即加载已保存的股票数据
    setTimeout(function() {
        // 检查是否有股票列表加载函数
        if (typeof window.loadUserStocks === 'function') {
            console.log('立即加载本地存储的股票数据');
            window.loadUserStocks();
        } else {
            console.log('loadUserStocks函数不可用，将使用直接显示方法');
            
            // 获取股票数据
            const stocks = FrontendStocks.getAllStocks();
            if (stocks && stocks.length > 0) {
                console.log('发现本地存储的股票:', stocks.length, '条');
                
                // 查找股票列表容器
                const stockList = document.getElementById('stockList');
                if (stockList) {
                    // 清空现有内容
                    stockList.innerHTML = '';
                    
                    // 创建股票网格
                    const stockGrid = document.createElement('div');
                    stockGrid.className = 'stock-grid';
                    
                    // 添加股票卡片
                    stocks.forEach(stock => {
                        console.log('创建股票卡片:', stock.code, stock.name);
                        const stockCard = FrontendStocks.createStockCard(stock);
                        stockGrid.appendChild(stockCard);
                    });
                    
                    // 添加到页面
                    stockList.appendChild(stockGrid);
                    console.log('股票列表已加载到页面');
                } else {
                    console.error('无法找到股票列表容器');
                }
            } else {
                console.log('本地存储中没有股票数据');
            }
        }
    }, 300); // 稍微延迟确保DOM已完全加载并且其他脚本已执行
});

// 修改股票卡片创建方法，添加历史按钮
const originalCreateStockCard = FrontendStocks.createStockCard;
FrontendStocks.createStockCard = function(stock) {
    // 调用原始方法创建卡片
    const card = originalCreateStockCard.call(this, stock);
    
    // 添加历史记录按钮
    const stockActions = card.querySelector('.stock-actions');
    if (stockActions) {
        const historyBtn = document.createElement('button');
        historyBtn.className = 'btn-history';
        historyBtn.setAttribute('data-code', stock.code);
        historyBtn.textContent = '记录';
        historyBtn.style.backgroundColor = '#673ab7';
        historyBtn.style.color = 'white';
        historyBtn.style.padding = '6px 0';
        historyBtn.style.border = 'none';
        historyBtn.style.borderRadius = '4px';
        historyBtn.style.cursor = 'pointer';
        historyBtn.style.fontSize = '12px';
        historyBtn.style.flex = '1';
        historyBtn.style.display = 'flex';
        historyBtn.style.alignItems = 'center';
        historyBtn.style.justifyContent = 'center';
        
        // 绑定历史记录按钮事件
        historyBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const stockCode = this.getAttribute('data-code');
            
            // 显示历史记录对话框
            if (window.ChatHistory && typeof ChatHistory.showChatHistoryDialog === 'function') {
                ChatHistory.showChatHistoryDialog(stockCode);
            }
        });
        
        // 找到咨询按钮和刷新按钮
        const consultBtn = stockActions.querySelector('.btn-consult');
        const refreshBtn = stockActions.querySelector('.btn-refresh');
        
        // 修改咨询按钮尺寸，使其与其他按钮一致
        if (consultBtn) {
            consultBtn.style.padding = '6px 0';
            consultBtn.style.margin = '0';
            consultBtn.style.flex = '1';
            consultBtn.style.width = 'auto';
            consultBtn.style.display = 'flex';
            consultBtn.style.alignItems = 'center';
            consultBtn.style.justifyContent = 'center';
        }
        
        // 将历史按钮插入到咨询按钮后面、刷新按钮前面
        if (consultBtn && refreshBtn) {
            stockActions.insertBefore(historyBtn, refreshBtn);
        } else {
            // 如果找不到特定按钮，则添加到末尾
            stockActions.appendChild(historyBtn);
        }
    }
    
    return card;
};

// 添加全局查看所有历史记录的方法
window.showAllChatHistory = function() {
    if (window.ChatHistory && typeof ChatHistory.showChatHistoryDialog === 'function') {
        ChatHistory.showChatHistoryDialog();
    }
};

// 立即导出模块到全局 (确保在脚本加载完成后立即可用)
window.FrontendStocks = FrontendStocks;
console.log('FrontendStocks 模块已立即导出到全局');

// 获取API密钥
function getApiKey() {
    // 尝试从页面中获取API密钥
    const apiKeyInput = document.getElementById('apiKey');
    if (apiKeyInput && apiKeyInput.value) {
        const key = apiKeyInput.value.trim();
        if (key) {
            // 确保保存到本地存储
            localStorage.setItem('api_key', key);
            sessionStorage.setItem('api_key', key);
            return key;
        }
    }
    
    // 如果页面上没有，尝试从sessionStorage获取
    const sessionKey = sessionStorage.getItem('api_key');
    if (sessionKey && sessionKey.trim()) {
        return sessionKey.trim();
    }
    
    // 尝试从localStorage获取
    const localKey = localStorage.getItem('api_key');
    if (localKey && localKey.trim()) {
        // 同步到sessionStorage
        sessionStorage.setItem('api_key', localKey.trim());
        return localKey.trim();
    }
    
    // 如果都没有，返回空字符串
    console.error('无法获取API密钥');
    
    // 显示通知
    if (typeof showNotification === 'function') {
        showNotification('请在API密钥设置中保存有效的密钥', 'error');
    }
    
    return '';
}

/**
 * 显示聊天输入对话框
 */
function showChatInputDialog(stockCode, stockName) {
    // 检查是否已有对话框存在
    let dialog = document.getElementById('chatInputDialog');
    if (dialog) {
        dialog.remove();
    }
    
    // 创建对话框
    dialog = document.createElement('div');
    dialog.id = 'chatInputDialog';
    dialog.className = 'chat-input-dialog';
    dialog.style.position = 'fixed';
    dialog.style.top = '50%';
    dialog.style.left = '50%';
    dialog.style.transform = 'translate(-50%, -50%)';
    dialog.style.backgroundColor = '#fff';
    dialog.style.padding = '20px';
    dialog.style.borderRadius = '8px';
    dialog.style.boxShadow = '0 0 20px rgba(0,0,0,0.2)';
    dialog.style.zIndex = '1000';
    dialog.style.width = '500px';
    dialog.style.maxWidth = '90%';
    
    // 创建对话框标题
    const title = document.createElement('h3');
    title.textContent = `咨询关于 ${stockName} (${stockCode}) 的问题`;
    title.style.margin = '0 0 15px 0';
    title.style.borderBottom = '1px solid #eee';
    title.style.paddingBottom = '10px';
    dialog.appendChild(title);
    
    // 创建输入框
    const input = document.createElement('textarea');
    input.placeholder = '请输入您想咨询的问题...如:1.预测明天的走势.2.我是xx元的成本,请给我操作建议';
    input.style.width = '100%';
    input.style.padding = '10px';
    input.style.borderRadius = '4px';
    input.style.border = '1px solid #ddd';
    input.style.marginBottom = '15px';
    input.style.minHeight = '80px';
    input.style.boxSizing = 'border-box';
    dialog.appendChild(input);
    
    // 创建按钮容器
    const buttonContainer = document.createElement('div');
    buttonContainer.style.display = 'flex';
    buttonContainer.style.justifyContent = 'flex-end';
    buttonContainer.style.gap = '10px';
    
    // 取消按钮
    const cancelButton = document.createElement('button');
    cancelButton.textContent = '取消';
    cancelButton.style.padding = '8px 15px';
    cancelButton.style.border = 'none';
    cancelButton.style.borderRadius = '4px';
    cancelButton.style.backgroundColor = '#f5f5f5';
    cancelButton.style.cursor = 'pointer';
    cancelButton.addEventListener('click', () => {
        dialog.remove();
    });
    buttonContainer.appendChild(cancelButton);
    
    // 发送按钮
    const sendButton = document.createElement('button');
    sendButton.textContent = '发送';
    sendButton.style.padding = '8px 15px';
    sendButton.style.border = 'none';
    sendButton.style.borderRadius = '4px';
    sendButton.style.backgroundColor = '#4CAF50';
    sendButton.style.color = 'white';
    sendButton.style.cursor = 'pointer';
    sendButton.addEventListener('click', () => {
        const message = input.value.trim();
        if (message) {
            // 发送聊天消息
            sendChatMessage(message, stockCode, stockName);
            dialog.remove();
        } else {
            // 提示用户输入内容
            input.style.border = '1px solid red';
            setTimeout(() => {
                input.style.border = '1px solid #ddd';
            }, 2000);
        }
    });
    buttonContainer.appendChild(sendButton);
    
    // 添加按钮容器到对话框
    dialog.appendChild(buttonContainer);
    
    // 创建半透明背景
    const overlay = document.createElement('div');
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
    overlay.style.zIndex = '999';
    overlay.addEventListener('click', () => {
        overlay.remove();
        dialog.remove();
    });
    
    // 阻止对话框点击事件冒泡
    dialog.addEventListener('click', (e) => {
        e.stopPropagation();
    });
    
    // 添加按键事件 - 按Escape关闭对话框
    document.addEventListener('keydown', function escapeHandler(e) {
        if (e.key === 'Escape') {
            overlay.remove();
            dialog.remove();
            document.removeEventListener('keydown', escapeHandler);
        }
    });
    
    // 按Enter发送，但允许Shift+Enter换行
    input.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendButton.click();
        }
    });
    
    // 添加到文档
    document.body.appendChild(overlay);
    document.body.appendChild(dialog);
    
    // 自动聚焦到输入框
    input.focus();
}

/**
 * 发送聊天消息
 */
function sendChatMessage(message, stockCode, stockName) {
    // 添加用户消息到聊天容器
    addChatMessage(message, 'user');
    
    // 添加"量化中"提示
    addChatMessage('量化中', 'ai', true);
    
    // 获取API密钥
    let userApiKey = '';
    // 尝试从数据属性获取API密钥
    const apiKeyElement = document.querySelector('.api-key-display');
    if (apiKeyElement && apiKeyElement.dataset.apiKey) {
        userApiKey = apiKeyElement.dataset.apiKey;
    } else {
        // 从localStorage获取
        userApiKey = localStorage.getItem('api_key');
    }
    
    if (!userApiKey) {
        addChatMessage("无法获取API密钥，请先设置有效的API密钥<div class=\"analysis-complete\"><i class=\"fas fa-exclamation-circle\"></i> 量化未完成</div>", 'ai');
        return;
    }
    
    // 构建消息内容 - 包含股票信息
    let content = message;
    if (stockCode && stockName) {
        // 先获取股票实时数据
        FrontendStocks.fetchStockRealTimeData(stockCode)
            .then(stockData => {
                // 格式化股票数据为中文
                let stockInfo = "";
                if (stockData && Object.keys(stockData).length > 0) {
                    stockInfo = formatStockDataToChinese(stockData);
                    console.log('获取到股票实时数据:', stockInfo);
                }
                
                // 构建完整的消息内容，包含实时数据
                content = `关于${stockName}(${stockCode})的问题: ${message}\n\n实时数据：${stockInfo}`;
                
                // 获取最近7天历史交易数据
                fetch(`api/stock_proxy.php?symbol=${stockCode}&type=hist`)
                    .then(res => res.json())
                    .then(histData => {
                        if (histData && histData.data && Array.isArray(histData.data)) {
                            // 添加历史数据到消息内容
                            content += `\n\n最近7日历史交易数据: ${JSON.stringify(histData.data)}`;
                        }
                        // 继续处理API调用
                        processApiCall(content, userApiKey, stockCode, stockName, message);
                    })
                    .catch(error => {
                        console.error('获取历史数据失败:', error);
                        // 即使没有历史数据，也继续处理API调用
                        processApiCall(content, userApiKey, stockCode, stockName, message);
                    });
            })
            .catch(error => {
                console.error('获取股票实时数据失败:', error);
                // 如果获取实时数据失败，仍然继续处理原始消息
                content = `关于${stockName}(${stockCode})的问题: ${message}`;
                processApiCall(content, userApiKey, stockCode, stockName, message);
            });
    } else {
        // 如果没有股票代码和名称，直接处理原始消息
        processApiCall(content, userApiKey, stockCode, stockName, message);
    }
}

// 处理API调用的辅助函数
function processApiCall(content, userApiKey, stockCode, stockName, message) {
    // 直接发送到AI服务处理，因为api_tracker.js已经处理了API调用跟踪
    sendToAIService(content, userApiKey, stockCode, stockName, message);
}

// 添加更新剩余次数的辅助函数
function updateRemainingCount(count) {
    const remainingElement = document.querySelector('.remaining-requests span');
    if (remainingElement) {
        remainingElement.textContent = count;
        console.log('已更新剩余次数显示为:', count);
    } else {
        console.warn('未找到剩余次数显示元素');
    }
    
    // 如果剩余次数较少，显示提示
    if (count <= 5) {
        if (typeof window.showNotification === 'function') {
            window.showNotification(`API调用次数不足，剩余${count}次，请及时充值`, 'warning');
        }
    }
}

function sendToAIService(content, userApiKey, stockCode, stockName, message) {
    console.log('发送到AI服务的消息:', content);
    
    // 使用后端代理发送请求
    fetch('/api/chat_proxy.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            api_key: userApiKey,
            content: content
        })
    })
    .then(response => {
        console.log('AI聊天API响应状态:', response.status);
        // 检查响应状态
        if (!response.ok) {
            if (response.status === 401) {
                throw new Error('API密钥无效或未授权');
            } else if (response.status === 403) {
                throw new Error('API访问被拒绝，可能是密钥权限不足');
            } else if (response.status === 429) {
                throw new Error('请求次数超过限制，请稍后再试');
            } else {
                throw new Error(`API请求失败，状态码：${response.status}`);
            }
        }
        return response.json();
    })
    .then(data => {
        console.log('AI聊天响应:', data);
        
        // 更新AI响应 - 支持Markdown渲染
        if (data.choices && data.choices[0] && data.choices[0].message) {
            const aiResponse = data.choices[0].message.content;
            
            // AI响应已收到，直接触发高评分刷新按钮
            setTimeout(() => {
                console.log('AI回复收到，模拟点击高评分刷新按钮');
                const extractScoreBtn = document.getElementById('extractScoreBtn');
                if (extractScoreBtn) {
                    console.log('正在触发高评分刷新按钮');
                    extractScoreBtn.click();
                } else {
                    console.error('找不到高评分刷新按钮');
                }
            }, 1000);
            
            // 使用marked.js渲染Markdown (需要先加载marked库)
            if (window.marked) {
                const markedContent = marked.parse(data.choices[0].message.content);
                addChatMessage(markedContent + '<div class="analysis-complete"><i class="fas fa-check-circle"></i> 量化完成</div>', 'ai');
            } else {
                // 如果marked库不可用，回退到普通文本显示，但保留换行
                const formattedContent = data.choices[0].message.content.replace(/\n/g, '<br>');
                addChatMessage(formattedContent + '<div class="analysis-complete"><i class="fas fa-check-circle"></i> 量化完成</div>', 'ai');
            }
            
            // 保存聊天记录到本地存储
            if (window.ChatHistory && typeof ChatHistory.saveMessage === 'function') {
                ChatHistory.saveMessage(stockCode, stockName, message, data.choices[0].message.content);
                console.log('聊天记录已保存');
            }
        } else {
            addChatMessage('无法获取回复，API没有返回预期的数据格式。<div class="analysis-complete"><i class="fas fa-exclamation-circle"></i> 量化未完成</div>', 'ai');
        }
    })
    .catch(error => {
        console.error('AI聊天错误:', error);
        addChatMessage(`请求失败: ${error.message}<div class="analysis-complete"><i class="fas fa-exclamation-circle"></i> 量化未完成</div>`, 'ai');
        
        // 如果是API密钥问题，显示特别提示
        if (error.message.includes('密钥') || error.message.includes('401') || error.message.includes('403')) {
            addChatMessage('请在仪表板的API密钥设置中检查您的密钥是否正确。<div class="analysis-complete"><i class="fas fa-exclamation-circle"></i> 量化未完成</div>', 'ai');
        }
    });
}

/**
 * 格式化股票数据为中文描述
 */
function formatStockDataToChinese(stockData) {
    if (!stockData) return "";
    
    const result = [];
    
    // 处理价格相关信息
    if (stockData.price) result.push(`当前价格: ${stockData.price}元`);
    if (stockData.change) {
        const changeValue = parseFloat(stockData.change);
        const prefix = changeValue >= 0 ? '+' : '';
        result.push(`涨跌幅: ${prefix}${stockData.change}%`);
    }
    
    // 处理成交量信息
    if (stockData.volume) result.push(`成交量: ${stockData.volume}`);
    
    // 处理最高最低价
    if (stockData.high) result.push(`当日最高: ${stockData.high}元`);
    if (stockData.low) result.push(`当日最低: ${stockData.low}元`);
    
    // 处理开盘收盘价
    if (stockData.open) result.push(`开盘价: ${stockData.open}元`);
    if (stockData.close) result.push(`收盘价: ${stockData.close}元`);
    
    // 处理五档买卖盘数据
    const hasLevel5Data = stockData.pb1 || stockData.ps1;
    if (hasLevel5Data) {
        const buyData = [];
        const sellData = [];
        
        // 处理买盘数据
        for (let i = 1; i <= 5; i++) {
            if (stockData[`pb${i}`] && stockData[`vb${i}`]) {
                buyData.push(`买${i}价: ${stockData[`pb${i}`]}元 / ${stockData[`vb${i}`]}手`);
            }
        }
        
        // 处理卖盘数据
        for (let i = 1; i <= 5; i++) {
            if (stockData[`ps${i}`] && stockData[`vs${i}`]) {
                sellData.push(`卖${i}价: ${stockData[`ps${i}`]}元 / ${stockData[`vs${i}`]}手`);
            }
        }
        
        if (sellData.length > 0) result.push(`卖盘: ${sellData.join(', ')}`);
        if (buyData.length > 0) result.push(`买盘: ${buyData.join(', ')}`);
    }
    
    // 处理分时成交数据
    if (stockData.minuteTrades && Array.isArray(stockData.minuteTrades) && stockData.minuteTrades.length > 0) {
        const latestTrades = stockData.minuteTrades.slice(0, 3).map(trade => {
            return `${trade.time}: ${trade.price}元/${trade.volume}手`;
        });
        result.push(`最近成交: ${latestTrades.join(', ')}`);
    } else if (stockData.minuteTrades && typeof stockData.minuteTrades === 'object') {
        // 处理单个对象格式的分时成交
        const trade = stockData.minuteTrades;
        if (trade.time && trade.price) {
            result.push(`最近成交: ${trade.time}: ${trade.price}元/${trade.volume || '未知'}手`);
        }
    }
    
    return result.join('，');
}

/**
 * 调用AI分析API
 */
function callAnalysisAPI(content) {
 //   console.log('发送到AI服务的消息:', content);
    
    const apiKey = sessionStorage.getItem('api_key') || '';
    if (!apiKey) {
        console.error('API密钥未设置');
        throw new Error('API密钥未设置，请先设置API密钥');
    }
    
    // 使用后端代理发送请求
    return fetch('/api/chat_proxy.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            api_key: apiKey,
            content: content
        })
    })
    .then(response => {
        console.log('AI分析API响应状态:', response.status);
        
        // 检查响应状态
        if (!response.ok) {
            // 尝试解析错误响应
            return response.json()
                .then(errorData => {
                    throw new Error(errorData.message || `请求失败 (${response.status})`);
                })
                .catch(e => {
                    // 如果无法解析JSON，使用通用错误消息
                    throw new Error(`请求失败 (${response.status}): ${e.message}`);
                });
        }
        return response.json();
    })
    .then(data => {
        console.log('AI分析响应:', data);
        
        if (!data || !data.choices || !data.choices[0] || !data.choices[0].message) {
            throw new Error('API返回的数据格式不正确');
        }
        
        // 提取评分信息
        if (stockCode && stockName && FrontendStocks && typeof FrontendStocks.extractStockScore === 'function') {
            FrontendStocks.extractStockScore(data.choices[0].message.content, stockCode, stockName);
            
            // 自动触发高评分股票刷新按钮点击
            setTimeout(() => {
                const extractScoreBtn = document.getElementById('extractScoreBtn');
                if (extractScoreBtn) {
                    console.log('模拟点击高评分刷新按钮');
                    extractScoreBtn.click();
                }
            }, 300);
        }
        
        return data;
    })
    .catch(error => {
        console.error('AI分析错误:', error);
        
        // 构建用户友好的错误消息
        let errorMessage = '分析请求失败';
        if (error.message.includes('API密钥')) {
            errorMessage = '请联系管理员检查API密钥设置是否正确';
        } else if (error.message.includes('429')) {
            errorMessage = '请求次数超过限制，请稍后再试';
        } else if (error.message.includes('500')) {
            errorMessage = '服务器处理请求时出错，可能是量化次数已用完';
        }
        
        // 显示错误通知
        if (typeof showNotification === 'function') {
            showNotification(errorMessage, 'error');
        }
        
        throw new Error(errorMessage);
    });
}

// 添加聊天历史存储相关功能
const ChatHistory = {
    // 存储键名
    STORAGE_KEY: 'stock_chat_history',
    
    // 获取所有聊天历史
    getAllHistory: function() {
        try {
            const historyJSON = localStorage.getItem(this.STORAGE_KEY);
            if (!historyJSON) return [];
            return JSON.parse(historyJSON);
        } catch (e) {
            console.error('获取聊天历史失败:', e);
            return [];
        }
    },
    
    // 保存聊天消息到历史记录
    saveMessage: function(stockCode, stockName, userMessage, aiResponse, timestamp = new Date().toISOString()) {
        try {
            const history = this.getAllHistory();
            
            // 创建新的聊天记录
            const chatItem = {
                id: Date.now().toString(),
                stockCode,
                stockName,
                userMessage,
                aiResponse,
                timestamp
            };
            
            // 获取当前股票的记录
            const stockRecords = history.filter(item => item.stockCode === stockCode);
            
            // 如果该股票的记录已经达到3条，保留最新的2条并加上当前新增的记录
            if (stockRecords.length >= 3) {
                // 按时间戳排序，保留最新的
                stockRecords.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
                
                // 获取要保留的记录ID (最新的2条)
                const keepIds = stockRecords.slice(0, 2).map(item => item.id);
                
                // 过滤掉该股票多余的记录，保留其他股票的所有记录
                const filteredHistory = history.filter(item => 
                    item.stockCode !== stockCode || keepIds.includes(item.id)
                );
                
                // 添加新记录
                filteredHistory.push(chatItem);
                
                // 保存更新后的历史记录
                localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredHistory));
                console.log(`保存聊天记录: ${stockCode} 已达到限制(3条)，清除旧记录`);
            } else {
                // 未达到限制，直接添加
                history.push(chatItem);
                localStorage.setItem(this.STORAGE_KEY, JSON.stringify(history));
                console.log(`保存聊天记录: ${stockCode} 当前记录数: ${stockRecords.length + 1}`);
            }
            
            return chatItem;
        } catch (e) {
            console.error('保存聊天历史失败:', e);
            return null;
        }
    },
    
    // 获取指定股票的聊天历史
    getHistoryByStockCode: function(stockCode) {
        try {
            const history = this.getAllHistory();
            return history.filter(item => item.stockCode === stockCode);
        } catch (e) {
            console.error('获取股票聊天历史失败:', e);
            return [];
        }
    },
    
    // 删除聊天历史记录
    deleteHistory: function(id) {
        try {
            const history = this.getAllHistory();
            const filtered = history.filter(item => item.id !== id);
            localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filtered));
            return true;
        } catch (e) {
            console.error('删除聊天历史失败:', e);
            return false;
        }
    },
    
    // 清空所有聊天历史
    clearAllHistory: function() {
        try {
            localStorage.removeItem(this.STORAGE_KEY);
            return true;
        } catch (e) {
            console.error('清空聊天历史失败:', e);
            return false;
        }
    },
    
    // 显示聊天历史对话框
    showChatHistoryDialog: function(stockCode = null) {
        // 获取聊天历史
        const history = stockCode ? this.getHistoryByStockCode(stockCode) : this.getAllHistory();
        
        // 排序 - 最新的排在前面
        history.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        // 创建对话框
        const dialog = document.createElement('div');
        dialog.className = 'chat-history-dialog';
        dialog.style.position = 'fixed';
        dialog.style.top = '50%';
        dialog.style.left = '50%';
        dialog.style.transform = 'translate(-50%, -50%)';
        dialog.style.backgroundColor = '#fff';
        dialog.style.padding = '20px';
        dialog.style.borderRadius = '12px';
        dialog.style.boxShadow = '0 0 20px rgba(0,0,0,0.2)';
        dialog.style.zIndex = '1000';
        dialog.style.width = '90%';
        dialog.style.maxWidth = '800px';
        dialog.style.maxHeight = '85vh';
        dialog.style.overflow = 'hidden';
        dialog.style.display = 'flex';
        dialog.style.flexDirection = 'column';
        
        // 创建对话框标题
        const title = document.createElement('h3');
        title.textContent = stockCode ? `${this.getStockName(stockCode)} (${stockCode}) 的量化记录` : '所有量化记录';
        title.style.margin = '0 0 15px 0';
        title.style.borderBottom = '1px solid #eee';
        title.style.paddingBottom = '10px';
        dialog.appendChild(title);
        
        // 创建历史记录容器
        const historyContainer = document.createElement('div');
        historyContainer.style.overflowY = 'auto';
        historyContainer.style.maxHeight = 'calc(85vh - 120px)';
        historyContainer.style.paddingRight = '10px';
        historyContainer.style.display = 'flex';
        historyContainer.style.flexDirection = 'column';
        historyContainer.style.alignItems = 'center';
        historyContainer.style.width = '100%';
        
        if (history.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'empty-message';
            emptyMessage.innerHTML = '<i class="fas fa-info-circle"></i> 暂无量化记录';
            emptyMessage.style.textAlign = 'center';
            emptyMessage.style.padding = '20px';
            emptyMessage.style.color = '#666';
            emptyMessage.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
            emptyMessage.style.borderRadius = '8px';
            emptyMessage.style.width = '100%';
            historyContainer.appendChild(emptyMessage);
        } else {
            history.forEach(item => {
                const chatItem = document.createElement('div');
                chatItem.className = 'message-content';
                chatItem.style.backgroundColor = '#fff';
                chatItem.style.borderRadius = '10px';
                chatItem.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.08)';
                chatItem.style.padding = '15px';
                chatItem.style.margin = '10px 0';
                chatItem.style.position = 'relative';
                chatItem.style.width = '100%';
                chatItem.style.maxWidth = '800px';
                chatItem.style.boxSizing = 'border-box';
                
                // 创建时间戳
                const timestamp = document.createElement('div');
                timestamp.style.fontSize = '12px';
                timestamp.style.color = '#888';
                timestamp.style.marginBottom = '10px';
                timestamp.textContent = new Date(item.timestamp).toLocaleString('zh-CN');
                chatItem.appendChild(timestamp);
                
                // 创建用户问题
                const question = document.createElement('div');
                question.style.marginBottom = '10px';
                question.style.padding = '10px';
                question.style.backgroundColor = 'rgba(74, 144, 226, 0.1)';
                question.style.borderRadius = '6px';
                question.style.color = '#333';
                question.innerHTML = `<strong>问题：</strong>${item.userMessage}`;
                chatItem.appendChild(question);
                
                // 创建AI回复
                const answer = document.createElement('div');
                answer.style.padding = '10px';
                answer.style.backgroundColor = '#f9f9f9';
                answer.style.borderRadius = '6px';
                answer.style.color = '#333';
                answer.innerHTML = `<strong>量化分析：</strong><div class="markdown-content">${marked.parse(item.aiResponse)}</div>`;
                chatItem.appendChild(answer);
                
                // 创建操作按钮容器
                const actions = document.createElement('div');
                actions.style.position = 'absolute';
                actions.style.top = '12px';
                actions.style.right = '12px';
                
                // 删除按钮
                const deleteBtn = document.createElement('button');
                deleteBtn.innerHTML = '<i class="fas fa-trash-alt"></i>';
                deleteBtn.style.backgroundColor = 'transparent';
                deleteBtn.style.border = 'none';
                deleteBtn.style.color = '#ff4444';
                deleteBtn.style.cursor = 'pointer';
                deleteBtn.style.padding = '4px 8px';
                deleteBtn.style.fontSize = '14px';
                deleteBtn.title = '删除此记录';
                
                deleteBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (confirm('确定要删除这条量化记录吗？')) {
                        this.deleteHistory(item.id);
                        chatItem.remove();
                        
                        // 如果没有记录了，显示空消息
                        if (historyContainer.children.length === 0) {
                            const emptyMessage = document.createElement('div');
                            emptyMessage.className = 'empty-message';
                            emptyMessage.innerHTML = '<i class="fas fa-info-circle"></i> 暂无量化记录';
                            emptyMessage.style.textAlign = 'center';
                            emptyMessage.style.padding = '20px';
                            emptyMessage.style.color = '#666';
                            emptyMessage.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
                            emptyMessage.style.borderRadius = '8px';
                            emptyMessage.style.width = '100%';
                            historyContainer.appendChild(emptyMessage);
                        }
                    }
                });
                actions.appendChild(deleteBtn);
                chatItem.appendChild(actions);
                historyContainer.appendChild(chatItem);
            });
        }
        
        dialog.appendChild(historyContainer);
        
        // 创建按钮容器
        const buttonContainer = document.createElement('div');
        buttonContainer.style.display = 'flex';
        buttonContainer.style.justifyContent = 'space-between';
        buttonContainer.style.marginTop = '15px';
        buttonContainer.style.paddingTop = '10px';
        buttonContainer.style.borderTop = '1px solid #eee';
        
        // 清空所有按钮
        const clearAllBtn = document.createElement('button');
        clearAllBtn.innerHTML = '<i class="fas fa-trash-alt"></i> 量化记录最多保存三条,超出后会自动删除最早的记录';
        clearAllBtn.style.padding = '8px 15px';
        clearAllBtn.style.border = 'none';
        clearAllBtn.style.borderRadius = '4px';
        clearAllBtn.style.backgroundColor = '#ff4444';
        clearAllBtn.style.color = 'white';
        clearAllBtn.style.cursor = 'pointer';
        clearAllBtn.style.display = 'flex';
        clearAllBtn.style.alignItems = 'center';
        
        clearAllBtn.addEventListener('click', () => {
            if (confirm('此操作会删除**所有股票**的**所有量化记录**!确定要清空所有的量化记录吗？此操作不可撤销。')) {
                this.clearAllHistory();
                historyContainer.innerHTML = '';
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'empty-message';
                emptyMessage.innerHTML = '<i class="fas fa-info-circle"></i> 暂无量化记录';
                emptyMessage.style.textAlign = 'center';
                emptyMessage.style.padding = '20px';
                emptyMessage.style.color = '#666';
                emptyMessage.style.backgroundColor = 'rgba(0, 0, 0, 0.02)';
                emptyMessage.style.borderRadius = '8px';
                emptyMessage.style.width = '100%';
                historyContainer.appendChild(emptyMessage);
            }
        });
        buttonContainer.appendChild(clearAllBtn);
        
        // 关闭按钮
        const closeButton = document.createElement('button');
        closeButton.innerHTML = '<i class="fas fa-times"></i> 关闭';
        closeButton.style.padding = '8px 15px';
        closeButton.style.border = 'none';
        closeButton.style.borderRadius = '4px';
        closeButton.style.backgroundColor = '#999';
        closeButton.style.color = 'white';
        closeButton.style.cursor = 'pointer';
        closeButton.style.display = 'flex';
        closeButton.style.alignItems = 'center';
        closeButton.style.gap = '5px';
        
        closeButton.addEventListener('click', () => {
            overlay.remove();
            dialog.remove();
        });
        buttonContainer.appendChild(closeButton);
        
        dialog.appendChild(buttonContainer);
        
        // 创建半透明背景
        const overlay = document.createElement('div');
        overlay.style.position = 'fixed';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
        overlay.style.zIndex = '999';
        
        overlay.addEventListener('click', () => {
            overlay.remove();
            dialog.remove();
        });
        
        // 阻止对话框点击事件冒泡
        dialog.addEventListener('click', (e) => {
            e.stopPropagation();
        });
        
        // 添加到页面
        document.body.appendChild(overlay);
        document.body.appendChild(dialog);
    },
    
    // 获取股票名称的辅助方法
    getStockName: function(stockCode) {
        // 尝试从本地存储的股票列表中获取
        const stocks = FrontendStocks.getAllStocks();
        const stock = stocks.find(s => s.code === stockCode);
        return stock ? stock.name : stockCode;
    }
};

// 导出到全局对象
window.ChatHistory = ChatHistory;

// 定义一个新的自动触发高评分刷新的函数
function autoTriggerHighScoreRefresh(silentMode = false, forceRefresh = true) {
    // 延迟执行，确保评分提取完成
    setTimeout(() => {
        console.log('自动执行高评分股票提取' + (silentMode ? ' (静默模式)' : '') + (forceRefresh ? ' (强制刷新)' : ''));
        
        if (forceRefresh && FrontendStocks && typeof FrontendStocks.fetchHighScoreStocksFromServer === 'function') {
            console.log('强制从服务器刷新高评分股票数据');
            FrontendStocks.fetchHighScoreStocksFromServer();
        }
        
        // 直接点击刷新按钮
        const btn = document.getElementById('extractScoreBtn');
        if (btn) {
            console.log('模拟点击高评分刷新按钮');
            btn.click();
        }
        
        // 如果是静默模式，我们保存原始的showNotification函数并临时替换成静默版本
        if (silentMode && typeof window.showNotification === 'function') {
            const originalShowNotification = window.showNotification;
            window.showNotification = function(message, type) {
                // 只记录日志，不显示通知
                console.log(`[静默通知] ${message} (${type})`);
            };
            
            // 还原原始的showNotification函数
            setTimeout(() => {
                window.showNotification = originalShowNotification;
            }, 1000);
        }
    }, 500);
}

// 将自身导出到全局对象，使其他模块可以访问
window.autoTriggerHighScoreRefresh = autoTriggerHighScoreRefresh;
