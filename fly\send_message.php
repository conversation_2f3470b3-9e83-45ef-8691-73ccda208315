<?php
// 先加载配置文件
require_once dirname(__FILE__) . '/includes/config.php';
require_once dirname(__FILE__) . '/includes/functions.php';

// 检查是否已登录为管理员
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

// 获取所有用户列表
$conn = getDbConnection();
$users = [];
$sql = "SELECT id, username FROM users ORDER BY username";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }
}

// 处理发送消息请求
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 处理删除消息请求
    if (isset($_POST['delete_messages'])) {
        $messageIds = json_decode($_POST['delete_messages'], true);
        
        if (is_array($messageIds) && !empty($messageIds)) {
            $conn = getDbConnection();
            $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
            $sql = "DELETE FROM messages WHERE id IN ($placeholders)";
            
            $stmt = $conn->prepare($sql);
            $types = str_repeat('i', count($messageIds));
            $stmt->bind_param($types, ...$messageIds);
            
            if ($stmt->execute()) {
                $affectedRows = $stmt->affected_rows;
                $message = "成功删除 {$affectedRows} 条消息";
            } else {
                $message = "删除消息失败: " . $conn->error;
            }
            
            $stmt->close();
        } else {
            $message = "无效的消息ID";
        }
    }
    // 处理发送消息请求
    elseif (isset($_POST['send'])) {
        $recipientJson = $_POST['recipient'] ?? '';
        $content = $_POST['content'] ?? '';
        
        if (empty($content)) {
            $message = '消息内容不能为空';
        } elseif (empty($recipientJson)) {
            $message = '请选择至少一个接收者';
        } else {
            // 解析接收者JSON
            $recipients = json_decode($recipientJson, true);
            if (!is_array($recipients) || empty($recipients)) {
                $message = '接收者格式错误';
            } else {
                // 检查消息表是否存在
                $tableCheckQuery = "SHOW TABLES LIKE 'messages'";
                $tableCheckResult = $conn->query($tableCheckQuery);
                
                if ($tableCheckResult->num_rows == 0) {
                    // 创建消息表
                    $createTableQuery = "
                    CREATE TABLE IF NOT EXISTS `messages` (
                      `id` int(11) NOT NULL AUTO_INCREMENT,
                      `user_id` int(11) DEFAULT NULL,
                      `username` varchar(50) DEFAULT NULL,
                      `content` text NOT NULL,
                      `is_read` tinyint(1) NOT NULL DEFAULT '0',
                      `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
                      PRIMARY KEY (`id`),
                      KEY `user_id` (`user_id`),
                      KEY `username` (`username`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    ";
                    
                    if (!$conn->query($createTableQuery)) {
                        $message = '创建消息表失败: ' . $conn->error;
                    }
                }
                
                if (empty($message)) {
                    $successCount = 0;
                    $failCount = 0;
                    
                    foreach ($recipients as $recipient) {
                        // 检查接收者格式 (ID:xxx 或 用户名)
                        if (strpos($recipient, 'ID:') === 0) {
                            $user_id = intval(substr($recipient, 3));
                            $sql = "INSERT INTO messages (user_id, content) VALUES (?, ?)";
                            $stmt = $conn->prepare($sql);
                            $stmt->bind_param('is', $user_id, $content);
                        } else {
                            $sql = "INSERT INTO messages (username, content) VALUES (?, ?)";
                            $stmt = $conn->prepare($sql);
                            $stmt->bind_param('ss', $recipient, $content);
                        }
                        
                        if ($stmt->execute()) {
                            $successCount++;
                        } else {
                            $failCount++;
                        }
                        $stmt->close();
                    }
                    
                    if ($successCount > 0) {
                        $message = "成功发送 {$successCount} 条消息";
                        if ($failCount > 0) {
                            $message .= "，{$failCount} 条消息发送失败";
                        }
                    } else {
                        $message = '所有消息发送失败';
                    }
                }
            }
        }
    }
}

// 获取消息历史
$messages = [];
$sql = "SELECT m.*, u.username as user_username 
        FROM messages m 
        LEFT JOIN users u ON m.user_id = u.id 
        ORDER BY m.created_at DESC 
        LIMIT 50";
$result = $conn->query($sql);
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $messages[] = $row;
    }
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发送消息 - 管理后台</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            box-sizing: border-box;
        }
        textarea {
            height: 150px;
            resize: vertical;
        }
        button {
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #3a75c4;
        }
        .message {
            background-color: #e8f4f8;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .nav-links {
            margin-bottom: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin-right: 15px;
            color: #4a90e2;
            text-decoration: none;
        }
        .nav-links a:hover {
            text-decoration: underline;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        table th {
            background-color: #f0f0f0;
        }
        .message-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .read-status {
            text-align: center;
        }
        .read {
            color: #28a745;
        }
        .unread {
            color: #dc3545;
        }
        .recipient-input {
            display: block;
        }
        .recipient-input select {
            width: 100%;
            margin-bottom: 10px;
        }
        #selected-recipients {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            margin-top: 10px;
            max-height: 150px;
            overflow-y: auto;
        }
        #selected-recipients div {
            padding: 4px 8px;
            margin: 3px 0;
            background-color: #e9f2fd;
            border-radius: 3px;
            display: inline-block;
            margin-right: 5px;
        }
        #select-all-btn, #clear-all-btn {
            padding: 5px 10px;
            font-size: 14px;
            margin-right: 10px;
        }
        #select-all-btn {
            background-color: #28a745;
        }
        #select-all-btn:hover {
            background-color: #218838;
        }
        #clear-all-btn {
            background-color: #dc3545;
        }
        #clear-all-btn:hover {
            background-color: #c82333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="nav-links">
            <a href="dashboard.php">返回仪表盘</a>
            <a href="export_users.php">用户管理</a>
        </div>
        
        <h1>发送消息</h1>
        
        <?php if (!empty($message)): ?>
            <div class="message <?php echo strpos($message, '成功') !== false ? 'success' : 'error'; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <form method="POST" action="">
            <div class="form-group">
                <label for="recipient">选择接收者 (可多选)</label>
                <div class="recipient-input">
                    <select id="username-select" multiple size="8" style="width: 100%; margin-bottom: 10px;">
                        <?php foreach ($users as $user): ?>
                            <option value="<?php echo htmlspecialchars($user['username']); ?>" 
                                    data-id="<?php echo $user['id']; ?>">
                                <?php echo htmlspecialchars($user['username']); ?> (ID:<?php echo $user['id']; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div style="margin-bottom: 10px;">
                        <button type="button" id="select-all-btn" onclick="selectAll()">全选</button>
                        <button type="button" id="clear-all-btn" onclick="clearAll()">清除选择</button>
                    </div>
                    <input type="hidden" id="recipient-input" name="recipient" value="">
                    <div id="selected-recipients" style="margin-top: 10px; padding: 5px; border: 1px solid #ddd; min-height: 50px; max-height: 150px; overflow-y: auto;"></div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="content">消息内容</label>
                <textarea id="content" name="content" required></textarea>
            </div>
            
            <button type="submit" name="send">发送消息</button>
        </form>
        
        <h2>最近消息记录</h2>
        <div style="margin-bottom: 10px;">
            <button type="button" id="delete-selected-btn" onclick="deleteSelected()" style="background-color: #dc3545;">删除选中消息</button>
        </div>
        <table>
            <thead>
                <tr>
                    <th width="40"><input type="checkbox" id="select-all-messages" onclick="toggleAllMessages()"></th>
                    <th>ID</th>
                    <th>接收者</th>
                    <th>内容</th>
                    <th>状态</th>
                    <th>发送时间</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($messages)): ?>
                    <tr>
                        <td colspan="6" style="text-align: center;">暂无消息记录</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($messages as $msg): ?>
                        <tr>
                            <td><input type="checkbox" class="message-checkbox" value="<?php echo $msg['id']; ?>"></td>
                            <td><?php echo $msg['id']; ?></td>
                            <td>
                                <?php 
                                if (!empty($msg['user_username'])) {
                                    echo htmlspecialchars($msg['user_username']) . ' (ID:' . $msg['user_id'] . ')';
                                } elseif (!empty($msg['username'])) {
                                    echo htmlspecialchars($msg['username']);
                                } else {
                                    echo '未知用户';
                                }
                                ?>
                            </td>
                            <td class="message-content" title="<?php echo htmlspecialchars($msg['content']); ?>">
                                <?php echo htmlspecialchars($msg['content']); ?>
                            </td>
                            <td class="read-status <?php echo $msg['is_read'] ? 'read' : 'unread'; ?>">
                                <?php echo $msg['is_read'] ? '已读' : '未读'; ?>
                            </td>
                            <td><?php echo $msg['created_at']; ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <script>
        function updateSelectedRecipients() {
            const select = document.getElementById('username-select');
            const hiddenInput = document.getElementById('recipient-input');
            const displayDiv = document.getElementById('selected-recipients');
            
            let selected = [];
            let displayHtml = '';
            
            for (let i = 0; i < select.options.length; i++) {
                if (select.options[i].selected) {
                    const username = select.options[i].value;
                    const userId = select.options[i].getAttribute('data-id');
                    selected.push(username);
                    displayHtml += `<div>${username} (ID:${userId})</div>`;
                }
            }
            
            hiddenInput.value = JSON.stringify(selected);
            displayDiv.innerHTML = displayHtml || '<div style="color: #999;">未选择任何接收者</div>';
        }
        
        function selectAll() {
            const select = document.getElementById('username-select');
            for (let i = 0; i < select.options.length; i++) {
                select.options[i].selected = true;
            }
            updateSelectedRecipients();
        }
        
        function clearAll() {
            const select = document.getElementById('username-select');
            for (let i = 0; i < select.options.length; i++) {
                select.options[i].selected = false;
            }
            updateSelectedRecipients();
        }
        
        // 初始化
        document.getElementById('username-select').addEventListener('change', updateSelectedRecipients);
        updateSelectedRecipients();

        // 消息全选/取消全选
        function toggleAllMessages() {
            const selectAll = document.getElementById('select-all-messages');
            const checkboxes = document.getElementsByClassName('message-checkbox');
            
            for (let i = 0; i < checkboxes.length; i++) {
                checkboxes[i].checked = selectAll.checked;
            }
        }
        
        // 删除选中的消息
        function deleteSelected() {
            const checkboxes = document.getElementsByClassName('message-checkbox');
            const selectedIds = [];
            
            for (let i = 0; i < checkboxes.length; i++) {
                if (checkboxes[i].checked) {
                    selectedIds.push(checkboxes[i].value);
                }
            }
            
            if (selectedIds.length === 0) {
                alert('请至少选择一条消息');
                return;
            }
            
            if (confirm('确定要删除选中的 ' + selectedIds.length + ' 条消息吗？')) {
                // 创建一个表单并提交
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '';
                
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'delete_messages';
                input.value = JSON.stringify(selectedIds);
                
                form.appendChild(input);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
</body>
</html> 