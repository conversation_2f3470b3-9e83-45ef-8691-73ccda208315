<?php
// 不再需要session_start()，因为config.php已经处理了会话

/**
 * 连接数据库
 */
function connectDB() {
    $host = 'localhost';
    $user = 'agpt';
    $password = 'hunterl6628096';
    $database = 'agpt';
    
    $db = new mysqli($host, $user, $password, $database);
    
    if ($db->connect_error) {
        die('数据库连接失败: ' . $db->connect_error);
    }
    
    // 设置字符集
    $db->set_charset('utf8mb4');
    
    return $db;
}

/**
 * 检查用户是否已登录
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * 获取当前登录用户信息
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    $db = connectDB();
    $userId = $_SESSION['user_id'];
    
    $stmt = $db->prepare("SELECT id, username, api_key, remaining_requests FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        return null;
    }
    
    return $result->fetch_assoc();
}

/**
 * 获取系统公告
 */
function getAnnouncements() {
    try {
        // 检查是否存在gonggao.json文件
        if (file_exists('gonggao.json')) {
            $announcementsData = file_get_contents('gonggao.json');
            $announcements = json_decode($announcementsData, true);
            
            if (json_last_error() === JSON_ERROR_NONE && is_array($announcements)) {
                return $announcements;
            }
        }
        
        // 如果没有找到文件或解析失败，从数据库获取
        $db = connectDB();
        $stmt = $db->prepare("SELECT id, title, content, created_at FROM announcements ORDER BY created_at DESC LIMIT 10");
        $stmt->execute();
        $result = $stmt->get_result();
        
        $announcements = [];
        while ($row = $result->fetch_assoc()) {
            $announcements[] = $row;
        }
        
        return $announcements;
    } catch (Exception $e) {
        return [];
    }
}

/**
 * 获取用户股票列表
 */
function getUserStocks($userId = null) {
    // 如果没有提供用户ID参数，使用会话中的用户ID
    if ($userId === null) {
        if (!isLoggedIn()) {
            return [];
        }
        $userId = $_SESSION['user_id'];
    }
    
    try {
        $db = connectDB();
        
        // 记录执行的查询语句
        error_log("获取用户股票: 用户ID=$userId");
        
        // 从user_stocks和stocks联表查询获取更多信息
        $sql = "SELECT us.user_id, us.stock_id as code, s.name, us.added_at 
                FROM user_stocks us 
                LEFT JOIN stocks s ON us.stock_id = s.id
                WHERE us.user_id = ?";
        
        error_log("SQL查询: $sql");
        
        $stmt = $db->prepare($sql);
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $stocks = [];
        while ($row = $result->fetch_assoc()) {
            // 记录获取到的股票信息
            error_log("获取到股票: " . json_encode($row));
            
            // 检查股票名称是否为null
            if (empty($row['name'])) {
                // 如果名称为空，尝试通过API获取
                if (function_exists('getStockName')) {
                    $row['name'] = getStockName($row['code']) ?: $row['code'];
                } else {
                    $row['name'] = $row['code']; // 默认使用股票代码作为名称
                }
            }
            
            $stocks[] = $row;
        }
        
        error_log("获取到用户股票: " . json_encode($stocks));
        return $stocks;
    } catch (Exception $e) {
        error_log("获取用户股票错误: " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString());
        return [];
    }
}

/**
 * 获取API许可证
 */
function getLicences() {
    // 检查是否存在licence.json文件（相对路径）
    $relativePath = 'licence.json';
    $absolutePath = __DIR__ . '/../licence.json';
    
    // 记录查找许可证文件的尝试
    error_log("尝试获取许可证文件: 相对路径={$relativePath}, 绝对路径={$absolutePath}");
    
    if (file_exists($absolutePath)) {
        error_log("找到许可证文件: {$absolutePath}");
        $licencesData = file_get_contents($absolutePath);
        $licences = json_decode($licencesData, true);
        
        if (json_last_error() === JSON_ERROR_NONE && is_array($licences)) {
            error_log("成功解析许可证: " . count($licences) . "个许可证");
            return $licences;
        } else {
            error_log("许可证解析失败: " . json_last_error_msg());
        }
    } else if (file_exists($relativePath)) {
        error_log("找到许可证文件(相对路径): {$relativePath}");
        $licencesData = file_get_contents($relativePath);
        $licences = json_decode($licencesData, true);
        
        if (json_last_error() === JSON_ERROR_NONE && is_array($licences)) {
            error_log("成功解析许可证(相对路径): " . count($licences) . "个许可证");
            return $licences;
        } else {
            error_log("许可证解析失败(相对路径): " . json_last_error_msg());
        }
    } else {
        error_log("未找到许可证文件");
    }
    
    // 如果没有找到文件或解析失败，返回空数组
    return [];
}

/**
 * 随机获取一个许可证
 */
function getRandomLicence() {
    $licences = getLicences();
    
    if (empty($licences)) {
        return null;
    }
    
    // 随机选择一个许可证
    $randomIndex = array_rand($licences);
    return $licences[$randomIndex];
}

/**
 * 安全过滤输入
 */
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * 加密密码
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * 验证密码
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * 重定向到指定URL
 */
function redirect($url) {
    header("Location: $url");
    exit;
}

/**
 * 设置错误消息
 */
function setError($message) {
    $_SESSION['error'] = $message;
}

/**
 * 设置成功消息
 */
function setSuccess($message) {
    $_SESSION['success'] = $message;
}

/**
 * 获取并清除错误消息
 */
function getError() {
    $error = $_SESSION['error'] ?? '';
    unset($_SESSION['error']);
    return $error;
}

/**
 * 获取并清除成功消息
 */
function getSuccess() {
    $success = $_SESSION['success'] ?? '';
    unset($_SESSION['success']);
    return $success;
}

// 注册新用户
function registerUser($username, $password) {
    try {
        $db = connectDB();
        
        // 检查用户名是否已存在
        $stmt = $db->prepare("SELECT id FROM users WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            return ['success' => false, 'message' => '用户名已存在'];
        }
        
        // 密码加密
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // 插入新用户
        $stmt = $db->prepare("INSERT INTO users (username, password_hash) VALUES (?, ?)");
        $stmt->bind_param("ss", $username, $hashedPassword);
        
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            return ['success' => true, 'message' => '注册成功'];
        } else {
            return ['success' => false, 'message' => '注册失败，请稍后再试'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => '系统错误：' . $e->getMessage()];
    }
}

// 用户登录
function loginUser($username, $password) {
    try {
        $db = connectDB();
        
        $stmt = $db->prepare("SELECT id, username, password_hash FROM users WHERE username = ?");
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 1) {
            $user = $result->fetch_assoc();
            
            if (password_verify($password, $user['password_hash'])) {
                // 获取用户IP地址
                $ip = '';
                if (isset($_SERVER['HTTP_CLIENT_IP'])) {
                    $ip = $_SERVER['HTTP_CLIENT_IP'];
                } elseif (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
                    $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
                } elseif (isset($_SERVER['REMOTE_ADDR'])) {
                    $ip = $_SERVER['REMOTE_ADDR'];
                }
                
                // 更新用户IP地址
                $updateStmt = $db->prepare("UPDATE users SET register_ip = ? WHERE id = ?");
                $updateStmt->bind_param("si", $ip, $user['id']);
                $updateStmt->execute();
                $updateStmt->close();
                
                // 登录成功，设置会话
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                
                return ['success' => true, 'message' => '登录成功'];
            }
        }
        
        return ['success' => false, 'message' => '用户名或密码错误'];
    } catch (Exception $e) {
        return ['success' => false, 'message' => '系统错误：' . $e->getMessage()];
    }
}

// 更新API密钥
function updateApiKey($userId, $apiKey) {
    try {
        $db = connectDB();
        $stmt = $db->prepare("UPDATE users SET api_key = ? WHERE id = ?");
        $stmt->bind_param("si", $apiKey, $userId);
        $stmt->execute();
        return $stmt->affected_rows >= 0;
    } catch (Exception $e) {
        return false;
    }
}

// 从数据库获取用户许可证
function getUserLicences($userId) {
    try {
        $db = connectDB();
        $stmt = $db->prepare("SELECT id, licence_key FROM licences WHERE user_id = ?");
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $licences = [];
        while ($row = $result->fetch_assoc()) {
            $licences[] = $row;
        }
        
        return $licences;
    } catch (Exception $e) {
        return [];
    }
}

// 添加许可证
function addLicence($userId, $licenceKey) {
    try {
        $db = connectDB();
        
        // 检查许可证是否已存在
        $stmt = $db->prepare("SELECT id FROM licences WHERE user_id = ? AND licence_key = ?");
        $stmt->bind_param("is", $userId, $licenceKey);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            return false; // 许可证已存在
        }
        
        // 添加许可证
        $stmt = $db->prepare("INSERT INTO licences (user_id, licence_key) VALUES (?, ?)");
        $stmt->bind_param("is", $userId, $licenceKey);
        $stmt->execute();
        
        return $stmt->affected_rows > 0;
    } catch (Exception $e) {
        return false;
    }
}

// 删除许可证
function deleteLicence($id, $userId) {
    try {
        $db = connectDB();
        $stmt = $db->prepare("DELETE FROM licences WHERE id = ? AND user_id = ?");
        $stmt->bind_param("ii", $id, $userId);
        $stmt->execute();
        
        return $stmt->affected_rows > 0;
    } catch (Exception $e) {
        return false;
    }
}

// 添加股票到用户列表
function addStockToUser($userId, $stockCode, $stockName) {
    try {
        $db = connectDB();
        
        // 检查是否已添加
        $stmt = $db->prepare("SELECT id FROM user_stocks WHERE user_id = ? AND stock_symbol = ?");
        $stmt->bind_param("is", $userId, $stockCode);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            return ['success' => false, 'message' => '该股票已在您的列表中'];
        }
        
        // 添加股票
        $stmt = $db->prepare("INSERT INTO user_stocks (user_id, stock_symbol, stock_name) VALUES (?, ?, ?)");
        $stmt->bind_param("iss", $userId, $stockCode, $stockName);
        $stmt->execute();
        
        if ($stmt->affected_rows > 0) {
            return ['success' => true, 'message' => '股票添加成功'];
        } else {
            return ['success' => false, 'message' => '股票添加失败'];
        }
    } catch (Exception $e) {
        return ['success' => false, 'message' => '系统错误：' . $e->getMessage()];
    }
}

/**
 * 删除用户的股票
 */
function removeStockFromUser($userId, $stockId) {
    try {
        $db = connectDB();
        
        $stmt = $db->prepare("DELETE FROM user_stocks WHERE id = ? AND user_id = ?");
        $stmt->bind_param("ii", $stockId, $userId);
        $stmt->execute();
        
        if ($stmt->affected_rows > 0) {
            return ['success' => true, 'message' => '股票删除成功'];
        } else {
            return ['success' => false, 'message' => '股票删除失败'];
        }
    } catch (Exception $e) {
        error_log("删除股票错误: " . $e->getMessage());
        return ['success' => false, 'message' => '系统错误：' . $e->getMessage()];
    }
}

/**
 * 获取股票名称
 * 先尝试缓存，再从API获取
 */
function getStockName($stockCode) {
    $stockName = '未知名称';
    $cacheFile = "../cache/stock_names.json";
    
    // 首先尝试从缓存读取
    if (file_exists($cacheFile)) {
        $cachedNames = file_get_contents($cacheFile);
        $nameData = json_decode($cachedNames, true);
        if (is_array($nameData)) {
            foreach ($nameData as $stock) {
                // 兼容两种格式：dm/mc和code/name
                if ((isset($stock['dm']) && $stock['dm'] === $stockCode) || 
                    (isset($stock['code']) && $stock['code'] === $stockCode)) {
                    return isset($stock['mc']) ? $stock['mc'] : $stock['name']; // 优先使用mc字段，其次name字段
                }
            }
        }
    }
    
    // 如果缓存中没有，尝试从API获取
    $licences = getLicences();
    if (empty($licences)) {
        return $stockName;
    }
    
    $randomLicence = $licences[array_rand($licences)];
    $STOCK_NAME_API = "http://localhost:8888/api/public/stock_info_a_code_name";
    $nameApiUrl = $STOCK_NAME_API;
    
    error_log("请求股票名称API: " . $nameApiUrl);
    
    // 初始化CURL获取名称列表
    $nameCh = curl_init();
    curl_setopt($nameCh, CURLOPT_URL, $nameApiUrl);
    curl_setopt($nameCh, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($nameCh, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($nameCh, CURLOPT_TIMEOUT, 15);
    curl_setopt($nameCh, CURLOPT_HTTPHEADER, [
        'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    ]);
    
    // 执行请求
    $nameResponse = curl_exec($nameCh);
    $nameHttpCode = curl_getinfo($nameCh, CURLINFO_HTTP_CODE);
    curl_close($nameCh);
    
    if ($nameHttpCode == 200 && !empty($nameResponse)) {
        // 确保缓存目录存在
        if (!file_exists("../cache")) {
            mkdir("../cache", 0755, true);
        }
        
        // 保存原始数据到缓存
        file_put_contents($cacheFile, $nameResponse);
        error_log("股票名称列表已缓存到: " . $cacheFile);
        
        // 解析名称数据
        $nameData = json_decode($nameResponse, true);
        if (is_array($nameData)) {
            // 查找匹配的股票代码
            foreach ($nameData as $stock) {
                if (isset($stock['code']) && $stock['code'] === $stockCode) {
                    $stockName = $stock['name']; // 使用name字段作为股票名称
                    error_log("通过名称API找到股票名称: " . $stockName);
                    break;
                }
            }
        }
    }
    
    return $stockName;
}

function getStockInfo($code) {
    error_log("获取股票信息: $code");
    
    if (empty($code)) {
        return [
            'name' => '未知股票',
            'price' => '0.00',
            'price_change' => '0.00'
        ];
    }
    
    $apiUrl = "https://api.doctorxiong.club/v1/stock?code=" . urlencode($code);
    error_log("请求API: $apiUrl");
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    $response = curl_exec($ch);
    
    if(curl_errno($ch)) {
        error_log("cURL错误: " . curl_error($ch));
        curl_close($ch);
        return [
            'name' => '无法获取',
            'price' => '0.00',
            'price_change' => '0.00'
        ];
    }
    
    curl_close($ch);
    error_log("API响应: $response");
    
    $data = json_decode($response, true);
    
    if ($data && isset($data['code']) && $data['code'] == 200 && isset($data['data']) && !empty($data['data'])) {
        $stockData = $data['data'][0];
        error_log("股票数据: " . json_encode($stockData, JSON_UNESCAPED_UNICODE));
        
        // 记录所有股票数据字段
        foreach ($stockData as $key => $value) {
            error_log("股票字段: $key = $value");
        }
        
        // 提取需要的数据
        $result = [
            'name' => $stockData['name'] ?? '未知',
            'price' => $stockData['price'] ?? ($stockData['current'] ?? '0.00'),
            'price_change' => $stockData['priceChange'] ?? ($stockData['change'] ?? '0.00'),
            'id' => $stockData['code'] ?? $code,
        ];
        
        return $result;
    }
    
    error_log("无法解析API响应或未找到股票数据");
    return [
        'name' => '未找到',
        'price' => '0.00',
        'price_change' => '0.00'
    ];
}