<?php
require_once '../includes/functions.php';

// 检查是否已登录
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

// 检查是否有查询参数
if (!isset($_GET['code']) || empty($_GET['code'])) {
    echo json_encode(['success' => false, 'message' => '请提供股票代码']);
    exit;
}

$stockCode = sanitizeInput($_GET['code']);

// 模拟获取股票价格
// 在实际应用中，应该连接到股票API获取实时数据
function getStockPriceData($code) {
    // 生成随机价格数据
    $basePrice = 10 + (floatval(substr($code, -2)) / 10);
    $changePercent = (mt_rand(-500, 500) / 100);
    $price = round($basePrice * (1 + $changePercent / 100), 2);
    
    return [
        'code' => $code,
        'price' => $price,
        'change' => round($price - $basePrice, 2),
        'change_percent' => $changePercent
    ];
}

// 获取股票价格数据
$priceData = getStockPriceData($stockCode);

// 返回结果
echo json_encode(['success' => true, 'data' => $priceData]);
exit;
?> 