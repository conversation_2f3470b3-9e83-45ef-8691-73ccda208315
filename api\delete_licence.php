<?php
require_once '../includes/functions.php';

// 检查是否已登录
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

// 检查是否有POST数据
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '无效的请求方法']);
    exit;
}

// 获取许可证ID
$id = isset($_POST['id']) ? intval($_POST['id']) : 0;

if ($id <= 0) {
    echo json_encode(['success' => false, 'message' => '无效的许可证ID']);
    exit;
}

// 删除许可证
$result = deleteLicence($id, $_SESSION['user_id']);

// 返回结果
echo json_encode($result);
exit;
?> 