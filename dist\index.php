<?php
// 先加载配置文件，它会处理会话启动
require_once 'includes/config.php';
require_once 'includes/functions.php';

// 检查是否已登录，如果已登录则重定向到仪表盘
if (isLoggedIn()) {
    header('Location: dashboard.php');
    exit;
}

// 处理登录表单提交
$error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = isset($_POST['username']) ? sanitizeInput($_POST['username']) : '';
    $password = isset($_POST['password']) ? $_POST['password'] : '';
    
    if (empty($username)) {
        $error = '请输入用户名';
    } elseif (empty($password)) {
        $error = '请输入密码';
    } else {
        try {
            // 连接数据库
            $db = connectDB();
            
            // 查询用户
            $stmt = $db->prepare("SELECT id, username, password_hash FROM users WHERE username = ?");
            $stmt->bind_param("s", $username);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows === 1) {
                $user = $result->fetch_assoc();
                
                // 验证密码
                if (password_verify($password, $user['password_hash'])) {
                    // 登录成功，设置会话
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    
                    // 重定向到仪表盘
                    header('Location: dashboard.php');
                    exit;
                } else {
                    $error = '用户名或密码错误';
                }
            } else {
                $error = '用户名或密码错误';
            }
        } catch (Exception $e) {
            $error = '登录失败，请稍后再试';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - <?php echo WEBSITE_NAME; ?></title>
    <link rel="icon" href="<?php echo WEBSITE_LOGO; ?>" type="image/png">
    <!-- 内联反调试代码 -->
    <script>
    (function() {
        // 提前标记已加载，防止重复
        window.__antiDebugLoaded = true;
        
        // 初始设置 - 非本地环境启用保护
        var isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
        if(!isProduction) return;
        
        // 记录原始console方法
        var _console = {
            log: console.log,
            warn: console.warn,
            error: console.error,
            info: console.info,
            debug: console.debug
        };
        
        // 立即禁用控制台 - 早期保护
        console.log = console.warn = console.error = console.info = console.debug = function(){};
        
        // 禁用F12和其他开发工具快捷键
        document.addEventListener('keydown', function(e) {
            if(e.key === 'F12' || 
              (e.ctrlKey && e.shiftKey && e.key === 'I') || 
              (e.ctrlKey && e.shiftKey && e.key === 'J') ||
              (e.ctrlKey && e.key === 'U')) {
                e.preventDefault();
                return false;
            }
        }, true);
        
        // 检测是否已打开开发工具
        function isDevToolsOpened() {
            var threshold = 160;
            var widthThreshold = 160;
            var heightThreshold = 160;
            
            // 检测大小差异
            var widthDiff = window.outerWidth - window.innerWidth > widthThreshold;
            var heightDiff = window.outerHeight - window.innerHeight > heightThreshold;
            
            return widthDiff || heightDiff;
        }
        
        // 周期性检查 - 使用非阻塞方式
        var checkInterval;
        
        function startChecking() {
            if(!checkInterval) {
                checkInterval = setInterval(function() {
                    if(isDevToolsOpened()) {
                        // 检测到开发工具，但不重定向，只禁用功能
                        disableDevTools();
                    }
                }, 1000);
            }
        }
        
        // 禁用开发工具而不是重定向
        function disableDevTools() {
            // 覆盖控制台对象，而不是重定向
            Object.defineProperty(window, 'console', {
                get: function() {
                    return {
                        log: function(){},
                        warn: function(){},
                        error: function(){},
                        info: function(){},
                        debug: function(){}
                    };
                },
                set: function(){}
            });
            
            // 可以添加其他措施，但不影响核心功能
        }
        
        // 页面加载后启动检查
        if(document.readyState === 'complete') {
            startChecking();
        } else {
            window.addEventListener('load', startChecking);
        }
    })();
    </script>
    <!-- 添加反调试脚本 -->
    <script src="js/anti-debug.js"></script>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/mobile.css">
</head>
<body class="auth-page">
    <div class="container">
        <div class="auth-wrapper">
            <div class="auth-features">
                <h1>π弈-专业个股量化平台</h1>
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-icon">📈</div>
                        <h3>全维度数据融合</h3>
                        <p>多模型配合+多源数据接入,非结构化解析:财务指标300+字段,产业链/舆情/龙虎榜</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📊</div>
                        <h3>智能模型矩阵</h3>
                        <p>自适应参数优化MACD/KDJ解决传统指标滞后性问题.主力资金拆解算法识别隐形大单与筹码异动</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">🤖</div>
                        <h3>Agent决策矩阵</h3>
                        <p>技术+事件量化驱动走势预测,政策-行业-个股传导链分析.风险控制模块根据波动率动态仓位调整</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">⭐</div>
                        <h3>专注个股</h3>
                        <p>个性化投资组合管理,快速提取关键信号.通过龙虎榜席位资金匹配算法，预警"涨停敢死队"介入标的准确率82.6%</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">📅</div>
                        <h3>动态决策输出</h3>
                        <p>科学策略建议,避免情绪化交易.动态权重评分模型,缩短80%的标的筛选时间,自动计算止损位</p>
                    </div>
                    <div class="feature-item">
                        <div class="feature-icon">💡</div>
                        <h3>Deepseek评价</h3>
                        <p>即使不追求全自动量化，该平台已具备显著优势,信息整合效率比人工复盘快10倍以上</p>
                    </div>
                </div>
            </div>

            <div class="login-container">
                <div class="auth-logo">
                    <img src="<?php echo WEBSITE_LOGO; ?>" alt="Logo">
                </div>
                
                <h2 class="form-title">用户登录</h2>
                <p class="form-subtitle">专业的股票分析工具，助您做出明智的投资决策</p>
                
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger"><?php echo $error; ?></div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <input type="text" id="username" name="username" class="form-control" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">密码</label>
                        <input type="password" id="password" name="password" class="form-control" required>
                    </div>
                    
                    <button type="submit" class="submit-btn">登录</button>
                </form>
                
                <div class="form-footer">
                    还没有账号？<a href="register.php">立即注册</a>
                </div>
            </div>
        </div>
        
        <footer>
            <p>&copy; <?php echo date('Y'); ?> <?php echo WEBSITE_NAME; ?> - 专业的AI股票分析平台</p>
        </footer>
    </div>

    <!-- 存储模式 -->
    <section class="storage-mode-section mobile-hidden">
        <h3 class="section-title">存储模式</h3>
        <label class="toggle-switch">
            <input type="checkbox" id="storageMode" <?php echo isset($_COOKIE['frontend_storage']) && $_COOKIE['frontend_storage'] === 'true' ? 'checked' : ''; ?>>
            <span class="slider"></span>
        </label>
        <span class="storage-mode-text">使用前端存储</span>
        <div class="storage-desc">数据将完全存储在您的本地设备中，确保隐私安全。但请注意，更换设备或清除浏览器数据后需要重新添加您的股票信息。</div>
    </section>

    <!-- Agent 分析区域 -->
    <section class="chat-section mobile-hidden">
        <h3 class="section-title">Agent 解答区域</h3>
        <div id="chatContainer" class="chat-container"></div>
        <form id="chatForm" class="input-section">
            <input type="text" id="userInput" placeholder="输入您的问题...">
            <button type="submit" class="send-btn">发送</button>
        </form>
    </section>
    
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    
    <!-- KDJ计算指标 -->
    <script src="js/indicators.js"></script>
    
    <!-- 前端股票管理模块 -->
    <script src="js/frontend_stocks.js"></script>
    
    <script>
        // 检测是否为移动设备
        function isMobileDevice() {
            return (window.innerWidth <= 768);
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 如果是移动设备，隐藏特定区域
            if (isMobileDevice()) {
                const mobileHiddenElements = document.querySelectorAll('.mobile-hidden');
                mobileHiddenElements.forEach(function(element) {
                    element.style.display = 'none';
                });
            }
        });
    </script>
</body>
</html> 