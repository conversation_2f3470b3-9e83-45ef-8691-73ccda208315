# 防止目录列表
Options -Indexes

# 防止访问敏感文件
<FilesMatch "^(\.htaccess|\.gitignore|\.env|config\.php|error_log\.txt)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# 防止查看源代码
<Files ~ "\.(php|html?|js|css)$">
    Header set X-Content-Type-Options "nosniff"
</Files>

# 防止显示PHP错误
php_flag display_errors off
php_flag display_startup_errors off
php_flag html_errors off
php_flag log_errors on
php_value error_log error_log.txt

# 添加安全头
<IfModule mod_headers.c>
    Header set X-XSS-Protection "1; mode=block"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set X-Content-Type-Options "nosniff"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' https://cdnjs.cloudflare.com https://cdn.jsdelivr.net 'unsafe-inline'; style-src 'self' https://cdnjs.cloudflare.com 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://api.llingfei.com;"
</IfModule>

# 防止查看PHP源代码
<Files ~ "\.php$">
    SetHandler application/x-httpd-php
</Files>

# 设置默认字符集
AddDefaultCharset UTF-8 