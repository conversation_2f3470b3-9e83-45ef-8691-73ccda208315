# 管理后台消息系统使用说明

## 功能概述

本系统实现了从管理后台向指定用户发送消息的功能。发送的消息会在用户登录后自动弹出通知，用户点击"已读"后消息将不再弹出。消息会显示在用户的dashboard.php页面（根目录下的用户面板）而不是管理员页面。

## 文件结构

1. `fly/create_messages_table.php` - 创建消息表的脚本
2. `fly/send_message.php` - 管理员发送消息的页面
3. `fly/api/message_api.php` - 处理消息获取和标记已读的API
4. `fly/dashboard.php` - 管理员后台面板，添加了发送消息功能
5. `dashboard.php` - 用户前台页面，添加了消息通知和弹窗功能

## 使用方法

### 管理员操作

1. 登录管理后台 (fly/dashboard.php)
2. 有两种方式发送消息：
   - 点击任意用户行中的"发送消息"按钮，可以直接发送给该用户
   - 点击顶部导航栏的"发送消息"链接，进入独立发送页面
3. 输入消息内容并发送
4. 消息会显示在用户的dashboard页面，不会在管理员后台显示
5. 消息发送后会在管理员页面下方显示发送成功的提示

### 用户体验

1. 用户登录后，如有未读消息，右上角会弹出通知
2. 5秒后或点击通知，消息内容会以弹窗形式显示
3. 用户阅读后点击"已读"按钮，消息将被标记为已读
4. 已读消息不会再次弹出

## 技术实现

1. 消息存储在数据库的 `messages` 表中
2. 消息通过用户ID和用户名标识接收者
3. 前端使用JavaScript定时检查是否有新消息
4. 使用AJAX异步标记消息为已读
5. 弹窗和通知使用CSS动画提升用户体验
6. API同时支持用户页面和管理员页面访问，自动识别用户身份

## 注意事项

1. 消息系统依赖于 `fly/includes` 目录中的配置和函数
2. 确保API目录（fly/api）有正确的权限
3. 消息内容支持文本格式，但会进行HTML转义
4. 消息仅发送给特定用户，不支持全局广播
5. 用户dashboard.php页面已添加消息接收功能，不需要额外修改

## 后续优化方向

1. 添加消息分类功能
2. 实现消息历史查看功能
3. 支持消息附件
4. 添加群发消息功能
5. 管理员后台增加消息发送状态和已读状态查询 