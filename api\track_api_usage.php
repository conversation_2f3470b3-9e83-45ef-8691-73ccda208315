<?php
// 允许跨域请求
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// 如果是OPTIONS请求，直接返回
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 确保请求方法为POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405); // Method Not Allowed
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit;
}

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);
$apiKey = $data['api_key'] ?? '';

// 验证API密钥
if (empty($apiKey)) {
    http_response_code(400); // Bad Request
    echo json_encode(['success' => false, 'message' => 'API密钥不能为空']);
    exit;
}

// 连接数据库
$db_host = 'localhost';
$db_name = 'agpt';
$db_user = 'agpt';
$db_pass = 'hunterl6628096';

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// 检查连接
if ($conn->connect_error) {
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => '数据库连接失败']);
    exit;
}
$conn->set_charset("utf8mb4");

// 查找用户并检查剩余次数
$query = "SELECT id, username, remaining_requests FROM users WHERE api_key = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $apiKey);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    http_response_code(404); // Not Found
    echo json_encode(['success' => false, 'message' => '找不到该API密钥对应的用户']);
    $stmt->close();
    $conn->close();
    exit;
}

$user = $result->fetch_assoc();
$stmt->close();

// 检查用户是否有剩余次数
if ($user['remaining_requests'] <= 0) {
    http_response_code(403); // Forbidden
    echo json_encode([
        'success' => false, 
        'message' => '您的调用次数已用尽，请联系管理员充值',
        'remaining' => 0
    ]);
    $conn->close();
    exit;
}

// 减少用户的剩余次数
$newRemaining = $user['remaining_requests'] - 1;
$updateQuery = "UPDATE users SET remaining_requests = ? WHERE id = ?";
$stmt = $conn->prepare($updateQuery);
$stmt->bind_param("ii", $newRemaining, $user['id']);

if (!$stmt->execute()) {
    http_response_code(500); // Internal Server Error
    echo json_encode(['success' => false, 'message' => '更新剩余次数失败']);
    $stmt->close();
    $conn->close();
    exit;
}

$stmt->close();
$conn->close();

// 返回成功响应
echo json_encode([
    'success' => true,
    'message' => '调用成功记录',
    'remaining' => $newRemaining,
    'username' => $user['username']
]);
?> 