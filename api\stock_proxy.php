<?php
// 股票代码
$stockCode = $_GET['symbol'] ?? '';
$type = $_GET['type'] ?? '';

// 检查股票代码是否为空（除了index和changes类型外都需要股票代码）
if (empty($stockCode) && $type != 'index' && $type != 'changes') {
    header('Content-Type: application/json');
    echo json_encode(['error' => '股票代码不能为空']);
    exit;
}

// 目标 API 地址
if ($type == 'fsjy') {
    // 分时成交数据API
    $apiUrl = "http://localhost:8888/api/public/stock_zh_a_minute?symbol=" . urlencode($stockCode);
} else if ($type == 'hist') {
    // 获取历史交易数据
    $end_date = date('Ymd');
    $start_date = date('Ymd', strtotime('-30 days'));
    $apiUrl = "http://localhost:8888/api/public/stock_zh_a_hist?symbol=" . urlencode($stockCode) . 
              "&period=daily&start_date=" . $start_date . "&end_date=" . $end_date . "&adjust=qfq";
} else if ($type == 'timeline') {
    // 获取分时图数据
    $apiUrl = "http://localhost:8888/api/public/stock_zh_a_minute?symbol=" . urlencode($stockCode);
} else if ($type == 'kline') {
    // 获取K线数据
    $period = $_GET['period'] ?? 'daily'; // 默认日K，可以是daily, weekly, monthly
    $days = $_GET['days'] ?? 90; // 默认获取90天数据
    $end_date = date('Ymd');
    
    // 根据周期选择适当的开始日期
    if ($period === 'weekly') {
        $start_date = date('Ymd', strtotime("-180 days")); // 约6个月
    } else if ($period === 'monthly') {
        $start_date = date('Ymd', strtotime("-365 days")); // 约1年
    } else {
        $start_date = date('Ymd', strtotime("-$days days")); // 默认日K
    }
    
    $adjust = $_GET['adjust'] ?? 'qfq'; // 默认前复权
    
    // 注意：这里不需要添加sh或sz前缀，API会根据股票代码自行处理
    $apiUrl = "http://127.0.0.1:8888/api/public/stock_zh_a_hist?symbol=" . urlencode($stockCode) . 
              "&period=$period&start_date=$start_date&end_date=$end_date&adjust=$adjust";
               
    error_log("K线API请求: $apiUrl");
} else if ($type == 'index') {
    // 获取市场指数数据
    $indexType = $_GET['index_type'] ?? '沪深重要指数';
    $apiUrl = "http://localhost:8888/api/public/stock_zh_index_spot_em?symbol=" . urlencode($indexType);
} else if ($type == 'changes') {
    // 获取市场变动数据（火箭发射、快速反弹、大笔买入）
    $changesType = $_GET['changes_type'] ?? '大笔买入';
    $apiUrl = "http://localhost:8888/api/public/stock_changes_em?symbol=" . urlencode($changesType);
} else {
    // 默认使用股票实时行情API
    $apiUrl = "http://localhost:8888/api/public/stock_bid_ask_em?symbol=" . urlencode($stockCode);
}

// 记录请求
error_log("请求API: " . $apiUrl);

// 初始化 cURL
$ch = curl_init();

// 设置 cURL 选项
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, false); // 不返回响应头
curl_setopt($ch, CURLOPT_TIMEOUT, 10); // 设置超时时间

// 发送请求并获取响应
$response = curl_exec($ch);

// 检查是否有错误发生
if (curl_errno($ch)) {
    header('Content-Type: application/json');
    $error = curl_error($ch);
    error_log("cURL错误: " . $error);
    echo json_encode(['error' => 'cURL 错误: ' . $error]);
    curl_close($ch);
    exit;
}

// 获取HTTP状态码
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
// 关闭 cURL 资源
curl_close($ch);

// 检查HTTP状态码
if ($httpCode != 200) {
    header('Content-Type: application/json');
    error_log("HTTP错误: " . $httpCode . ", 响应: " . substr($response, 0, 200));
    echo json_encode(['error' => 'HTTP错误: ' . $httpCode]);
    exit;
}

// 记录响应长度
error_log("API响应长度: " . strlen($response));

// 验证响应是否为有效的JSON
if (!empty($response)) {
    // 尝试解码以验证是否为有效的JSON
    $test = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        header('Content-Type: application/json');
        error_log("JSON解析错误: " . json_last_error_msg() . ", 响应: " . substr($response, 0, 200));
        echo json_encode(['error' => 'JSON解析错误: 服务器返回的数据格式无效']);
        exit;
    }
} else {
    header('Content-Type: application/json');
    error_log("API返回空响应");
    echo json_encode(['error' => '服务器返回空响应']);
    exit;
}

// 设置响应头为 JSON
header('Content-Type: application/json');

// 处理历史数据，计算均价
if ($type == 'hist' && !empty($response)) {
    $data = json_decode($response, true);
    if (is_array($data)) {
        // 按日期降序排序
        usort($data, function($a, $b) {
            return strtotime($b['日期']) - strtotime($a['日期']);
        });
        
        // 计算均价
        $ma5 = $ma10 = $ma20 = 0;
        $count5 = $count10 = $count20 = 0;
        
        foreach ($data as $index => $item) {
            if ($index < 5) {
                $ma5 += $item['收盘'];
                $count5++;
            }
            if ($index < 10) {
                $ma10 += $item['收盘'];
                $count10++;
            }
            if ($index < 20) {
                $ma20 += $item['收盘'];
                $count20++;
            }
        }
        
        // 计算最终均价
        $result = [
            'data' => array_slice($data, 0, 5), // 只返回最近5天数据
            'ma5' => $count5 > 0 ? round($ma5 / $count5, 2) : 0,
            'ma10' => $count10 > 0 ? round($ma10 / $count10, 2) : 0,
            'ma20' => $count20 > 0 ? round($ma20 / $count20, 2) : 0
        ];
        
        echo json_encode($result);
    } else {
        echo $response;
    }
} else if ($type == 'kline' && !empty($response)) {
    $data = json_decode($response, true);
    error_log("K线数据原始响应: " . substr(json_encode($data), 0, 200) . "...");
    
    // 直接返回原始数据，由前端处理格式
    echo json_encode([
        'code' => 0,
        'data' => $data
    ]);
} else if ($type == 'timeline' && !empty($response)) {
    $data = json_decode($response, true);
    if (is_array($data)) {
        // 新的处理方式，适用于返回day数组格式的数据
        if (isset($data) && is_array($data)) {
            $formattedData = [];
            $basePrice = 0;
            $currentDate = date('Y-m-d');
            
            // 如果返回了直接的数组（每分钟数据）
            if (isset($data[0]) && isset($data[0]['day'])) {
                // 提取基准价格（使用第一条记录的开盘价）
                $basePrice = isset($data[0]['open']) ? floatval($data[0]['open']) : 0;
                
                // 仅保留当天的分时数据
                foreach ($data as $item) {
                    if (isset($item['day']) && strpos($item['day'], $currentDate) === 0) {
                        // 提取时间部分
                        $timePart = substr($item['day'], 11, 5); // 提取HH:MM格式
                        
                        $formattedData[] = [
                            'time' => $timePart,
                            'price' => floatval($item['close']),
                            'volume' => floatval($item['volume']),
                            'avg_price' => (floatval($item['high']) + floatval($item['low'])) / 2,
                            'open' => floatval($item['open']),
                            'high' => floatval($item['high']),
                            'low' => floatval($item['low'])
                        ];
                    }
                }
            }
            // 新浪接口返回的分时数据格式处理
            else if (isset($data['day']) && is_array($data['day'])) {
                $basePrice = !empty($data['day']['yestclose']) ? floatval($data['day']['yestclose']) : 0;
                
                // 使用day中的开盘价作为基准价
                if ($basePrice == 0 && isset($data['day']['open'])) {
                    $basePrice = floatval($data['day']['open']);
                }
                
                // 如果存在分时数据，进行处理
                if (isset($data['data']) && is_array($data['data'])) {
                    foreach ($data['data'] as $item) {
                        if (count($item) >= 6) {
                            // 分时数据格式：[时间, 价格, 均价, 成交量, 成交额, 方向]
                            $time = $item[0];
                            $price = floatval($item[1]);
                            $avgPrice = floatval($item[2]);
                            $volume = floatval($item[3]);
                            
                            $formattedData[] = [
                                'time' => $time,
                                'price' => $price,
                                'volume' => $volume,
                                'avg_price' => $avgPrice
                            ];
                        }
                    }
                }
            }
            
            echo json_encode([
                'code' => 0,
                'data' => $formattedData,
                'basePrice' => $basePrice
            ]);
        } else {
            // 旧的处理方式，保留以兼容其他可能的数据结构
            usort($data, function($a, $b) {
                return strtotime($a['时间']) - strtotime($b['时间']);
            });
            
            // 格式化数据为echarts所需格式
            $formattedData = [];
            $basePrice = 0;
            
            // 查找第一个有效价格作为基准价
            foreach ($data as $index => $item) {
                if (!empty($item['价格']) && is_numeric($item['价格']) && $item['价格'] > 0) {
                    $basePrice = $item['价格'];
                    break;
                }
            }
            
            // 如果没有找到基准价，使用0
            if ($basePrice == 0) {
                $basePrice = 0;
            }
            
            foreach ($data as $item) {
                // 确保价格数据有效
                $price = !empty($item['价格']) && is_numeric($item['价格']) ? $item['价格'] : $basePrice;
                $volume = !empty($item['成交量']) && is_numeric($item['成交量']) ? $item['成交量'] : 0;
                
                $formattedData[] = [
                    'time' => $item['时间'],
                    'price' => $price,
                    'volume' => $volume,
                    'avg_price' => !empty($item['均价']) && is_numeric($item['均价']) ? $item['均价'] : $price
                ];
            }
            
            echo json_encode([
                'code' => 0,
                'data' => $formattedData,
                'basePrice' => $basePrice
            ]);
        }
    } else {
        echo $response;
    }
} else {
    // 输出原始响应
    echo $response;
}

?>
