<?php
// 首先关闭错误显示，防止错误输出影响JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 必须在输出前启动会话
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

require_once '../includes/functions.php';
require_once 'db_connect.php';

// 增加会话调试信息，使用error_log而非直接输出
error_log("获取股票API: SESSION=" . json_encode($_SESSION ?? []));
error_log("获取股票API: Cookie信息=" . json_encode($_COOKIE));
error_log("获取股票API: user_id=" . ($_SESSION['user_id'] ?? '未设置'));

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['error' => '请先登录']);
    exit;
}

$username = $_SESSION['username'];

try {
    // 获取用户的股票列表
    $stmt = $pdo->prepare("SELECT stock_code, stock_name FROM user_stocks WHERE username = ? ORDER BY id DESC");
    $stmt->execute([$username]);
    $stocks = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode(['success' => true, 'stocks' => $stocks]);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => '获取股票列表失败：' . $e->getMessage()]);
}

exit;