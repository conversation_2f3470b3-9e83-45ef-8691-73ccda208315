<?php
// 添加适当的响应头
header('Content-Type: application/json');

// 禁用错误显示，避免影响JSON输出
ini_set('display_errors', 0);
error_log("添加股票 API 被调用");

// 开启session
session_start();

// 定义服务器根目录路径
$rootPath = '/www/wwwroot/agpt.llingfei.com';

// 记录调试信息
error_log("服务器根目录: " . $rootPath);

// 尝试直接引入绝对路径的文件
try {
    require_once $rootPath . '/includes/functions.php';
    error_log("文件引入成功");
} catch (Exception $e) {
    error_log("文件引入失败: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '系统配置错误，请联系管理员'
    ]);
    exit;
}

// 记录会话信息
error_log("会话ID: " . session_id());
error_log("会话数据: " . json_encode($_SESSION));

// 检查用户是否已登录
if (!isset($_SESSION['username'])) {
    http_response_code(401);
    echo json_encode(['error' => '请先登录']);
    exit;
}

// 获取POST数据
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['code']) || !isset($data['name'])) {
    http_response_code(400);
    echo json_encode(['error' => '缺少必要参数']);
    exit;
}

$code = $data['code'];
$name = $data['name'];
$username = $_SESSION['username'];

// 标准化变量
$stockCode = trim($code);
$stockName = !empty($name) ? trim($name) : '未知股票';

// 记录要添加的股票信息
error_log("添加股票 API: 尝试添加 Stock Code: {$stockCode}, Stock Name: {$stockName}, User ID: {$username}");

try {
    // 连接数据库
    $db = getDbConnection();
    if (!$db) {
        throw new Exception("无法连接到数据库");
    }
    
    // 开始事务
    $db->begin_transaction();
    
    // 先检查用户是否已经添加了该股票
    $checkSql = "SELECT id FROM user_stocks WHERE user_id = ? AND stock_id = ?";
    $checkStmt = $db->prepare($checkSql);
    $checkStmt->bind_param("is", $username, $stockCode);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows > 0) {
        // 用户已添加该股票
        $db->rollback();
        error_log("添加股票 API: 用户已添加该股票");
        echo json_encode([
            'success' => false,
            'message' => '该股票已在您的关注列表中',
            'already_exists' => true
        ]);
        exit;
    }
    
    // 检查stocks表中是否已存在该股票
    $checkStockSql = "SELECT id FROM stocks WHERE id = ?";
    $checkStockStmt = $db->prepare($checkStockSql);
    $checkStockStmt->bind_param("s", $stockCode);
    $checkStockStmt->execute();
    $checkStockResult = $checkStockStmt->get_result();
    
    // 如果股票不存在，则添加
    if ($checkStockResult->num_rows == 0) {
        // 添加新股票
        $insertStockSql = "INSERT INTO stocks (id, name, price, price_change, updated_at) VALUES (?, ?, 0, 0, NOW())";
        $insertStockStmt = $db->prepare($insertStockSql);
        $insertStockStmt->bind_param("ss", $stockCode, $stockName);
        
        if (!$insertStockStmt->execute()) {
            throw new Exception("添加股票失败: " . $insertStockStmt->error);
        }
        error_log("添加股票 API: 成功添加新股票到stocks表");
    } else {
        error_log("添加股票 API: 股票已存在于stocks表");
    }
    
    // 添加到用户的股票列表
    $insertUserStockSql = "INSERT INTO user_stocks (user_id, stock_id, created_at) VALUES (?, ?, NOW())";
    $insertUserStockStmt = $db->prepare($insertUserStockSql);
    $insertUserStockStmt->bind_param("is", $username, $stockCode);
    
    if (!$insertUserStockStmt->execute()) {
        throw new Exception("添加用户股票关联失败: " . $insertUserStockStmt->error);
    }
    
    // 提交事务
    $db->commit();
    error_log("添加股票 API: 成功添加股票 {$stockCode} 到用户 {$username}");
    
    echo json_encode([
        'success' => true,
        'message' => '股票添加成功',
        'stock' => [
            'code' => $stockCode,
            'name' => $stockName
        ]
    ]);
    
} catch (Exception $e) {
    // 回滚事务
    if (isset($db)) {
        $db->rollback();
    }
    
    error_log("添加股票 API: 错误: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => '添加股票失败: ' . $e->getMessage()
    ]);
} 