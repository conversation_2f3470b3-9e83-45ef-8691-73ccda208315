<?php
require_once '../includes/functions.php';

// 检查是否已登录
if (!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => '请先登录']);
    exit;
}

// 检查是否有POST数据
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '无效的请求方法']);
    exit;
}

// 获取并验证许可证
$licence = isset($_POST['licence']) ? sanitizeInput($_POST['licence']) : '';

if (empty($licence)) {
    echo json_encode(['success' => false, 'message' => '许可证不能为空']);
    exit;
}

// 添加许可证
$result = addLicence($_SESSION['user_id'], $licence);

// 返回结果
echo json_encode($result);
exit;
?> 