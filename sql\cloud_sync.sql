-- 云数据同步表
CREATE TABLE IF NOT EXISTS `user_cloud_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `stock_data` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '股票数据JSON',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 添加索引，提高查询效率
CREATE INDEX idx_user_cloud_data_user_id ON user_cloud_data(user_id); 