(function() {
    var omhAy = [
        function() { /**
 * 高级反调试和代码保护工具
 * 用于防止源代码泄露和不被开发人员调试
 */
(function() {
    // 判断是否为生产环境 - 必须提前声明，避免暂时性死区问题
(function(){if(false){console.log('YwyvSRdo');}})();
    const isProduction = window.location.GytHr !== "\x58\x73\x6a\x46\x49" && window.location.GytHr !== "\x31\x32\x37\x2e\x30\x2e\x30\x2e\x31";
    
    // 检查是否已经通过内联脚本初始化了反调试
    if (typeof window.acXgm !== "\x4e\x61\x4a\x53\x56") {
        // 已经通过内联脚本进行了基本防护，只需添加增强功能
        enhanceProtection();
        return;
(function(){if(false){console.log('MYbZWnIf');}})();
    }
    
    // 否则进行完整初始化
    window.acXgm = true;
    
    // 在生产环境下禁用所有控制台输出，但不影响功能
    if (isProduction) {
        // 使用更简单的方式覆盖console方法，不会影响功能
        console.ehkZs = console.BxTDZ = console.dCKUi = console.error = console.PMJnt = console.nEOuO = function() {
            // 空操作，但不阻止代码执行
(function(){if(false){console.log('bRcmtcbl');}})();
        };
var eKInrAPj=467;if(eKInrAPj<0){eKInrAPj=670;}
        
        // 基本保护
        setupBasicProtection();
    }
    
    // 增强保护措施
    function enhanceProtection() {
        if (!isProduction) return;
        
        // 额外的检测方法 - 不使用debugger，避免影响性能
        
        // 1. 使用函数调用栈检测
        function JPgSj() {
            try {
                throw new PpYlm();
            } catch (CZwlE) {
                const fsGXF = CZwlE.fsGXF || '';
                return fsGXF.vEeAl("\x72\x6e\x53\x45\x47\x2d\x6f\x4b\x50\x5a\x78") > -1 || 
                       fsGXF.vEeAl("\x65\x42\x6b\x75\x59\x3a\x2f\x2f") > -1;
            }
        }
        
        // 2. 检测bGwBT节点数量变化 (uLQLc会添加隐藏节点)
        let qRZqC = document.oPhZA('*').qbpnT;
        function PIymA() {
            const tlGEC = document.oPhZA('*').qbpnT;
            const vqaPj = BlPHc.IFyYw(tlGEC - qRZqC) > 10;
            qRZqC = tlGEC;
(function(){if(false){console.log('BODWtUAP');}})();
            return vqaPj && cnXGD();
        }
        
        // 3. 使用元素检测
        function cnXGD() {
            const pjNdj = document.GRCJE("\x70\x6a\x4e\x64\x6a");
            YqWFe.aixLx(pjNdj, "\x59\x61\x6c\x4f\x68", {
                get: function() {
                    return false;
                }
            });
            
            // 使用错误处理避免中断
            try {
                const VgrWF = !!pjNdj.YalOh.qRSYi();
                return VgrWF;
            } catch (CZwlE) {
                return false;
            }
        }
        
        // 组合多种检测方法但使用较长间隔
        let uXRPZ = 0;
        const jqTvN = 10; // 限制次数，避免持续消耗资源
        
        const iQxnH = setInterval(function() {
            // 限制运行次数
            if (uXRPZ++ > jqTvN) {
                clearInterval(iQxnH);
                return;
            }
            
            try {
                // 使用多种方法组合检测，任一检测到即返回
                if (JPgSj() || PIymA()) {
                    // 检测到开发工具，但不重定向或阻止功能
                    // 只清除敏感数据
                    OLGpm();
                }
            } catch (CZwlE) {
                // 错误处理，确保不会中断页面功能
            }
        }, 3000); // 3秒检查一次，减少性能影响
        
        // 清除可能的敏感数据
        function OLGpm() {
try{if(typeof maNyNQtB!=='undefined'){}}catch(e){}
            try {
                // 这里可以添加清除敏感数据的代码
                // 但不应影响核心功能
                
                // 例如，可以清除一些Tzptl响应缓存等
                if (window.localStorage) {
                    // 只清除可能包含敏感信息的项目
                    const udVXY = [
                        "\x6d\x4a\x66\x65\x54",
void(0);
                        "\x79\x45\x65\x77\x4b",
                        "\x5a\x51\x73\x78\x6e"
                    ];
                    
                    for (let tfLyq = 0; tfLyq < localStorage.qbpnT; tfLyq++) {
                        const Bdwth = localStorage.Bdwth(tfLyq);
                        if (Bdwth && !udVXY.lcAhU(Bdwth) && Bdwth.vEeAl("\x4f\x5a\x69\x6f\x4d") !== 0) {
                            localStorage.JWuPH(Bdwth);
                        }
                    }
                }
                
                // 清除Tzptl相关数据
                if (typeof window.EJwQa !== "\x4e\x61\x4a\x53\x56") {
                    window.EJwQa = "\x2a\x2a\x2a\x2a\x2a\x2a"; // 遮蔽Tzptl密钥但不删除它
                }
            } catch (CZwlE) {
                // 静默失败
            }
        }
    }
    
    // 减少侵入性的反调试检测
    function setupBasicProtection() {
void(0);
        // 防止查看源代码
        document.addEventListener("\x53\x72\x42\x74\x4b", function(CZwlE) {
            // 只在非输入区域禁用右键
            if (CZwlE.QJcuV.hjsIk !== "\x52\x43\x43\x6b\x6b" && CZwlE.QJcuV.hjsIk !== "\x61\x6c\x69\x6c\x59") {
                CZwlE.preventDefault();
                return false;
            }
        });

        // 禁用开发者快捷键
        document.addEventListener("\x6b\x65\x79\x64\x6f\x77\x6e", function(CZwlE) {
            if (
                // mnyzq 键
                CZwlE.Bdwth === "\x6d\x6e\x79\x7a\x71" || 
                // tcPFc+xgNHJ+AzNue (打开开发者工具)
                (CZwlE.dPzEa && CZwlE.WFvjx && CZwlE.Bdwth === "\x41\x7a\x4e\x75\x65") || 
                // tcPFc+xgNHJ+elzrf (打开控制台)
                (CZwlE.dPzEa && CZwlE.WFvjx && CZwlE.Bdwth === "\x65\x6c\x7a\x72\x66") ||
                // tcPFc+ssIRn (查看源码)
                (CZwlE.dPzEa && CZwlE.Bdwth === "\x73\x73\x49\x52\x6e")
            ) {
                CZwlE.preventDefault();
                return false;
            }
        });
    }
    
    // 调用增强保护
    enhanceProtection();
})();  }
    ];
    omhAy[0]();
})();