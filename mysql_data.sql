-- MySQL dump 10.13  Distrib 5.6.50, for Linux (x86_64)
--
-- Host: localhost    Database: agpt
-- ------------------------------------------------------
-- Server version	5.6.50-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `announcements`
--

DROP TABLE IF EXISTS `announcements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `announcements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL,
  `content` text NOT NULL,
  `publish_date` date NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `announcements`
--

LOCK TABLES `announcements` WRITE;
/*!40000 ALTER TABLE `announcements` DISABLE KEYS */;
INSERT INTO `announcements` VALUES (1,'系统公告','欢迎使用AI-agent股票分析平台！本平台提供智能股票分析和咨询服务。','0000-00-00','2025-03-21 22:41:25'),(2,'功能更新','新增股票实时数据分析功能，支持多种技术指标和AI预测模型。','0000-00-00','2025-03-21 22:41:25'),(3,'使用说明','请在个人主页设置API密钥，以便使用AI分析和咨询功能。','0000-00-00','2025-03-21 22:41:25');
/*!40000 ALTER TABLE `announcements` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `high_score_stocks`
--

DROP TABLE IF EXISTS `high_score_stocks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `high_score_stocks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stock_code` varchar(20) NOT NULL COMMENT '股票代码',
  `stock_name` varchar(50) NOT NULL COMMENT '股票名称',
  `score` int(11) NOT NULL COMMENT '评分',
  `user_id` int(11) NOT NULL COMMENT '提交评分的用户ID',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_stock_code` (`stock_code`) COMMENT '股票代码唯一索引',
  KEY `ix_score` (`score`) COMMENT '评分索引',
  KEY `ix_updated` (`updated_at`) COMMENT '更新时间索引'
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='高评分股票表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `high_score_stocks`
--

LOCK TABLES `high_score_stocks` WRITE;
/*!40000 ALTER TABLE `high_score_stocks` DISABLE KEYS */;
INSERT INTO `high_score_stocks` VALUES (1,'601919','📊 中远海控',80,1,'2025-04-26 13:48:53','2025-04-26 13:48:53');
/*!40000 ALTER TABLE `high_score_stocks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `licences`
--

DROP TABLE IF EXISTS `licences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `licences` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `licence_key` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  CONSTRAINT `licences_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `licences`
--

LOCK TABLES `licences` WRITE;
/*!40000 ALTER TABLE `licences` DISABLE KEYS */;
/*!40000 ALTER TABLE `licences` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `messages`
--

DROP TABLE IF EXISTS `messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL,
  `username` varchar(50) DEFAULT NULL,
  `content` text NOT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `username` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=167 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `messages`
--

LOCK TABLES `messages` WRITE;
/*!40000 ALTER TABLE `messages` DISABLE KEYS */;
INSERT INTO `messages` VALUES (109,NULL,'18981804238','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(110,NULL,'flyhunterl','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',1,'2025-04-03 17:28:11'),(111,NULL,'godii','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(112,NULL,'gorrister','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(113,NULL,'gugen2000','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',1,'2025-04-03 17:28:11'),(114,NULL,'linduo','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(115,NULL,'terrywang','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(116,NULL,'tian','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(117,NULL,'vip','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(118,NULL,'willis','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(119,NULL,'wj','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(120,NULL,'xizhilang','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',1,'2025-04-03 17:28:11'),(121,NULL,'yuanda','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(122,NULL,'yy','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(123,NULL,'不二','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(124,NULL,'椰拓','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(125,NULL,'经年','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',0,'2025-04-03 17:28:11'),(126,NULL,'轶袜子','4月3日对量化进行了加强,增加更多的行情和咨询信息以提高量化精确度.相应的量化时间变得更长,平均在60-70秒之间!',1,'2025-04-03 17:28:11'),(127,19,'swr200108','你的10次量化次数已到账!',0,'2025-04-07 20:22:16'),(128,1,'flyhunterl','hah ',1,'2025-04-08 20:59:37'),(129,NULL,'18981804238','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(130,NULL,'flyhunterl','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',1,'2025-04-08 21:02:27'),(131,NULL,'godii','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(132,NULL,'gorrister','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(133,NULL,'gugen2000','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',1,'2025-04-08 21:02:27'),(134,NULL,'linduo','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(135,NULL,'swr200108','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(136,NULL,'terrywang','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(137,NULL,'tian','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(138,NULL,'vip','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(139,NULL,'willis','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(140,NULL,'wj','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(141,NULL,'xizhilang','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',1,'2025-04-08 21:02:27'),(142,NULL,'yuanda','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(143,NULL,'yy','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(144,NULL,'不二','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(145,NULL,'椰拓','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(146,NULL,'经年','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',0,'2025-04-08 21:02:27'),(147,NULL,'轶袜子','近期调整了量化数据的计算方式,由异步数据改成并行数据以提高量化时间.如果遇到BUG可通过顶部用户名按钮旁的实时反馈按钮进行反馈!',1,'2025-04-08 21:02:27'),(148,22,'zcj225588','检测到重复注册,无法获得赠送次数',0,'2025-04-08 21:41:01'),(149,23,'zcj885522','检测到重复注册,无法获得赠送次数',0,'2025-04-08 21:41:05'),(151,26,'jyf','你已获赠10量化次数',1,'2025-04-08 23:49:40'),(152,25,'<EMAIL>','你已获赠10量化次数',0,'2025-04-08 23:49:45'),(154,9,'xizhilang','系统检测到你今日发生一次量化异常,已退还量化次数*2',1,'2025-04-09 14:34:04'),(155,27,'<EMAIL>','你已获赠10量化次数',1,'2025-04-09 14:59:33'),(156,28,'zyklonwu','你已获赠10量化次数',0,'2025-04-09 15:19:25'),(157,29,'<EMAIL>','你已获赠5量化次数',1,'2025-04-10 21:15:41'),(158,30,'xiaophp','你已获赠5量化次数\r\n',0,'2025-04-12 12:34:15'),(159,31,'<EMAIL>','你已获赠5量化次数',0,'2025-04-12 18:29:20'),(160,32,'caicai912','你已获赠5量化次数',1,'2025-04-13 13:58:24'),(161,34,'17738706757','你已获赠10量化次数',1,'2025-04-21 22:26:19'),(162,35,'xifan','你已获赠5量化次数',1,'2025-04-24 21:23:13'),(163,39,'sunboss_dl','你已获赠5量化次数',1,'2025-06-05 11:15:14'),(164,39,'sunboss_dl','已经返还您4量化次数',1,'2025-06-05 11:38:36'),(165,40,'itsRae','您已获赠5量化次数',0,'2025-06-08 21:34:50'),(166,41,'newtime','你已获赠5量化次数',1,'2025-06-10 10:07:23');
/*!40000 ALTER TABLE `messages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `stocks`
--

DROP TABLE IF EXISTS `stocks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `stocks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(10) NOT NULL,
  `name` varchar(100) NOT NULL,
  `last_updated` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `stocks`
--

LOCK TABLES `stocks` WRITE;
/*!40000 ALTER TABLE `stocks` DISABLE KEYS */;
INSERT INTO `stocks` VALUES (1,'','平安银行','2025-03-22 10:34:12');
/*!40000 ALTER TABLE `stocks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_cloud_data`
--

DROP TABLE IF EXISTS `user_cloud_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_cloud_data` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `stock_data` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '股票数据JSON',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  KEY `idx_user_cloud_data_user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_cloud_data`
--

LOCK TABLES `user_cloud_data` WRITE;
/*!40000 ALTER TABLE `user_cloud_data` DISABLE KEYS */;
INSERT INTO `user_cloud_data` VALUES (1,1,'[{\"code\":\"600967\",\"name\":\"内蒙一机\",\"price\":9.75,\"price_change\":-1.12,\"open\":9.8,\"close\":\"0.00\",\"high\":10.15,\"low\":9.7,\"added_at\":\"2025-03-31T03:26:35.345Z\",\"ma5\":10.43,\"ma10\":10.79,\"ma20\":11.3,\"volumeRatio\":1.22,\"kdj\":{\"k\":14.01,\"d\":21.37,\"j\":-0.69,\"signal\":\"normal\"},\"macd\":{\"dif\":-0.1056,\"dea\":0.1087,\"macd\":-0.4287,\"signal\":\"normal\"},\"change\":-1.12,\"stock_code\":\"600967\",\"stock_name\":\"内蒙一机\",\"dm\":\"600967\",\"mc\":\"内蒙一机\"},{\"code\":\"000738\",\"name\":\"航发控制\",\"price\":17.25,\"change\":2.8,\"added_at\":\"2025-04-03T02:28:16.299Z\",\"stock_code\":\"000738\",\"stock_name\":\"航发控制\",\"dm\":\"000738\",\"mc\":\"航发控制\",\"ma5\":18.06,\"ma10\":18.89,\"ma20\":19.73,\"volumeRatio\":0.97,\"kdj\":{\"k\":12.32,\"d\":14.58,\"j\":7.78,\"signal\":\"normal\"},\"macd\":{\"dif\":-0.5845,\"dea\":-0.2402,\"macd\":-0.6887,\"signal\":\"normal\"},\"high\":17.41,\"low\":16.79,\"open\":16.79,\"price_change\":2.8,\"close\":\"0.00\"},{\"code\":\"601899\",\"name\":\"紫金矿业\",\"price\":15.69,\"change\":1.82,\"added_at\":\"2025-04-08T05:36:51.950Z\",\"stock_code\":\"601899\",\"stock_name\":\"紫金矿业\",\"dm\":\"601899\",\"mc\":\"紫金矿业\",\"ma5\":16.73,\"ma10\":17.44,\"ma20\":17.44,\"volumeRatio\":1.6,\"kdj\":{\"k\":15.9,\"d\":27.09,\"j\":-6.48,\"signal\":\"normal\"},\"macd\":{\"dif\":-0.041,\"dea\":0.2507,\"macd\":-0.5835,\"signal\":\"normal\"},\"high\":15.86,\"low\":15.21,\"open\":15.54,\"close\":\"0.00\",\"price_change\":1.82},{\"code\":\"002255\",\"name\":\"海陆重工\",\"price\":7.34,\"change\":-9.94,\"added_at\":\"2025-04-08T05:52:33.941Z\",\"stock_code\":\"002255\",\"stock_name\":\"海陆重工\",\"dm\":\"002255\",\"mc\":\"海陆重工\",\"ma5\":8.27,\"ma10\":8.04,\"ma20\":7.53,\"volumeRatio\":1.42,\"kdj\":{\"k\":45.64,\"d\":57.41,\"j\":22.09,\"signal\":\"normal\"},\"macd\":{\"dif\":0.4151,\"dea\":0.4064,\"macd\":0.0175,\"signal\":\"normal\"},\"open\":7.77,\"close\":\"0.00\",\"high\":8.32,\"low\":7.34,\"price_change\":-9.94},{\"code\":\"600170\",\"name\":\"上海建工\",\"price\":2.44,\"change\":0.41,\"added_at\":\"2025-04-08T05:53:39.128Z\",\"stock_code\":\"600170\",\"stock_name\":\"上海建工\",\"dm\":\"600170\",\"mc\":\"上海建工\",\"ma5\":2.57,\"ma10\":2.55,\"ma20\":2.54,\"volumeRatio\":0.9,\"kdj\":{\"k\":33.43,\"d\":44.36,\"j\":11.57,\"signal\":\"normal\"},\"macd\":{\"dif\":0.0012,\"dea\":0.009,\"macd\":-0.0155,\"signal\":\"death\"},\"price_change\":0.41,\"open\":2.44,\"close\":\"0.00\",\"high\":2.49,\"low\":2.44}]','2025-03-22 14:40:15','2025-04-08 14:21:42'),(2,12,'[{\"code\":\"002031\",\"name\":\"巨轮智能\",\"price\":8.48,\"price_change\":-2.42,\"open\":8.79,\"close\":\"0.00\",\"high\":8.84,\"low\":8.48,\"added_at\":\"2025-04-01T09:04:25.773Z\",\"ma5\":8.85,\"ma10\":9.25,\"ma20\":9.85,\"volumeRatio\":0.7,\"kdj\":{\"k\":12.64,\"d\":17.16,\"j\":3.59,\"signal\":\"normal\"},\"macd\":{\"dif\":-0.144,\"dea\":0.0896,\"macd\":-0.4672,\"signal\":\"normal\"}},{\"code\":\"000100\",\"name\":\"TCL科技\",\"price\":4.47,\"price_change\":0.45,\"open\":4.46,\"close\":\"0.00\",\"high\":4.53,\"low\":4.45,\"added_at\":\"2025-04-01T09:57:11.015Z\",\"ma5\":4.47,\"ma10\":4.54,\"ma20\":4.64,\"volumeRatio\":0.85,\"kdj\":{\"k\":14.57,\"d\":14.56,\"j\":14.59,\"signal\":\"golden\"},\"macd\":{\"dif\":-0.1048,\"dea\":-0.0881,\"macd\":-0.0335,\"signal\":\"normal\"}}]','2025-04-01 17:57:40','2025-04-01 17:57:40'),(3,33,'[{\"code\":\"002714\",\"name\":\"牧原股份\",\"price\":40.77,\"change\":-0.07,\"added_at\":\"2025-04-17T07:31:16.922Z\",\"stock_code\":\"002714\",\"stock_name\":\"牧原股份\",\"dm\":\"002714\",\"mc\":\"牧原股份\",\"ma5\":40.77,\"ma10\":40.76,\"ma20\":39.86,\"volumeRatio\":0.54,\"kdj\":{\"k\":56.3,\"d\":59.16,\"j\":50.57,\"signal\":\"normal\"},\"macd\":{\"dif\":0.7653,\"dea\":0.734,\"macd\":0.0627,\"signal\":\"normal\"},\"high\":40.83,\"low\":40.22,\"open\":40.5,\"price_change\":\"0.00\",\"close\":\"0.00\"},{\"code\":\"600779\",\"name\":\"水井坊\",\"price\":47.38,\"change\":0.94,\"added_at\":\"2025-04-17T07:31:58.005Z\",\"stock_code\":\"600779\",\"stock_name\":\"水井坊\",\"dm\":\"600779\",\"mc\":\"水井坊\",\"ma5\":47.11,\"ma10\":47.06,\"ma20\":48.77,\"volumeRatio\":1.03,\"kdj\":{\"k\":51.86,\"d\":42.25,\"j\":71.07,\"signal\":\"normal\"},\"macd\":{\"dif\":-0.7757,\"dea\":-0.6084,\"macd\":-0.3347,\"signal\":\"normal\"},\"high\":48.46,\"low\":46.57,\"open\":46.67}]','2025-04-17 15:32:30','2025-04-17 15:32:30'),(4,36,'[{\"code\":\"601919\",\"name\":\"中远海控\",\"price\":14.9,\"change\":1.57,\"added_at\":\"2025-05-09T04:20:02.646Z\",\"stock_code\":\"601919\",\"stock_name\":\"中远海控\",\"dm\":\"601919\",\"mc\":\"中远海控\",\"ma5\":14.66,\"ma10\":14.51,\"ma20\":14.31,\"volumeRatio\":1.52,\"kdj\":{\"k\":85.79,\"d\":82.1,\"j\":93.15,\"signal\":\"normal\"},\"macd\":{\"dif\":0.1659,\"dea\":0.1033,\"macd\":0.1251,\"signal\":\"normal\"},\"high\":14.94,\"low\":14.67,\"open\":14.69,\"price_change\":1.57,\"close\":\"0.00\"}]','2025-05-09 12:26:09','2025-05-09 12:26:09'),(5,39,'[{\"code\":\"600737\",\"name\":\"中粮糖业\",\"price\":9.28,\"change\":-0.75,\"added_at\":\"2025-06-05T03:03:38.783Z\",\"stock_code\":\"600737\",\"stock_name\":\"中粮糖业\",\"dm\":\"600737\",\"mc\":\"中粮糖业\",\"ma5\":9.31,\"ma10\":9.29,\"ma20\":9.3,\"volumeRatio\":1.18,\"kdj\":{\"k\":64.07,\"d\":58.72,\"j\":74.77,\"signal\":\"normal\"},\"macd\":{\"dif\":-0.0159,\"dea\":-0.0223,\"macd\":0.0127,\"signal\":\"normal\"},\"high\":9.35,\"low\":9.27,\"open\":9.35,\"price_change\":-0.75,\"close\":\"0.00\"},{\"code\":\"603699\",\"name\":\"纽威股份\",\"price\":\"0.00\",\"change\":\"0.00\",\"added_at\":\"2025-06-05T03:04:33.668Z\",\"stock_code\":\"603699\",\"stock_name\":\"纽威股份\",\"dm\":\"603699\",\"mc\":\"纽威股份\",\"ma5\":32.41,\"ma10\":31.3,\"ma20\":29.68,\"volumeRatio\":null,\"kdj\":{\"k\":91.82,\"d\":86.64,\"j\":102.17,\"signal\":\"normal\"},\"macd\":{\"dif\":1.7769,\"dea\":1.4164,\"macd\":0.7211,\"signal\":\"normal\"},\"high\":33.84,\"low\":33.06,\"open\":33.6,\"price_change\":\"0.00\",\"close\":\"0.00\"},{\"code\":\"688697\",\"name\":\"纽威数控\",\"price\":\"0.00\",\"change\":\"0.00\",\"added_at\":\"2025-06-05T03:04:48.269Z\",\"stock_code\":\"688697\",\"stock_name\":\"纽威数控\",\"dm\":\"688697\",\"mc\":\"纽威数控\",\"ma5\":19.12,\"ma10\":19.08,\"ma20\":19.3,\"volumeRatio\":0.77,\"kdj\":{\"k\":38.37,\"d\":30.66,\"j\":53.8,\"signal\":\"normal\"},\"macd\":{\"dif\":0.2024,\"dea\":0.2919,\"macd\":-0.1791,\"signal\":\"normal\"},\"high\":19.21,\"low\":19.05,\"open\":19.05,\"price_change\":\"0.00\",\"close\":\"0.00\"},{\"code\":\"601919\",\"name\":\"中远海控\",\"price\":15.96,\"change\":-1.05,\"added_at\":\"2025-06-05T03:06:02.467Z\",\"stock_code\":\"601919\",\"stock_name\":\"中远海控\",\"dm\":\"601919\",\"mc\":\"中远海控\",\"ma5\":16.22,\"ma10\":16.32,\"ma20\":16.07,\"volumeRatio\":1.65,\"kdj\":{\"k\":20.86,\"d\":34.88,\"j\":-7.18,\"signal\":\"normal\"},\"macd\":{\"dif\":0.4496,\"dea\":0.5558,\"macd\":-0.2124,\"signal\":\"normal\"},\"high\":16.17,\"low\":15.89,\"open\":16.12,\"price_change\":-1.05,\"close\":\"0.00\"}]','2025-06-05 11:10:34','2025-06-05 11:10:34');
/*!40000 ALTER TABLE `user_cloud_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_stocks`
--

DROP TABLE IF EXISTS `user_stocks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_stocks` (
  `user_id` int(11) NOT NULL,
  `stock_id` int(11) NOT NULL,
  `added_at` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`,`stock_id`),
  KEY `stock_id` (`stock_id`),
  CONSTRAINT `user_stocks_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_stocks_ibfk_2` FOREIGN KEY (`stock_id`) REFERENCES `stocks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_stocks`
--

LOCK TABLES `user_stocks` WRITE;
/*!40000 ALTER TABLE `user_stocks` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_stocks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `api_key` varchar(255) DEFAULT NULL,
  `remaining_requests` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `register_ip` varchar(50) DEFAULT '未知' COMMENT '注册IP地址',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_register_ip` (`register_ip`)
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'flyhunterl','$2y$10$H/1H20GDrDhusiV4h5S1Pe/34LjM2TGCocTXIuWYEHgyyZahlW./a','sk-Bbj3oJPQjZ1H5fr_HGyMBZolTldCYVQM4jKArYV8Mp1kY9zMhOEe0BWl0yo',9606,'2025-03-21 22:57:54','2a01:4f8:1c1e:a865::1'),(2,'轶袜子','$2y$10$uePvQkZE8ZpsURWthtleM.s6YTU64vWIr3fMGCxBQfc5H/FEOo7o.','sk-4pO4lIoAizKHPhj_cqjNqH7OMyJGnpRbInBnFp7Q4xafleY76gO4MucDLg4',96,'2025-03-22 11:58:43','***********'),(3,'gugen2000','$2y$10$86k6Xw475Cm.uRDAsbNbBOWylIEmoFQNNql0xgr9dqVj3nNqgvxMm','sk-kvKfTKyEQA7X6eZ__U_5mitJHjIWqvLNXDAQ_s3KJzdr6_ztJZyPlZ24Irs',90,'2025-03-22 13:23:24','**************'),(4,'18981804238','$2y$10$lBrfWcOq0eKmgdNQiFn4qOdjaH6F7ug4ZcfMu8gOn2aIRYo2QVC8O','sk-pCkglnO3f7F5eW8_Dsdyu1-0Q0w39c0N2bilTO0vQ9zlrUzxZc-LTmO7iwk',6,'2025-03-22 15:25:32','未知'),(5,'椰拓','$2y$10$Ti2HNyrWCNnvakLF1cHTUO1YlMHQhmPeH2YVzmJwZroQ4OwY4d4yK','sk-1L7OLths3yjpDU9_B_vVh7shygFPEy6aIJgpScgRYzi6dCj8hN9kjh1pIWU',10,'2025-03-23 19:09:56','未知'),(6,'不二','$2y$10$0E//CmZqfMCj./c5MeZxTuQHYr6JgzF5iREakz4Um5MaEDqWpFQz.','sk-UILDWxe9QjCicFl_YoSvgqxekLisngP6VSsPCNc4yY22PkrhKLdTwFKp7vk',10,'2025-03-23 20:01:45','未知'),(7,'yy','$2y$10$fsAAEsLbGmjNT6.v0Xkr3.HDHjbAbMKu4Rjr8RamSHqHsd/cWL2w2','sk-UvLDWxe9QjCicFl_bilNAPelqmddzwRlphRjstRd84ZgPQliYBnSLOH3Zk0',10,'2025-03-23 21:26:20','未知'),(8,'wj','$2y$10$l9Z5Q4zAZoaiPzOepTP99.MrvbOof/dLxCBrye.BGlSvcZ1k7E3NG','sk-klKfTKyEQA7X6eZ_gr7_FDC26TOqZYHJQVnBKvAk-cTOzZSQ4N__r2K39aM',10,'2025-03-24 18:52:58','未知'),(9,'xizhilang','$2y$10$b21ln6pjCdytwtrLPWQW5e5E2bnKmtIdFpLp.1sSBT4wocRZX/6sW','sk-CVSzrZpQwSzPoCL_pJU74RRRRiDTylV3HDn5iRKaYtW4TdsduUXLTIMMr28',12,'2025-03-24 19:17:59','**************'),(10,'willis','$2y$10$uKNsub/YgDVviqlXjryjIOz8aK0NAvTFvYhClPYopzpIasNLL0SOm','sk-Wk0Nr6F1dhLUi7A_iWTiNQ1d3HMaRHOyHPwm9JSar5mS9YFc_9wV0iTenxw',10,'2025-03-25 08:59:26','未知'),(11,'经年','$2y$10$4pjr1UUJpXh6kWya31rRxektoDSx1IW28e7QYtxzd5Bx28KeisBDi','sk-7RdoD7X28VLQhBl_A8Cnlzcof1XFDK9SSMMwujbca43jN5giDd54Fpw0LMU',9,'2025-03-29 09:17:36','未知'),(12,'godii','$2y$10$c9noXeRG7XeQgw8rTGLJe.UYwYsBtZLZlbPECrlTTuZXuxlzUYP/C','sk-xToMC7WyTfI10jk_H8qhyFlNmaa27RyuROvGJwoX8hYTnzCog7bKwuVhQ7c',8,'2025-04-01 17:02:59','未知'),(13,'terrywang','$2y$10$/D9mW84W0wNvD5/EEhT6duNSN.9pyBQonbk3j0D/kLqbmQzejGyl6','sk-RweyNTL9uP4lmHh_pds2bW3Q3cFne75oJSh0E670bdcgKeIiaFGAlgE2ls8',10,'2025-04-01 17:03:24','未知'),(14,'yuanda','$2y$10$4hasyDhDbIe5TFj8XCdwJOn6MTKMt64Mh518MwqVxN87XK6ek2TJu','sk-9DFaqUIHrQZFo1h_XP53ROBPFJjI_7Xhjm6PHZ7LGaONSiHqxNYseR47umw',10,'2025-04-01 17:03:41','未知'),(15,'gorrister','$2y$10$.WSotzkkGIMGPBFrA1kcmenMW.WYPpV..W8dahy/k9rhoF5nrGc9u','sk-6GgnxRWhabz1AZt_glhIBqVVSp5wN8I4e1kZa74b92QRD7tC3QhlCD7QvKY',10,'2025-04-01 17:03:43','未知'),(16,'tian','$2y$10$jseyWxz2B2PeVnhRXHo1QeHWxKL98iMYYSbt5NdOO6.WqjDON1Jo6','sk-JManEOuLlYb3jHC_p2GGhnjnZNvMZ5STb4hywEbiCKV1vzhUlFo1D_amn4o',10,'2025-04-01 17:04:12','未知'),(17,'vip','$2y$10$lZ2D734CADDL.5HjeTHmpus9uB1.PRYn.3xxxhrfHOF9fyYz4/mMS','sk-wPfQJnHVPbfJc2d_Dmc3xAfSgHlpOCtShs5A8mYLT4Uwk2NInVX9IGrxTyI',10,'2025-04-01 17:04:24','未知'),(18,'linduo','$2y$10$FsdxiiGDQ6jtIkw8QD11EON.z1JdS1hlpXRU7huPSchbZf6YJA46q','sk-FuGPXKc2kJ1H3hV_xKm3KodD0gL6xEplH9eh1M_7N7-0Fq3B7swJaDxdHB8',10,'2025-04-01 17:11:38','未知'),(19,'swr200108','$2y$10$6O1kxsGPjaQX9WaCZlj/ne8uIa5dhw1hZ/iU/MXV70SC97aflyOxa','sk-zvcbwmi2ZgP8BIX_ckPLGQy9X6-d6s8m8tKQuog3C4IecCbRbKlWcIbNR1s',10,'2025-04-07 15:00:20','未知'),(21,'zcj114477','$2y$10$0yiU2t4nfsrrsNxrvwHmZO8EKBxlL.xd1hU7bqLKrP5siAa9.PlgK','sk-a0tUNrn7KEBHR84_Ju45w5bdZJqOqYq7AjMqE6C0_I2wkC3H87I4MFMunYI',10,'2025-04-08 21:35:50','未知'),(22,'zcj225588','$2y$10$QKXrJHReY2tJ0tTvhX1zw.RUMqALhh3r0pAGlWI4iF0DU3b4F.Kzq','cfban',0,'2025-04-08 21:36:14','未知'),(23,'zcj885522','$2y$10$gX0nmFFf5zZx5aF9nqmFWujWlrmutZrk6kP.fPHLAJXLnWk3.56qy','cfban',0,'2025-04-08 21:36:42','未知'),(25,'<EMAIL>','$2y$10$8tqSsrcmc.RuHmgCL/Gc6e0CaE/A.xG4RuHRbN7pVqQf8SxCL.paC','sk-FEGPXKc2kJ1H3hV_Kfo9cjAYI_aTTdqoaIwjY2djQXCaBnRCP13gcsvPUkA',10,'2025-04-08 23:37:22','***************'),(26,'jyf','$2y$10$4EvVbdNFkeiDu1gkJtT7g.aY1q.JE2DjGUtCPHzs/bjPsFlrbCFS6','sk-IavUB64SMc0pts5_WDNBjLWLiPN-20jyQWM9mxKN9ROb38CYu4ltpoXPyrE',10,'2025-04-08 23:46:46','*************'),(27,'<EMAIL>','$2y$10$VZ3m3BHgAYB5LFXg75cMJeUXLXRAbkOpQzwmO1tvAyJ2XgOBCM2.S','sk-QLAW4fQub9e263n_MBhVYcISTVtAzxXWnhMgjyLUhF596r9VBuciPwB0clk',3,'2025-04-09 14:56:20','*************'),(28,'zyklonwu','$2y$10$xPNBFoZLiCfOMqr3.lurOumNlwC/MnIxCyb/0uk3RwgRhQCLVF5t2','sk-L9p3Hb31gBtseCp_ZS18GRwp2izjSBsHsnN4fSX20LCq88YCAFHh3bq0vDk',10,'2025-04-09 15:17:42','**************'),(29,'<EMAIL>','$2y$10$5ynnwkAH.dw32ZvTPJI8au4xCErICFGDdhWHtdzvEOQBOMDHUpPuu','sk-SyIIsqYMyQwI3GW_dhmVor6EYgBD-HjhrSsFwGftBWqPRKlZVbrZZ8Um7fM',4,'2025-04-10 21:10:08','2400:8a20:123:12:be24:11ff:fe8b:b772'),(30,'xiaophp','$2y$10$8mzYnSHyHsMt718E4Trf3efrygRAsFF5.rD6.0eLRRhIDfc5YVNLW','sk-d145w0qPeu5vkJM_ZNUR1BbacgZwap1lFvp5SahGFItIyG90Sazq0MMfOus',5,'2025-04-12 11:14:16','2409:891b:4066:7bb::1'),(31,'<EMAIL>','$2y$10$P4HHCHt6tzavsmN2MA2KdeIrvnDs07vi9ZAjVr.Ue.tGkntqUr6PO','sk-oxljrxF7z5oZS8e_MmKCZhEbNS_re0S1V55T-w2jNyEAJNrMXHeOOhKWsCo',5,'2025-04-12 18:16:19','*************'),(32,'caicai912','$2y$10$andVgYdRl/CM3cVuIhAAB.EbDULtLj7y/ucJk4KGBB.qK/PR9q8k6','sk-lliIGkh59m1PQao_sPo_CzkNyAC34PsuaJaNKLdoFdpma2DeWB-r9kIcWCk',4,'2025-04-13 13:57:03','***************'),(33,'hm21','$2y$10$28TCtUejHLBP2a9cQ.POI.4IwGRf6dv8PgEa/mCuHO/TnhgWtS3oa','sk-j9fwepu0NPZtdDS_m5e_5gZZMXXFpKCdmsTvGiKGTM58AtEr0pe-vGM16H0',5,'2025-04-17 15:30:26','*************'),(34,'17738706757','$2y$10$/lhjEsqNJSw90S/es2/8J.pV9A/8DRDfNCDJ7fNoZA.UmIAb443/m','sk-eHH6ql0A3XmvPoC_BXlSKPh8y0-PZ7eLvIIp1_58R4qM7rJYw0A2cxXOAnU',15,'2025-04-21 22:23:13','240e:476:ba81:326b:74e7:feff:fe74:c520'),(35,'xifan','$2y$10$v1llsPg6/TPWNEYFLvd2TuouVssoldfZkdYPEXMvVJv5llCRjpCbW','sk-j9Fwepu0NPZtdDS_uys1uYye5QSv7MujJFMlCZg14q1YR2WhxRdEyOZibM0',20,'2025-04-24 21:20:23','240e:47e:410:5b41:403c:d9ff:fe72:609b'),(36,'balalala','$2y$10$94lOPKDObE7PwbTsGxHT0.yDyrwkmEIQDbniP0h9HBffVWlY4hEg6','sk-AR6sR1HQ6DpsIjw_Kh_zyFcMsM45rfCtkv8kHpsYrb3H2IAYy-hH5hvYRS0',8,'2025-05-09 12:17:26','240e:476:5c2:e5f2:287f:d6bd:81fb:99f3'),(37,'soft','$2y$10$Mk4RKuzR327soR67eWMm3uEKCHUJZ1SqhIFQmOqfAbOk1sfNpI9IG',NULL,6,'2025-06-01 12:20:26','***************'),(38,'zouclang','$2y$10$0mJQJscUK20q0fdsXSZBB.xe5XsYFP/WMbj0tPXTc1kok3SViH/8a',NULL,0,'2025-06-05 10:45:10','240e:479:4e00:a5f3:10e0:3217:63a3:f4fa'),(39,'sunboss_dl','$2y$10$aD487FmnJDfdfyiszuM3subcQ/KL6X1qV79SUsZsDcGISpAth4rTq','sk-86XRE0xCYM7hPsV_tUexRM-H0N2Lj26QaHz5-KzueLZWSFCHjh-wz0iXlZQ',9,'2025-06-05 10:49:11','**************'),(40,'itsRae','$2y$10$HSoo.sYJc36PPK/XKTecDOfPq8RHPw8BGdh1fmjCSSqj/yVdg28um','sk-iNYDGf9o7x6kXKU_ByZn4nxE4DkIQocdojQwH20Z7h_ishlwFbGD5MkSngw',5,'2025-06-06 17:30:20','************'),(41,'newtime','$2y$10$PZetjzNTLc4OY4NEW8.1TeA/JliVnMWf2aZYGkterm0kx4J4v2H2y','sk-07tT3ta10w9tTHQ_TCp_MOCK-Qb7V4Vomwtcd00JvbMtIaWi9HUqLDljCZM',3,'2025-06-09 17:55:15','**************');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'agpt'
--

--
-- Dumping routines for database 'agpt'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-11 10:57:37
