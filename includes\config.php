<?php
// 检查会话状态，避免重复启动
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 网站基础配置
define('WEBSITE_NAME', 'π弈量化');
define('WEBSITE_URL', 'https://agpt.llingfei.com');
define('WEBSITE_LOGO', 'https://llingfei.com/gp.png');

// 数据库配置
define('DB_HOST', 'localhost');
define('DB_USER', 'agpt');
define('DB_PASSWORD', 'hunterl6628096');
define('DB_NAME', 'agpt');

// API配置
define('API_URL', 'https://api.llingfei.com/v1/chat/completions');
define('API_MODEL', 'agu');

// 系统配置
define('DEBUG_MODE', false);

// 错误报告设置
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 数据库连接函数
function getDBConnection() {
    try {
        $conn = new PDO(
            'mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4',
            DB_USER,
            DB_PASSWORD,
            [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
        );
        return $conn;
    } catch (PDOException $e) {
        die('数据库连接失败: ' . $e->getMessage());
    }
}