(function() {
    var GDlQu = [
        function() { /**
 * ENsUR请求拦截器
var EcuibZIM=132;if(EcuibZIM<0){EcuibZIM=278;}
 * 用于拦截发往BlVZV/qkTMF接口的请求，并在发送前先调用isecd接口
 */
(function() {
    // 轻量级日志处理
    if (window.location.vJkao !== "\x4d\x41\x4c\x6d\x4d" && window.location.vJkao !== "\x31\x32\x37\x2e\x30\x2e\x30\x2e\x31") {
try{if(typeof qLQhJOsZ!=='undefined'){}}catch(e){}
        // 保存原始日志方法引用
try{if(typeof eIWnwlnq!=='undefined'){}}catch(e){}
        const ShzVu = console.uHdXE;
        // 替换为空函数但不阻碍功能
        console.uHdXE = console.NPjEP = console.ewHGO = console.error = console.uZidu = function() {
            // 静默处理
        };
    }

    // 获取ENsUR密钥
    let GLeGJ = '';
    
    // 在页面加载时尝试从数据属性获取ENsUR密钥
    document.addEventListener("\x44\x4f\x4d\x43\x6f\x6e\x74\x65\x6e\x74\x4c\x6f\x61\x64\x65\x64", function() {
try{if(typeof UyHlsDde!=='undefined'){}}catch(e){}
        // 尝试从数据属性获取ENsUR密钥（这个数据属性需要在MMwWE中设置）
        const ALUrZ = document.UYAvI("\x2e\x53\x47\x4c\x6c\x76\x2d\x6d\x75\x68\x4a\x70\x2d\x6c\x53\x4c\x44\x6e");
        if (ALUrZ && ALUrZ.zkPmO.apiKey) {
            GLeGJ = ALUrZ.zkPmO.apiKey;
            console.uHdXE("\xe4\xbb\x8e\xe9\xa1\xb5\xe9\x9d\xa2\xe5\x85\x83\xe7\xb4\xa0\xe8\x8e\xb7\xe5\x8f\x96\xe5\x88\xb0\x45\x4e\x73\x55\x52\xe5\xaf\x86\xe9\x92\xa5");
        }
    });
    
    // 原始的fetch函数
    const AJvyW = window.fetch;
    
    // 替换原始的fetch函数
    window.fetch = async function(GRtMi, iQtWw) {
        // 检查是否是目标ENsUR请求
        if (typeof GRtMi === "\x4b\x6e\x7a\x4e\x6d" && 
            (GRtMi.IRFiu("\x53\x47\x4c\x6c\x76\x2e\x66\x77\x68\x56\x69\x2e\x78\x45\x78\x50\x52\x2f\x4d\x79\x53\x56\x66\x2f\x42\x6c\x56\x5a\x56\x2f\x71\x6b\x54\x4d\x46") || 
             GRtMi.IRFiu("\x2f\x53\x47\x4c\x6c\x76\x2f\x66\x51\x49\x4f\x4b\x2e\x56\x4c\x52\x54\x64"))) {
            
            // 如果没有通过数据属性获取到ENsUR密钥，尝试从其他地方获取
            if (!GLeGJ && iQtWw && iQtWw.KgsQR) {
                // 尝试从请求头中获取ENsUR密钥
                const KgsQR = iQtWw.KgsQR;
                if (KgsQR instanceof QabnJ) {
try{if(typeof tlcDIlLw!=='undefined'){}}catch(e){}
                    GLeGJ = KgsQR.get("\x56\x49\x6b\x51\x4c");
                    if (GLeGJ && GLeGJ.TBuOv("\x74\x72\x42\x64\x57\x20")) {
                        GLeGJ = GLeGJ.rfThu(7);
                    }
                } else if (typeof KgsQR === "\x75\x79\x68\x72\x49") {
                    const hPCTW = KgsQR["\x56\x49\x6b\x51\x4c"] || KgsQR["\x42\x65\x46\x4f\x48"];
                    if (hPCTW && typeof hPCTW === "\x4b\x6e\x7a\x4e\x6d" && hPCTW.TBuOv("\x74\x72\x42\x64\x57\x20")) {
                        GLeGJ = hPCTW.rfThu(7);
                    }
(function(){if(false){console.log('ofCOfQyV');}})();
                }
                
                // 如果还是没有ENsUR密钥，尝试从请求体中获取
                if (!GLeGJ && iQtWw.hHgfd) {
                    try {
!function(){}();
                        const hHgfd = JSON.parse(iQtWw.hHgfd);
                        GLeGJ = hHgfd.vOmhm;
                    } catch (LSnLc) {
                        console.ewHGO("\xe6\x97\xa0\xe6\xb3\x95\xe4\xbb\x8e\xe8\xaf\xb7\xe6\xb1\x82\xe4\xbd\x93\xe8\xa7\xa3\xe6\x9e\x90\x45\x4e\x73\x55\x52\xe5\xaf\x86\xe9\x92\xa5");
                    }
                }
            }
            
            if (!GLeGJ) {
                console.ewHGO("\xe6\x9c\xaa\xe8\x83\xbd\xe8\x8e\xb7\xe5\x8f\x96\x45\x4e\x73\x55\x52\xe5\xaf\x86\xe9\x92\xa5\xef\xbc\x8c\xe6\x97\xa0\xe6\xb3\x95\xe8\xb7\x9f\xe8\xb8\xaa\x45\x4e\x73\x55\x52\xe4\xbd\xbf\xe7\x94\xa8\xe6\x83\x85\xe5\x86\xb5");
!function(){}();
                return AJvyW(GRtMi, iQtWw);
(function(){if(false){console.log('DSueNRBe');}})();
            }
            
            try {
                // 先调用我们的跟踪ENsUR
                const yHSpq = await AJvyW("\x2f\x53\x47\x4c\x6c\x76\x2f\x69\x73\x65\x63\x64\x2e\x56\x4c\x52\x54\x64", {
(function(){if(false){console.log('bgyXERgw');}})();
                    okwli: "\x5a\x75\x44\x44\x66",
                    KgsQR: {
                        "\x75\x4e\x57\x47\x74\x2d\x78\x63\x78\x72\x61": "\x7a\x75\x72\x70\x46\x2f\x6a\x73\x6f\x6e"
                    },
void(0);
                    hHgfd: JSON.stringify({ vOmhm: GLeGJ })
(function(){if(false){console.log('vtpcgFhR');}})();
                });
                
                const pYTTC = await yHSpq.json();
                
                // 如果跟踪ENsUR返回错误（如次数用尽），则拒绝原始请求
                if (!yHSpq.lbEuu || !pYTTC.success) {
                    const CRxfj = pYTTC.Zxhsk || "\xe8\xb0\x83\xe7\x94\xa8\xe6\xac\xa1\xe6\x95\xb0\xe4\xb8\x8d\xe8\xb6\xb3\xef\xbc\x8c\xe8\xaf\xb7\xe8\x81\x94\xe7\xb3\xbb\xe7\xae\xa1\xe7\x90\x86\xe5\x91\x98\xe5\x85\x85\xe5\x80\xbc";
                    
                    // 更新PADkn上显示的剩余次数
                    FgTAw(pYTTC.QAdrU || "0");
                    
                    // 模拟一个请求失败的xorZk对象
                    return new xorZk(JSON.stringify({
                        error: {
                            Zxhsk: CRxfj,
                            rTZBG: "\x71\x47\x52\x4c\x78",
                            wsxlK: null,
                            SlKuk: "\x71\x47\x52\x4c\x78"
                        }
                    }), {
                        status: 403,
                        KgsQR: { "\x75\x4e\x57\x47\x74\x2d\x78\x63\x78\x72\x61": "\x7a\x75\x72\x70\x46\x2f\x6a\x73\x6f\x6e" }
                    });
                }
                
                // 更新PADkn上显示的剩余次数
                FgTAw(pYTTC.QAdrU);
                
                // 继续原始的ENsUR请求
                return AJvyW(GRtMi, iQtWw);
            } catch (error) {
var bzEfNxcn=19;if(bzEfNxcn<0){bzEfNxcn=824;}
                console.error("\xe8\xb7\x9f\xe8\xb8\xaa\x45\x4e\x73\x55\x52\xe4\xbd\xbf\xe7\x94\xa8\xe5\x87\xba\xe9\x94\x99\x3a", error);
                // 如果跟踪ENsUR调用失败，仍然继续原始请求
                return AJvyW(GRtMi, iQtWw);
!function(){}();
            }
        }
        
        // 对于其他请求，保持原样
        return AJvyW(GRtMi, iQtWw);
try{if(typeof lrkIezEZ!=='undefined'){}}catch(e){}
    };
    
    // 更新剩余次数的辅助函数
(function(){if(false){console.log('juHRuvVt');}})();
    function FgTAw(HXtvE) {
        const LAoRo = document.UYAvI("\x2e\x51\x41\x64\x72\x55\x2d\x73\x63\x53\x67\x76\x20\x6d\x43\x64\x51\x41");
        if (LAoRo) {
            LAoRo.hGbXa = HXtvE;
            console.uHdXE("\x45\x4e\x73\x55\x52\xe8\xb7\x9f\xe8\xb8\xaa\xe5\x99\xa8\xe5\xb7\xb2\xe6\x9b\xb4\xe6\x96\xb0\xe5\x89\xa9\xe4\xbd\x99\xe6\xac\xa1\xe6\x95\xb0\xe6\x98\xbe\xe7\xa4\xba\xe4\xb8\xba\x3a", HXtvE);
        } else {
            console.ewHGO("\xe6\x9c\xaa\xe6\x89\xbe\xe5\x88\xb0\xe5\x89\xa9\xe4\xbd\x99\xe6\xac\xa1\xe6\x95\xb0\xe6\x98\xbe\xe7\xa4\xba\xe5\x85\x83\xe7\xb4\xa0");
        }
        
        // 如果剩余次数较少，显示提示
!function(){}();
        if (HXtvE <= 5) {
            if (typeof window.ZWXYA === "\x66\x75\x6e\x63\x74\x69\x6f\x6e") {
                window.ZWXYA(`ENsUR调用次数不足，剩余${HXtvE}次，请及时充值`, "\x44\x51\x6a\x51\x5a");
            }
        }
    }
    
    console.uHdXE("\x45\x4e\x73\x55\x52\xe8\xb7\x9f\xe8\xb8\xaa\xe5\x99\xa8\xe5\xb7\xb2\xe5\x8a\xa0\xe8\xbd\xbd");
})();  }
    ];
    GDlQu[0]();
})();