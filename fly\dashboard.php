<?php
session_start();

// 检查是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

// 辅助函数：格式化API密钥显示
function formatApiKey($apiKey) {
    if (empty($apiKey)) {
        return '未设置';
    }
    
    $length = strlen($apiKey);
    if ($length <= 6) {
        return $apiKey; // 如果密钥长度小于等于6，直接返回原值
    }
    
    // 获取前三位和后三位
    $prefix = substr($apiKey, 0, 3);
    $suffix = substr($apiKey, -3);
    
    // 返回格式化的密钥
    return $prefix . '***' . $suffix;
}

// 数据库连接
$db_host = 'localhost';
$db_name = 'agpt';
$db_user = 'agpt';
$db_pass = 'hunterl6628096';

$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}
$conn->set_charset("utf8mb4");

// 获取总用户数
$usersCountQuery = "SELECT COUNT(*) as total FROM users";
$usersCountResult = $conn->query($usersCountQuery);
$usersCount = $usersCountResult->fetch_assoc()['total'];

// 处理搜索请求
$searchResults = [];
$searchTerm = '';
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $searchTerm = $_GET['search'];
    $searchQuery = "SELECT id, username, api_key, remaining_requests, password_hash, register_ip FROM users 
                  WHERE id LIKE ? OR username LIKE ? OR api_key LIKE ?";
    $stmt = $conn->prepare($searchQuery);
    $searchParam = "%$searchTerm%";
    $stmt->bind_param("sss", $searchParam, $searchParam, $searchParam);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $searchResults[] = $row;
    }
    $stmt->close();
}

// 处理用户操作
$operationMessage = '';
$operationError = '';

// 修改密码
if (isset($_POST['action']) && $_POST['action'] === 'update_password') {
    $userId = $_POST['user_id'];
    $newPassword = $_POST['new_password'];
    
    if (empty($newPassword)) {
        $operationError = '密码不能为空';
    } else {
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        $updateQuery = "UPDATE users SET password_hash = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("si", $passwordHash, $userId);
        
        if ($stmt->execute()) {
            $operationMessage = '密码修改成功';
        } else {
            $operationError = '密码修改失败: ' . $conn->error;
        }
        $stmt->close();
    }
}

// 修改API密钥
if (isset($_POST['action']) && $_POST['action'] === 'update_api_key') {
    $userId = $_POST['user_id'];
    $newApiKey = $_POST['new_api_key'];
    
    if (empty($newApiKey)) {
        $operationError = 'API密钥不能为空';
    } else {
        $updateQuery = "UPDATE users SET api_key = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("si", $newApiKey, $userId);
        
        if ($stmt->execute()) {
            $operationMessage = 'API密钥修改成功';
        } else {
            $operationError = 'API密钥修改失败: ' . $conn->error;
        }
        $stmt->close();
    }
}

// 修改剩余次数
if (isset($_POST['action']) && $_POST['action'] === 'update_remaining') {
    $userId = $_POST['user_id'];
    $newRemaining = $_POST['new_remaining'];
    
    if (!is_numeric($newRemaining) || $newRemaining < 0) {
        $operationError = '剩余次数必须是大于等于0的数字';
    } else {
        $updateQuery = "UPDATE users SET remaining_requests = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ii", $newRemaining, $userId);
        
        if ($stmt->execute()) {
            $operationMessage = '剩余次数修改成功';
        } else {
            $operationError = '剩余次数修改失败: ' . $conn->error;
        }
        $stmt->close();
    }
}

// 批量设置剩余次数
if (isset($_POST['action']) && $_POST['action'] === 'bulk_update_remaining') {
    $bulkValue = intval($_POST['bulk_remaining']);
    $selectedUsers = $_POST['selected_users'] ?? [];
    
    if (empty($selectedUsers)) {
        $operationError = '请选择至少一个用户';
    } elseif ($bulkValue < 0) {
        $operationError = '剩余次数必须是大于等于0的数字';
    } else {
        $updateCount = 0;
        $errors = [];
        
        foreach ($selectedUsers as $userId) {
            $updateQuery = "UPDATE users SET remaining_requests = ? WHERE id = ?";
            $stmt = $conn->prepare($updateQuery);
            $stmt->bind_param("ii", $bulkValue, $userId);
            
            if ($stmt->execute()) {
                $updateCount++;
            } else {
                $errors[] = "用户ID " . $userId . " 更新失败: " . $conn->error;
            }
            $stmt->close();
        }
        
        if ($updateCount > 0) {
            $operationMessage = "成功更新 $updateCount 个用户的剩余次数";
        }
        
        if (!empty($errors)) {
            $operationError = implode("<br>", $errors);
        }
    }
}

// 删除用户
if (isset($_POST['action']) && $_POST['action'] === 'delete_user') {
    $userId = $_POST['user_id'];
    $adminPassword = $_POST['admin_password'];
    
    // 验证管理员密码
    if ($adminPassword !== 'hunterl6628096') {
        $operationError = '管理员密码不正确，无法删除用户';
    } else {
        $deleteQuery = "DELETE FROM users WHERE id = ?";
        $stmt = $conn->prepare($deleteQuery);
        $stmt->bind_param("i", $userId);
        
        if ($stmt->execute()) {
            $operationMessage = '用户删除成功';
            // 刷新搜索结果
            header("Location: dashboard.php" . ($searchTerm ? "?search=" . urlencode($searchTerm) : ""));
            exit;
        } else {
            $operationError = '用户删除失败: ' . $conn->error;
        }
        $stmt->close();
    }
}

// 消息发送处理
if (isset($_POST['action']) && $_POST['action'] === 'send_message') {
    if (empty($_POST['message_content'])) {
        $operationError = "消息内容不能为空！";
    } else {
        // 检查消息表是否存在，不存在则创建
        $checkTable = $conn->query("SHOW TABLES LIKE 'messages'");
        if ($checkTable->num_rows == 0) {
            $createTable = "CREATE TABLE messages (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                user_id INT(11) NOT NULL,
                username VARCHAR(255) NOT NULL,
                content TEXT NOT NULL,
                is_read TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )";
            if (!$conn->query($createTable)) {
                $operationError = "创建消息表失败: " . $conn->error;
            }
        }
        
        if (empty($operationError)) {
            // 获取接收者信息
            $recipientId = isset($_POST['recipient_id']) ? (int)$_POST['recipient_id'] : 0;
            $recipientUsername = isset($_POST['recipient_username']) ? $_POST['recipient_username'] : '';
            
            if ($recipientId > 0) {
                // 通过ID获取用户名
                $stmt = $conn->prepare("SELECT username FROM users WHERE id = ?");
                $stmt->bind_param("i", $recipientId);
                $stmt->execute();
                $result = $stmt->get_result();
                
                if ($result->num_rows > 0) {
                    $userData = $result->fetch_assoc();
                    $recipientUsername = $userData['username'];
                    
                    // 插入消息
                    $stmt = $conn->prepare("INSERT INTO messages (user_id, username, content) VALUES (?, ?, ?)");
                    $stmt->bind_param("iss", $recipientId, $recipientUsername, $_POST['message_content']);
                    
                    if ($stmt->execute()) {
                        $operationMessage = "消息成功发送给用户: " . $recipientUsername;
                    } else {
                        $operationError = "发送消息失败: " . $conn->error;
                    }
                } else {
                    $operationError = "未找到ID为 " . $recipientId . " 的用户";
                }
            } else {
                $operationError = "无效的接收者ID";
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        h1 {
            color: #333;
            margin: 0;
        }
        .user-stats {
            font-size: 18px;
            font-weight: 500;
            color: #4a90e2;
        }
        .search-section {
            margin-bottom: 30px;
        }
        .search-box {
            display: flex;
            max-width: 600px;
        }
        input[type="text"],
        input[type="password"],
        input[type="number"] {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px 0 0 4px;
            font-size: 16px;
        }
        .search-button {
            padding: 10px 20px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .search-button:hover {
            background-color: #3a75c4;
        }
        .results-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            table-layout: fixed;
        }
        .results-table th, 
        .results-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        /* 设置列宽度 */
        .results-table th:nth-child(1), .results-table td:nth-child(1) { width: 40px; } /* 复选框列 */
        .results-table th:nth-child(2), .results-table td:nth-child(2) { width: 60px; } /* ID列 */
        .results-table th:nth-child(3), .results-table td:nth-child(3) { width: 120px; } /* 用户名列 */
        .results-table th:nth-child(4), .results-table td:nth-child(4) { width: 150px; } /* API密钥列 */
        .results-table th:nth-child(5), .results-table td:nth-child(5) { width: 100px; } /* 剩余次数列 */
        .results-table th:nth-child(6), .results-table td:nth-child(6) { width: auto; } /* 操作列 */
        .results-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        .results-table tr:hover {
            background-color: #f9f9f9;
        }
        .action-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            justify-content: center;
        }
        .btn {
            padding: 6px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 13px;
            transition: background-color 0.3s;
            white-space: nowrap;
        }
        .btn-edit {
            background-color: #4a90e2;
            color: white;
        }
        .btn-edit:hover {
            background-color: #3a75c4;
        }
        .btn-delete {
            background-color: #e74c3c;
            color: white;
        }
        .btn-delete:hover {
            background-color: #c0392b;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        .modal-content {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.2);
            padding: 25px;
            width: 400px;
            max-width: 90%;
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        .modal-title {
            margin: 0;
            font-size: 20px;
        }
        .close-button {
            background: none;
            border: none;
            font-size: 22px;
            cursor: pointer;
            color: #777;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #555;
        }
        .modal-footer {
            margin-top: 20px;
            text-align: right;
        }
        .btn-submit {
            padding: 8px 16px;
            background-color: #4a90e2;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 15px;
            cursor: pointer;
        }
        .btn-submit:hover {
            background-color: #3a75c4;
        }
        .btn-cancel {
            padding: 8px 16px;
            background-color: #ddd;
            color: #333;
            border: none;
            border-radius: 4px;
            font-size: 15px;
            margin-right: 10px;
            cursor: pointer;
        }
        .btn-cancel:hover {
            background-color: #ccc;
        }
        .message {
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        .message-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .message-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .empty-message {
            text-align: center;
            color: #777;
            padding: 40px 0;
        }
        .logout-button {
            padding: 8px 16px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
        }
        .logout-button:hover {
            background-color: #c0392b;
        }
        /* 分页样式 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;
            padding: 10px 0;
            flex-wrap: wrap;
        }
        .page-link {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #4a90e2;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        .page-link:hover {
            background-color: #f5f5f5;
            border-color: #4a90e2;
        }
        .page-current {
            display: inline-block;
            padding: 8px 12px;
            margin: 0 5px;
            background-color: #4a90e2;
            color: white;
            border: 1px solid #4a90e2;
            border-radius: 4px;
        }
        .page-gap {
            margin: 0 5px;
            color: #777;
        }
        /* 用户列表头部样式 */
        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .export-button {
            padding: 8px 15px;
            background-color: #27ae60;
            color: white;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .export-button:hover {
            background-color: #219653;
        }
        /* 批量操作样式 */
        .bulk-actions {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9f9f9;
            border: 1px solid #eee;
            border-radius: 4px;
        }
        .bulk-input-group {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }
        .bulk-input-group label {
            margin-right: 10px;
            font-weight: 500;
        }
        .bulk-input-group input[type="number"] {
            width: 100px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            header {
                flex-direction: column;
                align-items: flex-start;
            }
            .user-stats {
                margin-top: 10px;
            }
            .results-table {
                font-size: 14px;
            }
            .results-table th, 
            .results-table td {
                padding: 8px 10px;
            }
            .action-buttons {
                flex-direction: column;
                gap: 5px;
            }
        }
        .user-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 8px 15px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s;
            border: none;
            cursor: pointer;
            display: inline-block;
        }
        
        .btn-primary {
            background-color: #4a90e2;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #3a75c4;
        }
        
        .btn-logout {
            background-color: #f44336;
            color: white;
        }
        
        .btn-logout:hover {
            background-color: #d32f2f;
        }
        
        .btn-message {
            background-color: #27ae60;
            color: white;
        }
        
        .btn-message:hover {
            background-color: #219653;
        }
        
        .operation-message {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        /* API密钥样式 */
        .api-key {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 2px 8px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
            display: inline-block;
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>管理后台</h1>
            <div class="user-actions">
                <a href="send_message.php" class="btn btn-primary">消息管理</a>
                <a href="logout.php" class="btn btn-logout">退出登录</a>
            </div>
        </header>
        
        <div class="user-stats">
            <?php if ($usersCount > 0): ?>
                总用户数: <?php echo $usersCount; ?>
            <?php endif; ?>
        </div>
        
        <!-- 操作消息 -->
        <?php if (!empty($operationMessage)): ?>
            <div class="operation-message success"><?php echo $operationMessage; ?></div>
        <?php endif; ?>
        
        <?php if (!empty($operationError)): ?>
            <div class="operation-message error"><?php echo $operationError; ?></div>
        <?php endif; ?>
        
        <!-- 用户查询部分 -->
        <section class="search-section">
            <h2>用户查询</h2>
            <form action="" method="GET" class="search-box">
                <input type="text" name="search" placeholder="输入用户ID、用户名或API密钥" value="<?php echo htmlspecialchars($searchTerm); ?>">
                <button type="submit" class="search-button">搜索</button>
            </form>
            
            <?php if (!empty($searchResults)): ?>
                <table class="results-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>API密钥</th>
                            <th>剩余次数</th>
                            <th>IP地址</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($searchResults as $user): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($user['id']); ?></td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><span class="api-key"><?php echo htmlspecialchars(formatApiKey($user['api_key'])); ?></span></td>
                                <td><?php echo htmlspecialchars($user['remaining_requests'] ?? '0'); ?></td>
                                <td><?php echo htmlspecialchars($user['register_ip'] ?? '未设置'); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-edit" onclick="showPasswordModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">修改密码</button>
                                        <button class="btn btn-edit" onclick="showApiKeyModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">修改密钥</button>
                                        <button class="btn btn-edit" onclick="showRemainingModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>', <?php echo htmlspecialchars($user['remaining_requests'] ?? '0'); ?>)">修改次数</button>
                                        <button class="btn btn-message" onclick="showDirectMessageModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">发送消息</button>
                                        <button class="btn btn-delete" onclick="showDeleteModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">删除用户</button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php elseif (isset($_GET['search'])): ?>
                <div class="empty-message">未找到匹配的用户</div>
            <?php endif; ?>
        </section>

        <!-- 用户列表部分 -->
        <section class="user-list-section">
            <div class="list-header">
                <h2>用户列表</h2>
                <a href="export_users.php" class="export-button">导出用户数据</a>
            </div>
            
            <?php
            // 分页配置
            $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
            $perPage = 10; // 每页显示10个用户
            $offset = ($page - 1) * $perPage;
            
            // 获取用户总数
            $countQuery = "SELECT COUNT(*) as total FROM users";
            $countResult = $conn->query($countQuery);
            $totalUsers = $countResult->fetch_assoc()['total'];
            $totalPages = ceil($totalUsers / $perPage);
            
            // 获取当前页的用户
            $allUsersQuery = "SELECT id, username, api_key, remaining_requests, register_ip FROM users ORDER BY id DESC LIMIT $offset, $perPage";
            $allUsersResult = $conn->query($allUsersQuery);
            $allUsers = [];
            
            if ($allUsersResult) {
                while ($row = $allUsersResult->fetch_assoc()) {
                    $allUsers[] = $row;
                }
            }
            ?>
            
            <?php if (!empty($allUsers)): ?>
                <table class="results-table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" id="selectAll"></th>
                            <th>ID</th>
                            <th>用户名</th>
                            <th>API密钥</th>
                            <th>剩余次数</th>
                            <th>IP地址</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($allUsers as $user): ?>
                            <tr>
                                <td><input type="checkbox" class="user-checkbox" name="user_id" value="<?php echo $user['id']; ?>"></td>
                                <td><?php echo htmlspecialchars($user['id']); ?></td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><span class="api-key"><?php echo htmlspecialchars(formatApiKey($user['api_key'])); ?></span></td>
                                <td><?php echo htmlspecialchars($user['remaining_requests'] ?? '0'); ?></td>
                                <td><?php echo htmlspecialchars($user['register_ip'] ?? '未设置'); ?></td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-edit" onclick="showPasswordModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">修改密码</button>
                                        <button class="btn btn-edit" onclick="showApiKeyModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">修改密钥</button>
                                        <button class="btn btn-edit" onclick="showRemainingModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>', <?php echo htmlspecialchars($user['remaining_requests'] ?? '0'); ?>)">修改次数</button>
                                        <button class="btn btn-message" onclick="showDirectMessageModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">发送消息</button>
                                        <button class="btn btn-delete" onclick="showDeleteModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">删除用户</button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                
                <!-- 批量操作 -->
                <div class="bulk-actions">
                    <form id="bulkForm" method="POST" action="">
                        <input type="hidden" name="action" value="bulk_update_remaining">
                        <div id="selectedUsersContainer"></div>
                        <div class="bulk-input-group">
                            <label for="bulkRemaining">批量设置剩余次数:</label>
                            <input type="number" id="bulkRemaining" name="bulk_remaining" min="0" value="100" required>
                            <button type="submit" id="bulkSubmit" class="btn-submit" disabled>应用到选中用户</button>
                        </div>
                    </form>
                </div>
                
                <!-- 分页导航 -->
                <?php if ($totalPages > 1): ?>
                <div class="pagination">
                    <?php 
                    // 构建查询字符串，保留搜索参数
                    $queryParams = [];
                    if (!empty($searchTerm)) {
                        $queryParams['search'] = urlencode($searchTerm);
                    }
                    
                    // 上一页链接
                    if ($page > 1): 
                        $queryParams['page'] = $page - 1;
                        $prevPageLink = '?' . http_build_query($queryParams);
                    ?>
                        <a href="<?php echo $prevPageLink; ?>" class="page-link">&laquo; 上一页</a>
                    <?php endif; ?>
                    
                    <?php
                    // 显示页码
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);
                    
                    if ($startPage > 1) {
                        $queryParams['page'] = 1;
                        $firstPageLink = '?' . http_build_query($queryParams);
                        echo '<a href="' . $firstPageLink . '" class="page-link">1</a>';
                        if ($startPage > 2) {
                            echo '<span class="page-gap">...</span>';
                        }
                    }
                    
                    for ($i = $startPage; $i <= $endPage; $i++) {
                        $queryParams['page'] = $i;
                        $pageLink = '?' . http_build_query($queryParams);
                        
                        if ($i == $page) {
                            echo '<span class="page-current">' . $i . '</span>';
                        } else {
                            echo '<a href="' . $pageLink . '" class="page-link">' . $i . '</a>';
                        }
                    }
                    
                    if ($endPage < $totalPages) {
                        if ($endPage < $totalPages - 1) {
                            echo '<span class="page-gap">...</span>';
                        }
                        $queryParams['page'] = $totalPages;
                        $lastPageLink = '?' . http_build_query($queryParams);
                        echo '<a href="' . $lastPageLink . '" class="page-link">' . $totalPages . '</a>';
                    }
                    
                    // 下一页链接
                    if ($page < $totalPages): 
                        $queryParams['page'] = $page + 1;
                        $nextPageLink = '?' . http_build_query($queryParams);
                    ?>
                        <a href="<?php echo $nextPageLink; ?>" class="page-link">下一页 &raquo;</a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="empty-message">暂无用户</div>
            <?php endif; ?>
        </section>
    </div>
    
    <!-- 修改密码模态框 -->
    <div id="passwordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">修改密码</h3>
                <button class="close-button" onclick="hideModal('passwordModal')">&times;</button>
            </div>
            <form id="passwordForm" method="POST" action="">
                <input type="hidden" name="action" value="update_password">
                <input type="hidden" name="user_id" id="passwordUserId">
                
                <div class="form-group">
                    <label>用户: <span id="passwordUsername"></span></label>
                </div>
                
                <div class="form-group">
                    <label for="newPassword">新密码</label>
                    <input type="password" id="newPassword" name="new_password" required>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn-cancel" onclick="hideModal('passwordModal')">取消</button>
                    <button type="submit" class="btn-submit">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 修改API密钥模态框 -->
    <div id="apiKeyModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">修改密钥</h3>
                <button class="close-button" onclick="hideModal('apiKeyModal')">&times;</button>
            </div>
            <form id="apiKeyForm" method="POST" action="">
                <input type="hidden" name="action" value="update_api_key">
                <input type="hidden" name="user_id" id="apiKeyUserId">
                
                <div class="form-group">
                    <label>用户: <span id="apiKeyUsername"></span></label>
                </div>
                
                <div class="form-group">
                    <label for="newApiKey">新API密钥</label>
                    <input type="text" id="newApiKey" name="new_api_key" required>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn-cancel" onclick="hideModal('apiKeyModal')">取消</button>
                    <button type="submit" class="btn-submit">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 修改剩余次数模态框 -->
    <div id="remainingModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">修改次数</h3>
                <button class="close-button" onclick="hideModal('remainingModal')">&times;</button>
            </div>
            <form id="remainingForm" method="POST" action="">
                <input type="hidden" name="action" value="update_remaining">
                <input type="hidden" name="user_id" id="remainingUserId">
                
                <div class="form-group">
                    <label>用户: <span id="remainingUsername"></span></label>
                </div>
                
                <div class="form-group">
                    <label for="newRemaining">新剩余次数</label>
                    <input type="number" id="newRemaining" name="new_remaining" min="0" required>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn-cancel" onclick="hideModal('remainingModal')">取消</button>
                    <button type="submit" class="btn-submit">保存</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 删除用户模态框 -->
    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">删除用户</h3>
                <button class="close-button" onclick="hideModal('deleteModal')">&times;</button>
            </div>
            <form id="deleteForm" method="POST" action="">
                <input type="hidden" name="action" value="delete_user">
                <input type="hidden" name="user_id" id="deleteUserId">
                
                <div class="form-group">
                    <label>您正在删除用户: <span id="deleteUsername"></span></label>
                    <p style="color: #e74c3c;">该操作不可逆，请谨慎操作。</p>
                </div>
                
                <div class="form-group">
                    <label for="adminPassword">请输入管理员密码确认</label>
                    <input type="password" id="adminPassword" name="admin_password" required>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn-cancel" onclick="hideModal('deleteModal')">取消</button>
                    <button type="submit" class="btn-submit" style="background-color: #e74c3c;">确认删除</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 发送消息模态框 -->
    <div id="directMessageModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">发送消息</h3>
                <button class="close-button" onclick="hideModal('directMessageModal')">&times;</button>
            </div>
            <form id="messageForm" method="POST" action="">
                <input type="hidden" name="action" value="send_message">
                <input type="hidden" name="recipient_id" id="messageRecipientId">
                <input type="hidden" name="recipient_username" id="messageRecipientUsername">
                
                <div class="form-group">
                    <label>发送给用户: <span id="messageUsername"></span></label>
                </div>
                
                <div class="form-group">
                    <label for="messageContent">消息内容</label>
                    <textarea id="messageContent" name="message_content" rows="6" placeholder="请输入要发送的消息" required></textarea>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn-cancel" onclick="hideModal('directMessageModal')">取消</button>
                    <button type="submit" class="btn-submit">发送消息</button>
                </div>
            </form>
        </div>
    </div>
    
    <!-- 消息通知窗口 -->
    <div class="message-modal" id="messageModal">
        <div class="message-content">
            <span class="close-btn" onclick="closeMessageModal()">&times;</span>
            <h3 class="message-title">系统消息</h3>
            <div class="message-body" id="messageBody"></div>
            <div class="message-time" id="messageTime"></div>
            <div class="message-actions">
                <button class="message-btn" onclick="markMessageAsRead()">已读</button>
            </div>
        </div>
    </div>
    
    <!-- 消息通知提示 -->
    <div class="message-notification" id="messageNotification" onclick="showMessageModal()">
        您有新的系统消息
    </div>

    <script>
        // 显示模态框
        function showPasswordModal(userId, username) {
            document.getElementById('passwordUserId').value = userId;
            document.getElementById('passwordUsername').textContent = username;
            document.getElementById('passwordModal').style.display = 'flex';
        }
        
        function showApiKeyModal(userId, username) {
            document.getElementById('apiKeyUserId').value = userId;
            document.getElementById('apiKeyUsername').textContent = username;
            document.getElementById('apiKeyModal').style.display = 'flex';
        }
        
        function showRemainingModal(userId, username, currentValue) {
            document.getElementById('remainingUserId').value = userId;
            document.getElementById('remainingUsername').textContent = username;
            document.getElementById('newRemaining').value = currentValue;
            document.getElementById('remainingModal').style.display = 'flex';
        }
        
        function showDeleteModal(userId, username) {
            document.getElementById('deleteUserId').value = userId;
            document.getElementById('deleteUsername').textContent = username;
            document.getElementById('deleteModal').style.display = 'flex';
        }
        
        function showDirectMessageModal(userId, username) {
            document.getElementById('messageRecipientId').value = userId;
            document.getElementById('messageRecipientUsername').value = username;
            document.getElementById('messageUsername').textContent = username + ' (ID: ' + userId + ')';
            document.getElementById('messageContent').value = '';
            document.getElementById('directMessageModal').style.display = 'flex';
        }
        
        // 隐藏模态框
        function hideModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        // 点击模态框外部关闭
        window.onclick = function(event) {
            var modals = document.getElementsByClassName('modal');
            for (var i = 0; i < modals.length; i++) {
                if (event.target == modals[i]) {
                    modals[i].style.display = 'none';
                }
            }
        }
        
        // 批量操作相关
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.getElementById('selectAll');
            const userCheckboxes = document.querySelectorAll('.user-checkbox');
            const bulkSubmitButton = document.getElementById('bulkSubmit');
            const selectedUsersContainer = document.getElementById('selectedUsersContainer');
            const bulkForm = document.getElementById('bulkForm');
            
            if (selectAllCheckbox) {
                selectAllCheckbox.addEventListener('change', function() {
                    userCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                    updateBulkFormState();
                });
            }
            
            userCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    updateBulkFormState();
                    
                    // 检查是否所有复选框都被选中
                    let allChecked = true;
                    userCheckboxes.forEach(cb => {
                        if (!cb.checked) allChecked = false;
                    });
                    
                    if (selectAllCheckbox) {
                        selectAllCheckbox.checked = allChecked;
                    }
                });
            });
            
            function updateBulkFormState() {
                // 清空已有的hidden输入
                selectedUsersContainer.innerHTML = '';
                
                // 获取所有选中的用户
                const selectedUsers = [];
                userCheckboxes.forEach(checkbox => {
                    if (checkbox.checked) {
                        selectedUsers.push(checkbox.value);
                        
                        // 为每个选中的用户创建hidden输入
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = 'selected_users[]';
                        hiddenInput.value = checkbox.value;
                        selectedUsersContainer.appendChild(hiddenInput);
                    }
                });
                
                // 禁用或启用提交按钮
                if (selectedUsers.length > 0) {
                    bulkSubmitButton.disabled = false;
                    bulkSubmitButton.textContent = `应用到选中的 ${selectedUsers.length} 个用户`;
                } else {
                    bulkSubmitButton.disabled = true;
                    bulkSubmitButton.textContent = '应用到选中用户';
                }
            }
        });
        
        // 消息相关变量
        let currentMessageId = null;
        let messageCheckInterval = null;
        
        // 页面加载后检查消息
        document.addEventListener('DOMContentLoaded', function() {
            // 添加消息样式
            const style = document.createElement('style');
            style.innerHTML = `
                .message-modal {
                    display: none;
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0,0,0,0.5);
                    z-index: 2000;
                    justify-content: center;
                    align-items: center;
                }
                
                .message-content {
                    background-color: #fff;
                    padding: 25px;
                    border-radius: 12px;
                    text-align: left;
                    position: relative;
                    width: 90%;
                    max-width: 500px;
                    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                }
                
                .message-content .close-btn {
                    position: absolute;
                    top: 10px;
                    right: 15px;
                    cursor: pointer;
                    font-size: 24px;
                    color: #888;
                    transition: color 0.3s;
                }
                
                .message-content .close-btn:hover {
                    color: #333;
                }
                
                .message-title {
                    margin-top: 0;
                    margin-bottom: 15px;
                    color: #4a90e2;
                    font-size: 20px;
                    border-bottom: 1px solid #eee;
                    padding-bottom: 10px;
                }
                
                .message-body {
                    font-size: 16px;
                    line-height: 1.5;
                    color: #333;
                    margin-bottom: 20px;
                    max-height: 300px;
                    overflow-y: auto;
                    white-space: pre-line;
                }
                
                .message-time {
                    font-size: 12px;
                    color: #888;
                    text-align: right;
                    margin-bottom: 15px;
                }
                
                .message-actions {
                    display: flex;
                    justify-content: flex-end;
                }
                
                .message-btn {
                    background-color: #4a90e2;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 8px 15px;
                    cursor: pointer;
                    transition: background-color 0.3s;
                }
                
                .message-btn:hover {
                    background-color: #3a75c4;
                }
                
                .message-notification {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background-color: #4a90e2;
                    color: white;
                    padding: 12px 20px;
                    border-radius: 8px;
                    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
                    z-index: 1500;
                    cursor: pointer;
                    display: none;
                    animation: slideIn 0.5s forwards;
                }
                
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
            
            // 设置定时检查新消息
            checkForNewMessages();
            messageCheckInterval = setInterval(checkForNewMessages, 60000); // 每分钟检查一次
        });
        
        // 检查新消息
        function checkForNewMessages() {
            fetch('api/message_api.php?action=get_messages')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络响应错误');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success && data.messages && data.messages.length > 0) {
                        // 显示最新的消息
                        const latestMessage = data.messages[0];
                        showMessageNotification();
                        currentMessageId = latestMessage.id;
                        
                        // 预加载消息内容
                        document.getElementById('messageBody').innerHTML = latestMessage.content;
                        document.getElementById('messageTime').innerText = '发送时间: ' + latestMessage.created_at;
                    }
                })
                .catch(error => {
                    console.error('获取消息出错:', error);
                });
        }
        
        // 显示消息通知提示
        function showMessageNotification() {
            const notification = document.getElementById('messageNotification');
            notification.style.display = 'block';
            
            // 10秒后自动隐藏通知
            setTimeout(() => {
                if (notification.style.display !== 'none') {
                    notification.style.display = 'none';
                }
            }, 10000);
        }
        
        // 显示消息弹窗
        function showMessageModal() {
            document.getElementById('messageNotification').style.display = 'none';
            document.getElementById('messageModal').style.display = 'flex';
        }
        
        // 关闭消息弹窗
        function closeMessageModal() {
            document.getElementById('messageModal').style.display = 'none';
        }
        
        // 标记消息为已读
        function markMessageAsRead() {
            if (!currentMessageId) return;
            
            fetch('api/message_api.php?action=mark_read', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    message_id: currentMessageId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeMessageModal();
                    currentMessageId = null;
                }
            })
            .catch(error => {
                console.error('标记消息出错:', error);
            });
        }
        
        // 点击模态框背景关闭
        document.getElementById('messageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeMessageModal();
            }
        });
    </script>
</body>
</html> 