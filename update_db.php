<?php
// 数据库更新脚本 - 添加用户注册IP字段

// 数据库连接配置
$db_host = 'localhost';
$db_name = 'agpt';
$db_user = 'agpt';
$db_pass = 'hunterl6628096';

// 连接数据库
$conn = new mysqli($db_host, $db_user, $db_pass, $db_name);

// 检查连接
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}
$conn->set_charset("utf8mb4");

// 检查register_ip字段是否存在
$checkColumn = $conn->query("SHOW COLUMNS FROM users LIKE 'register_ip'");
if ($checkColumn->num_rows == 0) {
    // 字段不存在，添加字段
    $alterTable = "ALTER TABLE users ADD COLUMN register_ip VARCHAR(45) DEFAULT '未知' AFTER password_hash";
    
    if ($conn->query($alterTable)) {
        echo "成功添加register_ip字段到users表！<br>";
    } else {
        echo "添加字段失败: " . $conn->error . "<br>";
    }
} else {
    echo "register_ip字段已存在，无需添加。<br>";
}

// 更新现有用户的IP为"未知"
$updateNullIPs = "UPDATE users SET register_ip = '未知' WHERE register_ip IS NULL";
if ($conn->query($updateNullIPs)) {
    echo "已将现有用户的空IP地址设置为默认值。<br>";
} else {
    echo "更新默认IP失败: " . $conn->error . "<br>";
}

echo "数据库更新完成！<br>";
echo "<a href='index.php'>返回首页</a>";

// 关闭连接
$conn->close();
?> 