<?php
/**
 * 股票数据云同步API
 * 支持将用户的前端存储数据上传到服务器或从服务器下载数据
 */

// 错误处理设置
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 确保所有错误都记录到日志
function exception_error_handler($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return;
    }
    error_log("PHP Error: $message in $file on line $line");
    throw new ErrorException($message, 0, $severity, $file, $line);
}
set_error_handler("exception_error_handler");

// 引入配置和函数
try {
    require_once '../includes/config.php';
    require_once '../includes/functions.php';
} catch (Exception $e) {
    error_log("引入文件错误: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => '服务器配置错误: ' . $e->getMessage()
    ]);
    exit;
}

// 设置响应头
header('Content-Type: application/json');

try {
    // 确保用户已登录
    if (!isLoggedIn()) {
        echo json_encode([
            'success' => false,
            'message' => '用户未登录'
        ]);
        exit;
    }

    // 获取当前用户ID
    $user_id = $_SESSION['user_id'] ?? 0;
    if (!$user_id) {
        throw new Exception("无效的用户ID");
    }

    // 判断请求类型
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    $data = null;
    
    if (empty($action) && $_SERVER['REQUEST_METHOD'] === 'POST') {
        // 获取POST请求中的JSON数据
        $json_data = file_get_contents('php://input');
        if (!$json_data) {
            throw new Exception("未收到POST数据");
        }
        
        error_log("收到的原始数据: " . $json_data);
        $data = json_decode($json_data, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("JSON解析错误: " . json_last_error_msg());
        }
        
        $action = isset($data['action']) ? $data['action'] : '';
    }

    // 记录请求信息
    error_log("云同步请求: 用户ID=$user_id, 操作=$action");

    // 根据操作类型执行不同的功能
    switch ($action) {
        case 'upload':
            // 处理数据上传请求
            uploadStocks($user_id, $data);
            break;
            
        case 'download':
            // 处理数据下载请求
            downloadStocks($user_id);
            break;
            
        default:
            // 未知操作
            echo json_encode([
                'success' => false,
                'message' => '未知操作类型: ' . $action
            ]);
            break;
    }
} catch (Exception $e) {
    error_log("云同步API错误: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => '服务器错误: ' . $e->getMessage()
    ]);
}

/**
 * 将用户的股票数据上传到服务器
 * 
 * @param int $user_id 用户ID
 * @param array $data 请求数据
 */
function uploadStocks($user_id, $data) {
    // 检查数据格式
    if (!isset($data['stocks']) || !is_array($data['stocks'])) {
        echo json_encode([
            'success' => false,
            'message' => '无效的股票数据格式'
        ]);
        return;
    }
    
    // 提取股票数据
    $stocks = $data['stocks'];
    error_log("股票数据上传: 用户ID=$user_id, 数据条数=" . count($stocks));
    
    try {
        // 将数据转换为JSON字符串
        $json_data = json_encode($stocks, JSON_UNESCAPED_UNICODE);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("编码股票数据失败: " . json_last_error_msg());
        }
        
        // 连接数据库
        $db = getDBConnection();
        if (!$db) {
            throw new Exception("无法连接到数据库");
        }
        
        // 检查数据库中是否存在表
        $tables_result = $db->query("SHOW TABLES LIKE 'user_cloud_data'");
        if (!$tables_result || $tables_result->rowCount() == 0) {
            // 表不存在，创建表
            error_log("user_cloud_data表不存在，创建表");
            $create_table_sql = "
            CREATE TABLE IF NOT EXISTS `user_cloud_data` (
              `id` int(11) NOT NULL AUTO_INCREMENT,
              `user_id` int(11) NOT NULL,
              `stock_data` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '股票数据JSON',
              `created_at` datetime NOT NULL,
              `updated_at` datetime NOT NULL,
              PRIMARY KEY (`id`),
              UNIQUE KEY `user_id` (`user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
            
            if (!$db->exec($create_table_sql)) {
                throw new Exception("创建表失败: " . implode(', ', $db->errorInfo()));
            }
        }
        
        // 检查是否已有记录
        $check_stmt = $db->prepare("SELECT id FROM user_cloud_data WHERE user_id = :user_id");
        if (!$check_stmt) {
            throw new Exception("预处理检查语句失败: " . implode(', ', $db->errorInfo()));
        }
        
        $check_stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $check_stmt->execute();
        
        if ($check_stmt->rowCount() > 0) {
            // 更新现有记录
            error_log("更新现有记录");
            $stmt = $db->prepare("UPDATE user_cloud_data SET stock_data = :stock_data, updated_at = NOW() WHERE user_id = :user_id");
            if (!$stmt) {
                throw new Exception("预处理更新语句失败: " . implode(', ', $db->errorInfo()));
            }
            $stmt->bindParam(':stock_data', $json_data, PDO::PARAM_STR);
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        } else {
            // 插入新记录
            error_log("插入新记录");
            $stmt = $db->prepare("INSERT INTO user_cloud_data (user_id, stock_data, created_at, updated_at) VALUES (:user_id, :stock_data, NOW(), NOW())");
            if (!$stmt) {
                throw new Exception("预处理插入语句失败: " . implode(', ', $db->errorInfo()));
            }
            $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
            $stmt->bindParam(':stock_data', $json_data, PDO::PARAM_STR);
        }
        
        // 执行SQL
        if ($stmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => '数据已成功上传到云端',
                'rows_affected' => $stmt->rowCount()
            ]);
            error_log("数据上传成功: 影响行数=" . $stmt->rowCount());
        } else {
            throw new Exception("数据库操作失败: " . implode(', ', $stmt->errorInfo()));
        }
        
    } catch (Exception $e) {
        error_log("上传股票数据错误: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => '服务器错误: ' . $e->getMessage()
        ]);
    }
}

/**
 * 从服务器下载用户的股票数据
 * 
 * @param int $user_id 用户ID
 */
function downloadStocks($user_id) {
    try {
        // 连接数据库
        $db = getDBConnection();
        if (!$db) {
            throw new Exception("无法连接到数据库");
        }
        
        // 检查表是否存在
        $tables_result = $db->query("SHOW TABLES LIKE 'user_cloud_data'");
        if (!$tables_result || $tables_result->rowCount() == 0) {
            // 表不存在
            echo json_encode([
                'success' => false,
                'message' => '未找到云端数据表'
            ]);
            return;
        }
        
        // 查询用户的云端数据
        $stmt = $db->prepare("SELECT stock_data, updated_at FROM user_cloud_data WHERE user_id = :user_id");
        if (!$stmt) {
            throw new Exception("预处理语句失败: " . implode(', ', $db->errorInfo()));
        }
        
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // 解码JSON数据
            $stock_data_json = $row['stock_data'];
            $stock_data = json_decode($stock_data_json, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("解码云端数据失败: " . json_last_error_msg());
            }
            
            $updated_at = $row['updated_at'];
            
            echo json_encode([
                'success' => true,
                'message' => '数据获取成功',
                'stocks' => $stock_data,
                'updated_at' => $updated_at
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => '未找到云端数据记录'
            ]);
        }
        
    } catch (Exception $e) {
        error_log("下载股票数据错误: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => '服务器错误: ' . $e->getMessage()
        ]);
    }
} 