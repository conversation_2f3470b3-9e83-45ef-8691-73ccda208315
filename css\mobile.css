/* 移动端响应式样式 */

/* 移动菜单样式 */
.mobile-menu-toggle {
    display: none;
    font-size: 24px;
    cursor: pointer;
    margin-left: 15px;
}

.mobile-menu {
    display: none;
    background-color: #fff;
    position: fixed;
    top: 0;
    left: -250px;
    width: 250px;
    height: 100%;
    z-index: 1000;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    transition: left 0.3s ease;
    overflow-y: auto;
    padding-top: 60px;
}

.mobile-menu.active {
    left: 0;
}

.mobile-menu-item {
    padding: 15px 20px;
    border-bottom: 1px solid #eaeaea;
}

.mobile-menu-item a {
    color: #333;
    text-decoration: none;
    font-size: 16px;
    display: block;
}

.mobile-menu-item:hover {
    background-color: #f8f8f8;
}

/* 为移动设备隐藏的内容 */
.mobile-hidden {
    display: none;
}

/* 通用响应式设置 */
@media (max-width: 992px) {
    .container {
        padding: 10px;
    }
    
    .auth-wrapper {
        flex-direction: column;
        gap: 20px;
    }
    
    .auth-features {
        padding: 15px;
        width: 100%;
    }
    
    .login-container,
    .register-container {
        width: 100%;
        flex: none;
        max-width: 100%;
        padding: 25px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .dashboard-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .main-content {
        width: 100%;
    }
    
    .stock-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    /* 显示移动端菜单图标 */
    .mobile-menu-toggle {
        display: inline-block;
    }
    
    /* 在移动端隐藏的内容 */
    .desktop-only {
        display: none;
    }
}

/* 平板设备 */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }

    main {
        padding-top: 10px;
    }

    .dashboard-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
    }

    .main-content {
        width: 100%;
    }

    .header-content {
        padding: 10px;
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-content h1 {
        font-size: 18px;
    }

    .user-info {
        font-size: 12px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .user-info a {
        padding: 3px 8px;
        font-size: 12px;
    }

    .section-title {
        font-size: 16px;
    }

    .sidebar section, .main-content section {
        margin-bottom: 15px;
        padding: 15px;
    }
    
    .auth-features h1 {
        font-size: 1.8rem;
        margin-bottom: 20px;
    }
    
    .feature-item {
        padding: 15px;
    }
    
    .search-section {
        margin-top: 10px;
    }
    
    .stock-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .stock-card {
        padding: 12px;
    }
    
    .stock-actions {
        flex-direction: column;
        gap: 8px;
    }
    
    .btn {
        width: 100%;
        padding: 8px;
    }
    
    .cloud-sync-buttons {
        width: 100%;
    }
    
    .cloud-button {
        width: 100%;
        margin-bottom: 5px;
    }
}

/* 手机设备 */
@media (max-width: 480px) {
    body.auth-page {
        padding: 15px 0;
    }
    
    .auth-features {
        display: none; /* 在非常小的屏幕上隐藏特性介绍 */
    }
    
    .auth-wrapper {
        gap: 10px;
    }
    
    .login-container,
    .register-container {
        padding: 20px 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
    }
    
    .form-title {
        font-size: 20px;
    }
    
    .form-subtitle {
        font-size: 13px;
        margin-bottom: 20px;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-control {
        padding: 10px;
        font-size: 14px;
    }
    
    .submit-btn {
        padding: 10px;
        font-size: 14px;
    }
    
    .stock-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .stock-price {
        font-size: 16px;
        margin-top: 5px;
    }
    
    .info-row {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .info-item {
        width: 100%;
        margin-bottom: 5px;
    }
    
    /* 图表和分析区域适配 */
    .chart-container {
        height: 200px;
    }
    
    /* 仪表盘页面顶部导航适配 */
    header h1 {
        font-size: 18px;
    }
    
    header h1 img {
        width: 24px;
        height: 24px;
    }
    
    /* 搜索区域适配 */
    .search-input {
        padding: 8px;
    }
    
    .search-button {
        padding: 8px 10px;
    }
    
    /* 存储模式区域 */
    .storage-mode-section {
        padding: 10px;
    }
    
    .toggle-label {
        font-size: 13px;
        min-width: auto;
    }
    
    .storage-desc {
        font-size: 12px;
        padding: 8px;
    }
    
    /* 云同步按钮 */
    .cloud-sync-buttons {
        flex-direction: column;
    }
    
    .cloud-button {
        width: 100%;
        padding: 8px 5px;
        font-size: 12px;
    }
    
    /* Agent分析区域 */
    .chat-container {
        max-height: 300px;
    }
    
    .message {
        padding: 8px;
    }
    
    .message-content {
        font-size: 13px;
    }
    
    .input-section input {
        padding: 8px;
        font-size: 13px;
    }
    
    .send-btn {
        padding: 8px;
        font-size: 13px;
    }
    
    /* 底部样式 */
    footer {
        font-size: 12px;
        text-align: center;
        padding: 10px 0;
    }
} 