:root {
  /* 主色调 - 专业金融蓝 */
  --primary-color: #2563eb;
  --primary-light: #3b82f6;
  --primary-dark: #1d4ed8;
  
  /* 辅助色调 */
  --success-color: #10b981;
  --danger-color: #ef4444;
  --warning-color: #f59e0b;
  --info-color: #3b82f6;
  
  /* 中性色调 */
  --neutral-50: #f9fafb;
  --neutral-100: #f3f4f6;
  --neutral-200: #e5e7eb;
  --neutral-300: #d1d5db;
  --neutral-400: #9ca3af;
  --neutral-500: #6b7280;
  --neutral-600: #4b5563;
  --neutral-700: #374151;
  --neutral-800: #1f2937;
  --neutral-900: #111827;
  
  /* 文字与背景 */
  --bg-color: #f9fafb;
  --card-bg: #ffffff;
  --text-primary: #1f2937;
  --text-secondary: #4b5563;
  --text-muted: #6b7280;
  
  /* 涨跌色 */
  --up-color: #ef4444;
  --down-color: #10b981;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  
  /* 边框 */
  --border-radius-sm: 0.25rem;  /* 4px */
  --border-radius: 0.375rem;    /* 6px */
  --border-radius-md: 0.5rem;   /* 8px */
  --border-radius-lg: 0.75rem;  /* 12px */
  --border-radius-xl: 1rem;     /* 16px */
  
  /* 动画 */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition: 200ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* 基础样式重置与通用样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-primary);
  line-height: 1.5;
  font-size: 0.875rem; /* 14px */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 容器布局 */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 1rem;
}

/* 顶部导航栏 */
header {
  background-color: var(--card-bg);
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 100;
  margin-bottom: 1.5rem;
  border-radius: 0 0 var(--border-radius-md) var(--border-radius-md);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.25rem;
}

header h1 {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

header h1 img {
  width: 2rem;
  height: 2rem;
  margin-right: 0.75rem;
  border-radius: 50%;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-info span {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.user-info a {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color var(--transition);
}

.user-info a:hover {
  background-color: var(--primary-dark);
}

/* 主体内容布局 */
.dashboard-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 1.5rem;
}

/* 卡片样式 */
.sidebar section,
.main-content section {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
  margin-bottom: 1.5rem;
  overflow: hidden;
  transition: transform var(--transition), box-shadow var(--transition);
}

.sidebar section:hover,
.main-content section:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--neutral-200);
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.section-title span {
  display: flex;
  align-items: center;
}

.section-title span i {
  margin-right: 0.5rem;
}

.section-title > div {
  margin-left: auto;
}

/* 搜索区域 */
.search-section {
  padding: 1.5rem;
}

.input-section {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.search-input {
  flex: 1;
  padding: 0.625rem 1rem;
  border: 1px solid var(--neutral-300);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  transition: border-color var(--transition), box-shadow var(--transition);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.search-button {
  padding: 0.625rem 1.25rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition);
}

.search-button:hover {
  background-color: var(--primary-dark);
}

/* 大盘指数 */
.market-index-section {
  padding: 1.5rem;
}

.market-indices {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.index-card {
  background-color: white;
  border-radius: var(--border-radius-md);
  padding: 1rem;
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition), box-shadow var(--transition);
}

.index-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

.index-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.index-price {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.index-change {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.index-change-item {
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
}

.price-up {
  color: var(--up-color);
  background-color: rgba(239, 68, 68, 0.1);
}

.price-down {
  color: var(--down-color);
  background-color: rgba(16, 185, 129, 0.1);
}

.index-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* 市场动态 */
.stock-changes-section {
  padding: 1rem;
}

.stock-changes-container {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.stock-changes-tabs {
  display: flex;
  background-color: var(--neutral-100);
  border-bottom: 1px solid var(--neutral-200);
}

.changes-tab {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition);
  position: relative;
}

.changes-tab:hover {
  color: var(--primary-color);
  background-color: rgba(37, 99, 235, 0.05);
}

.changes-tab.active {
  color: var(--primary-color);
  font-weight: 500;
}

.changes-tab.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--primary-color);
}

.stock-changes-marquee {
  height: 2.5rem;
  overflow: hidden;
  position: relative;
  background-color: white;
}

.stock-changes-content {
  white-space: nowrap;
  position: absolute;
  animation: marquee 180s linear infinite;
  display: flex;
  align-items: center;
  height: 100%;
}

.stock-changes-content:hover {
  animation-play-state: paused;
}

@keyframes marquee {
  0% { transform: translateX(0%); }
  100% { transform: translateX(-100%); }
}

.stock-change-item {
  display: inline-flex;
  align-items: center;
  margin-right: 2rem;
  font-size: 0.875rem;
}

.stock-change-item .time {
  color: var(--text-muted);
  font-size: 0.75rem;
  margin-right: 0.5rem;
}

.stock-change-item .code {
  color: var(--primary-color);
  font-weight: 500;
  margin-right: 0.5rem;
}

.stock-change-item .name {
  color: var(--text-primary);
  margin-right: 0.5rem;
}

.stock-change-item .info {
  color: var(--up-color);
  font-size: 0.75rem;
  font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 992px) {
  .dashboard-container {
    grid-template-columns: 1fr;
  }
  
  .sidebar {
    order: 2;
  }
  
  .main-content {
    order: 1;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0.75rem;
  }
  
  .header-content {
    flex-direction: column;
    padding: 0.75rem;
  }
  
  header h1 {
    margin-bottom: 0.5rem;
  }
  
  .user-info {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .market-indices {
    grid-template-columns: 1fr;
  }
  
  .search-section,
  .market-index-section {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .section-title {
    padding: 1rem;
    font-size: 1rem;
  }
  
  .index-card {
    padding: 0.75rem;
  }
  
  .changes-tab {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
} 